# CTS Project Structure

## Table of Contents
- [Overview](#overview)
- [Solution Structure](#solution-structure)
- [Project Breakdown](#project-breakdown)
- [Directory Organization](#directory-organization)
- [File Naming Conventions](#file-naming-conventions)
- [Component Relationships](#component-relationships)
- [Configuration Files](#configuration-files)
- [Quick Navigation Guide](#quick-navigation-guide)

## Overview

The CTS (Correspondence Tracking System) follows a well-organized project structure that separates concerns across multiple projects and maintains clear boundaries between different layers of the application.

### High-Level Architecture
```
CTS Solution
├── Intalio.CTS                    # Main web application (Presentation Layer)
├── Intalio.CTS.Core               # Business logic and data access (Core Layer)
├── Database/                      # SQL scripts and database changes
└── docs/                          # Documentation files
```

## Solution Structure

### Complete Project Tree
```
CTS/
├── Intalio.CTS/                           # Main Web Application
│   ├── Controllers/                       # Web API Controllers
│   ├── Views/                            # Razor Views
│   │   ├── Shared/                       # Shared views and layouts
│   │   │   └── _Translator.cshtml        # Translation resources
│   │   └── Home/                         # Home controller views
│   ├── wwwroot/                          # Static web assets
│   │   ├── components/                   # Backbone.js components
│   │   │   ├── appComponent.js           # Main application routing
│   │   │   └── {feature}Component.js     # Feature components
│   │   ├── js/                           # JavaScript files
│   │   │   ├── CTSCoreComponents.dev.js  # Core routing
│   │   │   └── {feature}.js              # Feature scripts
│   │   ├── templates/                    # Handlebars templates
│   │   │   ├── {feature}.handlebars      # Feature templates
│   │   │   └── shared/                   # Shared templates
│   │   ├── css/                          # Stylesheets
│   │   ├── images/                       # Images and icons
│   │   └── lib/                          # Third-party libraries
│   ├── Filters/                          # Custom action filters
│   ├── Models/                           # View models and DTOs
│   ├── Properties/                       # Assembly properties
│   ├── appsettings.json                  # Application configuration
│   ├── Program.cs                        # Application entry point
│   └── Startup.cs                        # Application startup configuration
│
├── Intalio.CTS.Core/                     # Core Business Logic
│   ├── API/                              # Manager classes (Business Layer)
│   │   ├── ManageDocument.cs             # Document business logic
│   │   ├── ManageTransfer.cs             # Transfer business logic
│   │   ├── ManageUser.cs                 # User business logic
│   │   └── Manage{Entity}.cs             # Other entity managers
│   ├── DAL/                              # Data Access Layer
│   │   ├── CTSContext.cs                 # Entity Framework context
│   │   ├── Document.cs                   # Document entity
│   │   ├── Transfer.cs                   # Transfer entity
│   │   ├── User.cs                       # User entity
│   │   └── {Entity}.cs                   # Other entities
│   ├── Model/                            # Data models and enums
│   │   ├── DocumentModel.cs              # Document models
│   │   ├── TransferModel.cs              # Transfer models
│   │   ├── Enums/                        # Enumeration types
│   │   └── DTOs/                         # Data transfer objects
│   ├── Utility/                          # Utility classes
│   │   ├── Extensions/                   # Extension methods
│   │   ├── Helpers/                      # Helper classes
│   │   └── Constants/                    # Application constants
│   └── Configuration/                    # Configuration classes
│
├── Database/                             # Database Scripts
│   ├── CTS 4.0.0.sql                    # Base schema script
│   ├── RC {number}.sql                   # Release candidate scripts
│   ├── {Developer}_{Feature}.sql         # Individual feature scripts
│   ├── CTS_Sprint{N}_Queries/            # Sprint-organized scripts
│   │   ├── Sprint {N} queries.sql        # Sprint summary scripts
│   │   └── {specific_feature}.sql        # Individual sprint features
│   ├── QDB/                              # QDB environment scripts
│   └── archived/                         # Archived/obsolete scripts
│
└── docs/                                 # Documentation
    ├── CTS-Architecture-Overview.md      # Architecture documentation
    ├── CTS-Development-Guidelines.md     # Development practices
    ├── CTS-Translation-System.md         # Translation system docs
    ├── CTS-Database-Management.md        # Database management docs
    ├── CTS-Frontend-Architecture.md      # Frontend architecture docs
    └── CTS-Project-Structure.md          # This file
```

## Project Breakdown

### Intalio.CTS (Main Web Application)

#### Purpose
The presentation layer that handles HTTP requests, user interface, and client-side functionality.

#### Key Responsibilities
- HTTP request/response handling
- User interface rendering
- Client-side routing and components
- Authentication and authorization
- Static asset serving

#### Technology Stack
- **ASP.NET Core MVC** for server-side framework
- **Backbone.js** for client-side MVC
- **Handlebars** for templating
- **Bootstrap** for responsive design
- **jQuery** for DOM manipulation

### Intalio.CTS.Core (Business Logic and Data Access)

#### Purpose
The core business logic and data access layer that implements the application's domain logic.

#### Key Responsibilities
- Business rule implementation
- Data access and persistence
- Entity relationship management
- Transaction coordination
- Utility functions and helpers

#### Technology Stack
- **Entity Framework Core** for data access
- **SQL Server** for database
- **.NET Core** for business logic

## Directory Organization

### Controllers Directory (`Intalio.CTS/Controllers/`)

```
Controllers/
├── BaseController.cs              # Base controller with common functionality
├── DocumentController.cs          # Document-related endpoints
├── TransferController.cs          # Transfer-related endpoints
├── UserController.cs              # User management endpoints
├── SearchController.cs            # Search functionality
├── ReportController.cs            # Reporting endpoints
├── AdminController.cs             # Administrative functions
└── {Feature}Controller.cs         # Other feature controllers
```

**Naming Convention**: `{Entity}Controller.cs`
**Pattern**: Each controller handles one primary entity or feature area

### Managers Directory (`Intalio.CTS.Core/API/`)

```
API/
├── ManageDocument.cs              # Document business logic
├── ManageTransfer.cs              # Transfer business logic
├── ManageUser.cs                  # User business logic
├── ManageCategory.cs              # Category business logic
├── ManageAttachment.cs            # Attachment business logic
├── ManageSearch.cs                # Search business logic
├── ManageReport.cs                # Report business logic
├── ManageWorkflow.cs              # Workflow business logic
└── Manage{Entity}.cs              # Other entity managers
```

**Naming Convention**: `Manage{Entity}.cs`
**Pattern**: Static classes with business logic methods

### Entities Directory (`Intalio.CTS.Core/DAL/`)

```
DAL/
├── CTSContext.cs                  # Entity Framework context
├── Document.cs                    # Document entity and data access
├── Transfer.cs                    # Transfer entity and data access
├── User.cs                        # User entity and data access
├── Category.cs                    # Category entity and data access
├── Attachment.cs                  # Attachment entity and data access
├── ActivityLog.cs                 # Activity log entity
├── TranslatorDictionary.cs        # Translation entity
└── {Entity}.cs                    # Other entities
```

**Naming Convention**: `{Entity}.cs`
**Pattern**: Partial classes implementing `IDbObject<T>` and `IDisposable`

### Frontend Components (`Intalio.CTS/wwwroot/`)

#### Components Directory
```
components/
├── appComponent.js                # Main application router
├── documentComponent.js           # Document management component
├── transferComponent.js           # Transfer management component
├── searchComponent.js             # Search component
├── inboxComponent.js              # Inbox component
├── draftComponent.js              # Draft component
└── {feature}Component.js          # Other feature components
```

#### Templates Directory
```
templates/
├── document.handlebars            # Document template
├── transfer.handlebars            # Transfer template
├── inbox.handlebars               # Inbox template
├── draft.handlebars               # Draft template
├── search.handlebars              # Search template
├── attachment.handlebars          # Attachment template
└── {feature}.handlebars           # Other feature templates
```

#### JavaScript Directory
```
js/
├── CTSCoreComponents.dev.js       # Core component routing
├── cts-core.js                    # Core application logic
├── cts-utilities.js               # Utility functions
├── lib/                           # Third-party libraries
│   ├── backbone.min.js
│   ├── handlebars.min.js
│   ├── jquery.min.js
│   └── bootstrap.min.js
└── {feature}.js                   # Feature-specific scripts
```

### Database Scripts (`Database/`)

#### Organization by Type
```
Database/
├── Base Schema/
│   └── CTS 4.0.0.sql             # Complete schema
├── Release Candidates/
│   ├── RC 30.sql                 # Latest release
│   ├── RC 29.sql                 # Previous release
│   └── RC {N}.sql                # Other releases
├── Individual Features/
│   ├── Ibrahim_AddFollowUp.sql   # Developer-specific features
│   ├── BashirHatoum_Translation.sql
│   └── {Developer}_{Feature}.sql
├── Sprint Collections/
│   ├── CTS_Sprint1_Queries/
│   ├── CTS_Sprint2_Queries/
│   └── CTS_Sprint{N}_Queries/
└── Environment Specific/
    ├── QDB/                      # QDB environment
    └── archived/                 # Obsolete scripts
```

## File Naming Conventions

### Backend Files

#### Controllers
```csharp
// Pattern: {Entity}Controller.cs
DocumentController.cs              // ✅ Correct
TransferController.cs              // ✅ Correct
UserManagementController.cs        // ✅ Correct

DocumentsController.cs             // ❌ Avoid plural
docController.cs                   // ❌ Wrong casing
```

#### Managers
```csharp
// Pattern: Manage{Entity}.cs
ManageDocument.cs                  // ✅ Correct
ManageTransfer.cs                  // ✅ Correct
ManageUser.cs                      // ✅ Correct

DocumentManager.cs                 // ❌ Different pattern
ManageDocuments.cs                 // ❌ Avoid plural
```

#### Entities
```csharp
// Pattern: {Entity}.cs (matches table name)
Document.cs                        // ✅ Correct
Transfer.cs                        // ✅ Correct
ActivityLog.cs                     // ✅ Correct

DocumentEntity.cs                  // ❌ Avoid suffix
Documents.cs                       // ❌ Avoid plural
```

### Frontend Files

#### Components
```javascript
// Pattern: {feature}Component.js
documentComponent.js               // ✅ Correct
transferComponent.js               // ✅ Correct
inboxComponent.js                  // ✅ Correct

DocumentComponent.js               // ❌ Wrong casing
document-component.js              // ❌ Wrong separator
```

#### Templates
```handlebars
// Pattern: {feature}.handlebars
document.handlebars                // ✅ Correct
transfer.handlebars                // ✅ Correct
inbox.handlebars                   // ✅ Correct

Document.handlebars                // ❌ Wrong casing
document.hbs                       // ❌ Wrong extension
```

### Database Scripts

#### Individual Features
```sql
-- Pattern: {Developer}_{Feature}.sql
Ibrahim_AddFollowUpFeature.sql     // ✅ Correct
BashirHatoum_TranslationFix.sql    // ✅ Correct
Maha_ExportEnhancements.sql        // ✅ Correct

AddFollowUp.sql                    // ❌ Missing developer
ibrahim_followup.sql               // ❌ Wrong casing
```

#### Translation Scripts
```sql
-- Pattern: {Developer}_{Feature}_Translation.sql
Ibrahim_FollowUp_Translation.sql   // ✅ Correct
BashirHatoumTranslationFix.sql     // ✅ Acceptable alternative

Translation_FollowUp.sql           // ❌ Wrong order
followup_translation.sql           // ❌ Wrong casing
```

## Component Relationships

### Data Flow Relationships
```
HTTP Request
     ↓
Controller (Intalio.CTS/Controllers/)
     ↓
Manager (Intalio.CTS.Core/API/)
     ↓
Entity (Intalio.CTS.Core/DAL/)
     ↓
Database (via CTSContext)
```

### Frontend Component Relationships
```
Browser URL
     ↓
Backbone Router (appComponent.js)
     ↓
Component (documentComponent.js)
     ↓
Template (document.handlebars)
     ↓
Rendered HTML
```

### Translation System Relationships
```
Database (TranslatorDictionary)
     ↓
Server Localizer (ASP.NET Core)
     ↓
_Translator.cshtml (Client Resources)
     ↓
JavaScript (window.Resources)
     ↓
Templates ({{Resources.Key}})
```

### Dependency Map

#### Project Dependencies
```
Intalio.CTS
    ├── References Intalio.CTS.Core
    ├── Uses Entity Framework Core
    ├── Uses ASP.NET Core MVC
    └── Serves static files from wwwroot/

Intalio.CTS.Core
    ├── Uses Entity Framework Core
    ├── Connects to SQL Server
    └── Independent of web layer

Database/
    ├── Independent SQL scripts
    └── Applied manually to database
```

#### Frontend Dependencies
```
Templates (.handlebars)
    ├── Depend on Resources (translations)
    ├── Depend on data models
    └── Compiled during build

Components (.js)
    ├── Depend on Backbone.js
    ├── Depend on templates
    ├── Depend on jQuery
    └── Use Resources for translations

Routing (appComponent.js)
    ├── Depends on Backbone.Router
    ├── Creates component instances
    └── Manages navigation
```

## Configuration Files

### Application Configuration (`appsettings.json`)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=...;Database=CTS;..."
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  },
  "CTS": {
    "Features": {
      "EnableG2G": true,
      "EnableFollowUp": true
    },
    "Security": {
      "RequireHttps": true
    }
  }
}
```

### Project Files

#### Main Web Project (`Intalio.CTS.csproj`)
```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Intalio.CTS.Core\Intalio.CTS.Core.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="5.0.0" />
  </ItemGroup>
</Project>
```

#### Core Project (`Intalio.CTS.Core.csproj`)
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>
</Project>
```

## Quick Navigation Guide

### Finding Specific Functionality

#### For Document Management:
- **Controller**: `Intalio.CTS/Controllers/DocumentController.cs`
- **Business Logic**: `Intalio.CTS.Core/API/ManageDocument.cs`
- **Data Access**: `Intalio.CTS.Core/DAL/Document.cs`
- **Frontend Component**: `Intalio.CTS/wwwroot/components/documentComponent.js`
- **Template**: `Intalio.CTS/wwwroot/templates/document.handlebars`

#### For User Management:
- **Controller**: `Intalio.CTS/Controllers/UserController.cs`
- **Business Logic**: `Intalio.CTS.Core/API/ManageUser.cs`
- **Data Access**: `Intalio.CTS.Core/DAL/User.cs`
- **Frontend Component**: `Intalio.CTS/wwwroot/components/userComponent.js`

#### For Database Changes:
- **Current Scripts**: `Database/{Developer}_{Feature}.sql`
- **Sprint Scripts**: `Database/CTS_Sprint{N}_Queries/`
- **Base Schema**: `Database/CTS 4.0.0.sql`

#### For Translations:
- **Client Resources**: `Intalio.CTS/Views/Shared/_Translator.cshtml`
- **Database Table**: `TranslatorDictionary`
- **SQL Scripts**: `Database/*Translation*.sql`

### Common File Patterns

```bash
# Find all controllers
find . -name "*Controller.cs" -path "*/Controllers/*"

# Find all managers
find . -name "Manage*.cs" -path "*/API/*"

# Find all entities
find . -name "*.cs" -path "*/DAL/*" ! -name "CTSContext.cs"

# Find all components
find . -name "*Component.js" -path "*/components/*"

# Find all templates
find . -name "*.handlebars" -path "*/templates/*"

# Find translation scripts
find . -name "*Translation*.sql" -path "*/Database/*"
```

### Directory Quick Reference

| What you need | Where to look |
|---------------|---------------|
| Web API endpoints | `Intalio.CTS/Controllers/` |
| Business logic | `Intalio.CTS.Core/API/` |
| Database entities | `Intalio.CTS.Core/DAL/` |
| Frontend components | `Intalio.CTS/wwwroot/components/` |
| UI templates | `Intalio.CTS/wwwroot/templates/` |
| Database scripts | `Database/` |
| Translations | `_Translator.cshtml` + `Database/*Translation*` |
| Configuration | `appsettings.json` |
| Documentation | `docs/` |
