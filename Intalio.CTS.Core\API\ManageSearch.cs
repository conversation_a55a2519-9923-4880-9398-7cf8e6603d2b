﻿using Elasticsearch.Net;
using ICSharpCode.SharpZipLib;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Nest;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.API
{
    public static class ManageSearch
    {
        #region Fields

        private static readonly List<string> keywordsList = new List<string> { "subject", "referencenumber" };

        #endregion

        #region Properties

        private static IElasticClient _client;

        #endregion

        #region Public Methods

        /// <summary>
        /// List searched documents
        /// </summary>
        /// <param name="model"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<FollowUpSearchListViewModel>)> SearchFollowUp(SearchModel model, int startIndex, int pageSize, long userId, long structureId , List<long> structureIds, short privacyLevel,
              List<SortExpression> sortExpression, Intalio.Core.Language language = Intalio.Core.Language.EN)
        {
            if (structureIds.Count < 1)
            {
                return (0, null);
            }

            ManageActivityLog.AddFullActivityLog(null, null, (int)ActivityLogs.Search, userId, JsonConvert.SerializeObject(model), "");
            var categoryIds = new List<short>();
            ExpressionBuilderFilters filter = new ExpressionBuilderFilters();

            if (model.Status != default)
            {
                filter.Add("FollowUpStatusId", model.Status, Intalio.Core.Operator.Equals);
            }
            if (model.Priority != default)
            {
                filter.Add("FollowUpDocument.PriorityId", model.Priority, Intalio.Core.Operator.Equals);
            }
            if (model.DocumentSender != default)
            {
                filter.Add("OriginalDocument.SendingEntityId", model.DocumentSender, Intalio.Core.Operator.Equals);

            }
            if (!string.IsNullOrEmpty(model.ReferenceNumber))
            {
                filter.Add("OriginalDocument.ReferenceNumber", model.ReferenceNumber, Intalio.Core.Operator.Contains);
            }

            if (!string.IsNullOrEmpty(model.FromDate))
            {
                DateTime from = DateTime.Parse(model.FromDate);
                filter.Add("FollowUpFromDate", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);
            }
            if (!string.IsNullOrEmpty(model.ToDate))
            {
                DateTime to = DateTime.Parse(model.ToDate);
                filter.Add("FollowUpToDate", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);
            }
            if (model.UserIds != default)
            {
                filter.Add("CreatedByUserId", model.UserIds, Intalio.Core.Operator.Equals);
            }
            //if (model.AssignedToId != default)
            //{
            //    filter.Add("Transfer[ToUserId]", model.AssignedToId, Intalio.Core.Operator.Equals);
            //}
           
            using (FollowUp item = new FollowUp())
            {
                var filterExp = ExpressionBuilder.GetExpression<FollowUp>(filter, ExpressionBuilderOperator.And);
                //List<SearchAssignedSecurity> searchAssignedStructures = new SearchAssignedSecurity().GetSearchAssignedSecurity(userId, structureIds);

                var countResult = await item.GetSearchCount(userId, structureId, filterExp, sortExpression.OrderByExpression<FollowUp>(), model.Keyword, model.Subject);
                var itemList = await item.ListSearchAsync(startIndex, pageSize, userId, structureId, filterExp, sortExpression.OrderByExpression<FollowUp>(), model.Keyword, model.Subject);
                return (countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.OriginalDocument.SendingEntity != null)
                    {
                        sendingEntity = t.OriginalDocument.SendingEntity.Name;
                        if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.OriginalDocument.SendingEntity.NameAr))
                        {
                            sendingEntity = t.OriginalDocument.SendingEntity.NameAr;
                        }
                        else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.OriginalDocument.SendingEntity.NameFr))
                        {
                            sendingEntity = t.OriginalDocument.SendingEntity.NameFr;
                        }
                    }
                    var isOverdue = false;

                    if (t.FollowUpToDate!=default)
                    {
                            isOverdue = DateTime.Now > t.FollowUpToDate; 
                    }
                    return new FollowUpSearchListViewModel
                    {
                        Id = t.Id,
                        IsHasNote = t.FollowUpDocument.Note.Any(),
                        FollowUpDocument = t.FollowUpDocument,
                        FollowUpDocumentId = t.DocumentId,
                        OriginalDocument = t.OriginalDocument,
                        OriginalDocumentId = t.OriginalDocument.Id,
                        Subject = t.FollowUpDocument.Subject,
                        IsOverDue = isOverdue,
                        CategoryId = t.OriginalDocument.CategoryId,
                        ReferenceNumber = t.OriginalDocument.ReferenceNumber,
                        StatusId = (int)t.FollowUpStatusId,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        PriorityId = t.FollowUpDocument.PriorityId,
                        PrivacyId = t.OriginalDocument.PrivacyId,
                        DueDate = t.FollowUpToDate!=default ? t.FollowUpToDate.ToString(Constants.DATE_FORMAT) : t.FollowUpDocument.DueDate.HasValue? t.FollowUpDocument.DueDate.Value.ToString(Constants.DATE_FORMAT):"",
                        CreatedByUser = language == Intalio.Core.Language.EN ?
                        $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
                        SendingEntity = sendingEntity,
                        ReceivingEntity = t.OriginalDocument.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, t.OriginalDocument.DocumentReceiverEntity.Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty,


                    };
                }).ToList());
            }
        }



        /// <summary>
        /// List searched documents
        /// </summary>
        /// <param name="model"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<SearchListViewModel>)> Search(SearchModel model, int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
              List<SortExpression> sortExpression,long? roleId = null, Intalio.Core.Language language = Intalio.Core.Language.EN)
        {
            if (structureIds.Count < 1)
            {
                return (0, null);
            }
            ManageActivityLog.AddFullActivityLog(null, null, (int)ActivityLogs.Search, userId, JsonConvert.SerializeObject(model), "");
            if (Configuration.CrawlerServerUrls.Count > 0 && model.SimpleSearch == null)
            {
                List<FormBuilderInputs> additionInputs = new List<FormBuilderInputs>();
                if (model.DocumentId != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "id", Value = model.DocumentId.ToString(), SearchSign = SearchSign.NotEqual, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.Category != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "categoryId", Value = model.Category.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.Status != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "statusId", Value = model.Status.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.Priority != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "priorityId", Value = model.Priority.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.Privacy != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "privacyId", Value = model.Privacy.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.DocumentSender != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "sendingEntityId", Value = model.DocumentSender.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (!string.IsNullOrEmpty(model.ReferenceNumber))
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "referenceNumber", Value = model.ReferenceNumber, SearchSign = SearchSign.Contains, Type = Intalio.Core.SearchType.String });
                }
                //if (!string.IsNullOrEmpty(model.Subject))
                //{
                //    additionInputs.Add(new FormBuilderInputs { Text = "subject", Value = model.Subject, SearchSign = SearchSign.Contains, Type = Intalio.Core.SearchType.String });
                //}
                if (!string.IsNullOrEmpty(model.Subject))
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "subjectSearch", Value = Helper.GetSearchString(model.Subject), SearchSign = SearchSign.Contains, Type = Intalio.Core.SearchType.String });
                }
                if (!string.IsNullOrEmpty(model.Note))
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "note", Value = Helper.GetSearchString(model.Note), SearchSign = SearchSign.Contains, Type = Intalio.Core.SearchType.String });
                }
                if (!string.IsNullOrEmpty(model.FromDate))
                {
                    DateTime from = DateTime.Parse(model.FromDate);
                    additionInputs.Add(new FormBuilderInputs { Text = "createdDate", Value = from.Date.ToString(), SearchSign = SearchSign.GreaterThanOrEqual, Type = Intalio.Core.SearchType.Date });
                }

                if (!string.IsNullOrEmpty(model.ToDate))
                {
                    DateTime to = DateTime.Parse(model.ToDate);
                    additionInputs.Add(new FormBuilderInputs { Text = "createdDate", Value = to.Date.ToString(), SearchSign = SearchSign.LessThanOrEqual, Type = Intalio.Core.SearchType.Date });
                }


                if (model.FromUser != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "transfers.fromUserId", Value = model.FromUser.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.ToUser != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "transfers.toUserId", Value = model.ToUser.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.FromStructure != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "transfers.fromStructureId", Value = model.FromStructure.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.ToStructure != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "transfers.toStructureId", Value = model.ToStructure.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (!string.IsNullOrEmpty(model.FromTransferDate))
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "transfers.createdDate", Value = DateTime.Parse(model.FromTransferDate).ToString(), SearchSign = SearchSign.GreaterThanOrEqual, Type = Intalio.Core.SearchType.Date });
                }
                if (!string.IsNullOrEmpty(model.ToTransferDate))
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "transfers.createdDate", Value = DateTime.Parse(model.ToTransferDate).ToString(), SearchSign = SearchSign.LessThanOrEqual, Type = Intalio.Core.SearchType.Date });
                }
                if (model.DocumentReceiver != default)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "documentReceiverEntity", Value = model.DocumentReceiver.ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (model.IsOverdue)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "dueDate", Value = DateTime.Now.ToString(), SearchSign = SearchSign.LessThan, Type = Intalio.Core.SearchType.Datetime });
                    additionInputs.Add(new FormBuilderInputs { Text = "statusId", Value = ((short)DocumentStatus.InProgress).ToString(), SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.Numeric });
                }
                if (!string.IsNullOrEmpty(model.Keyword))
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "keyword", Value = model.Keyword, SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.String });
                }

                if (!string.IsNullOrEmpty(model.OcrContent) && Configuration.EnableOCR)
                {
                    additionInputs.Add(new FormBuilderInputs { Text = "ocrContent", Value = model.OcrContent, SearchSign = SearchSign.Equal, Type = Intalio.Core.SearchType.String });
                }

                var advanceSearchModel = new AdvanceSearchModel();
                advanceSearchModel.DelegationId = model.DelegationId;
                advanceSearchModel.CategoryId = (short?)model.Category;
                return ManageSearch.AdvanceSearch(advanceSearchModel, additionInputs, startIndex, pageSize, userId, structureIds, isStructureReceiver, privacyLevel, sortExpression, language);
            }
            var delegation = model.DelegationId != default ? ManageDelegation.GetByDelegationId(userId, model.DelegationId.Value) : null;
            var categoryIds = new List<short>();
            ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
            List<short> categorySearchSecurityIds = new CategorySearchSecurity().ListByRoleId(roleId.Value).Select(c => c.CategoryId).ToList();
            List<short> allCategoriesIds = new Category().ListSecurity().Select(c => c.Id).ToList();
            List<short> excludedCategoryForSearch = allCategoriesIds.Except(categorySearchSecurityIds).ToList();
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("Transfer[CreatedDate]", delegation.StartDate, Intalio.Core.Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("Transfer[CreatedDate]", delegation.FromDate, Intalio.Core.Operator.GreaterThanOrEqual);

                filter.Add("Transfer[CreatedDate]", delegation.ToDate, Intalio.Core.Operator.LessThanOrEqualTo);
                filter.Add("Privacy.Level", privacyLevel, Intalio.Core.Operator.LessThanOrEqualTo);
            }
            ExpressionBuilderFilters categoryFilter = new ExpressionBuilderFilters();
            ExpressionBuilderFilters categoryIgnoreFilter = new ExpressionBuilderFilters();
            ExpressionBuilderFilters simpleSearchFilter = new ExpressionBuilderFilters();
            if (model.DocumentId != default)
            {
                filter.Add("Id", model.DocumentId, Intalio.Core.Operator.NotEqual);
            }
            if (!model.IgnoredCategories.IsNullOrEmpty())
            {
                foreach (var category in model.IgnoredCategories)
                {
                    categoryIgnoreFilter.Add("CategoryId", category, Intalio.Core.Operator.NotEqual);
                }
            }
            if(excludedCategoryForSearch.Count > 0)
            {
                foreach (var item in excludedCategoryForSearch)
                {
                    categoryIgnoreFilter.Add("CategoryId", item, Intalio.Core.Operator.NotEqual);
                }
            }
            if (model.Category != default)
            {
                filter.Add("CategoryId", model.Category, Intalio.Core.Operator.Equals);
            }

            if (!categoryIds.IsNullOrEmpty())
            {
                foreach (var category in categoryIds)
                {
                    categoryFilter.Add("CategoryId", category, Intalio.Core.Operator.Equals);
                }
            }

            if (model.Status != default)
            {
                filter.Add("StatusId", model.Status, Intalio.Core.Operator.Equals);
            }
            if (model.Priority != default)
            {
                filter.Add("PriorityId", model.Priority, Intalio.Core.Operator.Equals);
            }
            if (model.Privacy != default)
            {
                filter.Add("PrivacyId", model.Privacy, Intalio.Core.Operator.Equals);
            }
            if (model.DocumentSender != default)
            {
                filter.Add("SendingEntityId", model.DocumentSender, Intalio.Core.Operator.Equals);
            }
            if (!string.IsNullOrEmpty(model.ReferenceNumber))
            {
                filter.Add("ReferenceNumber", model.ReferenceNumber, Intalio.Core.Operator.Contains);
            }
            //TODO: MAHA: 
            if (!string.IsNullOrEmpty(model.SimpleSearch))
            {
                //simpleSearchFilter.Add("Subject", model.SimpleSearch, Intalio.Core.Operator.Contains);
                simpleSearchFilter.Add("SubjectSearch", Helper.GetSearchString(model.SimpleSearch), Intalio.Core.Operator.Contains);
                simpleSearchFilter.Add("ReferenceNumber", model.SimpleSearch, Intalio.Core.Operator.Contains);
            }
            //if (!string.IsNullOrEmpty(model.Subject))
            //{
            //    filter.Add("Subject", model.Subject, Intalio.Core.Operator.Contains);
            //}
            if (!string.IsNullOrEmpty(model.Subject))
            {
                filter.Add("SubjectSearch", Helper.GetSearchString(model.Subject), Intalio.Core.Operator.Contains);
            }
            if (!string.IsNullOrEmpty(model.Note))
            {
                filter.Add("Note[Notes]", Helper.GetSearchString(model.Note), Intalio.Core.Operator.Contains);
            }
            if (!string.IsNullOrEmpty(model.FromDate))
            {
                DateTime from = DateTime.Parse(model.FromDate);
                if (model.IsFollowUp == true)
                {
                    filter.Add("DocumentDate", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);
                }
                else
                {
                    filter.Add("CreatedDate.Date", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);
                }
            }
            if (!string.IsNullOrEmpty(model.Year))
            {
                //DateTime year = DateTime.Parse(model.Year);
                int year = int.Parse(model.Year);
                if (model.IsFollowUp == true)
                {
                    filter.Add("DocumentDate", year, Intalio.Core.Operator.Equals);
                }
                else
                {
                    filter.Add("CreatedDate.Date.Year", year, Intalio.Core.Operator.Equals);
                }
            }
            if (!string.IsNullOrEmpty(model.ExportedDate))
            {

                DateTime exportedDate = DateTime.Parse(model.ExportedDate);
                DateTime dateWithMaxTime = exportedDate.Date.Add(new TimeSpan(23, 59, 59)).AddMilliseconds(999);

                filter.Add("Transfer[ExportedDate]", exportedDate, Intalio.Core.Operator.GreaterThanOrEqual); // 23-06-2025  000000000   
                filter.Add("Transfer[ExportedDate]", dateWithMaxTime, Intalio.Core.Operator.LessThanOrEqualTo); // 23-06-2025   23:59:59



            }
            if (!string.IsNullOrEmpty(model.ToDate))
            {
                if (model.IsFollowUp == true)
                {
                    DateTime to = DateTime.Parse(model.ToDate);
                    filter.Add("DueDate", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);
                    //filter.Add("StatusId", (short)DocumentStatus.InProgress, Intalio.Core.Operator.Equals);
                }
                else
                {
                    DateTime to = DateTime.Parse(model.ToDate);
                    filter.Add("CreatedDate.Date", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);
                }
            }
            if (!string.IsNullOrEmpty(model.DocumentDate))
            {
                DateTime documentDate = DateTime.Parse(model.DocumentDate);
                filter.Add("DocumentDate", documentDate, Intalio.Core.Operator.Equals);  // إضافة شرط البحث لـ DocumentDate
            }

            if (model.FromUser != default)
            {
                filter.Add("Transfer[FromUserId]", model.FromUser, Intalio.Core.Operator.Equals);
            }
            if (model.ToUser != default)
            {
                filter.Add("Transfer[ToUserId]", model.ToUser, Intalio.Core.Operator.Equals);
            }
            if (model.CreatedBy != default)
            {
                filter.Add("CreatedByUserId", model.CreatedBy, Intalio.Core.Operator.Equals);
            }
            if (model.FromStructure != default)
            {
                filter.Add("Transfer[FromStructureId]", model.FromStructure, Intalio.Core.Operator.Equals);
            }
            if (model.ToStructure != default)
            {
                filter.Add("Transfer[ToStructureId]", model.ToStructure, Intalio.Core.Operator.Equals);
            }
            if (!string.IsNullOrEmpty(model.FromTransferDate))
            {
                filter.Add("Transfer[CreatedDate]", DateTime.Parse(model.FromTransferDate), Intalio.Core.Operator.GreaterThanOrEqual);
            }
            if (!string.IsNullOrEmpty(model.ToTransferDate))
            {
                filter.Add("Transfer[CreatedDate]", DateTime.Parse(model.ToTransferDate), Intalio.Core.Operator.LessThanOrEqualTo);
            }
            if (model.DocumentReceiver != default)
            {
                filter.Add("DocumentReceiverEntity[StructureId]", model.DocumentReceiver, Intalio.Core.Operator.Equals);
            }
            if (model.IsOverdue)
            {
                filter.Add("DueDate", DateTime.Now, Intalio.Core.Operator.LessThan);
                filter.Add("StatusId", (short)DocumentStatus.InProgress, Intalio.Core.Operator.Equals);
            }
            if (model.IsFollowUp == true)
            {
                if (!string.IsNullOrEmpty(model.Instructions))
                {
                    filter.Add("DocumentForm.Body", model.Instructions, Intalio.Core.Operator.Contains);
                }
                if (model.AssignedToId != default)
                {
                    filter.Add("Transfer[ToUserId]", model.AssignedToId, Intalio.Core.Operator.Equals);
                }
            }
            //if ((model.UserIds != default))
            //{
            //    filter.Add("CreatedByUserId", model.UserIds, Intalio.Core.Operator.Equals);

            //}

            using (Document item = new Document())
            {
                var catFilterExp = ExpressionBuilder.GetExpression<Document>(categoryFilter, ExpressionBuilderOperator.Or);
                var categoryIgnoreExpression = ExpressionBuilder.GetExpression<Document>(categoryIgnoreFilter, ExpressionBuilderOperator.And);
                var simplSearchFilterExp = ExpressionBuilder.GetExpression<Document>(simpleSearchFilter, ExpressionBuilderOperator.Or);
                var filterExp = ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And);
                //var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
                List<SearchAssignedSecurity> searchAssignedStructures = new SearchAssignedSecurity().GetSearchAssignedSecurity(userId, structureIds);

                bool useAllStructures = true;
                if (Core.Configuration.EnablePerStructure)
                    useAllStructures = false;
                var countResult = await item.GetSearchCount(userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures, Configuration.SearchAssignedStructureSearchUsersDocuments, useAllStructures, filterExp, catFilterExp, simplSearchFilterExp, model.Keyword, categoryIgnoreExpression);
                var itemList = await item.ListSearchAsync(startIndex, pageSize, userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures, Configuration.SearchAssignedStructureSearchUsersDocuments, useAllStructures, filterExp, catFilterExp, simplSearchFilterExp, sortExpression.OrderByExpression<Document>(), model.Keyword, null, categoryIgnoreExpression);
                return (countResult, itemList.Select(t =>
                {
                    var sendingEntity = string.Empty;
                    if (t.SendingEntity != null)
                    {
                        sendingEntity = t.SendingEntity.Name;
                        if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
                        {
                            sendingEntity = t.SendingEntity.NameAr;
                        }
                        else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.SendingEntity.NameFr))
                        {
                            sendingEntity = t.SendingEntity.NameFr;
                        }
                    }
                    var isOverdue = false;

                    if (t.DueDate.HasValue)
                    {

                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now > t.DueDate.Value;
                        }
                    }
                    return new SearchListViewModel
                    {
                        Id = t.Id,
                        Subject = t.Subject,
                        IsOverDue = isOverdue,

                        CategoryId = t.CategoryId,
                        ReferenceNumber = t.ReferenceNumber,
                        StatusId = t.StatusId,
                        CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        PriorityId = t.PriorityId,
                        PrivacyId = t.PrivacyId,
                        DueDate = t.DueDate.HasValue ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : "",
                        DocumentDate = t.DocumentDate.HasValue ? t.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : "",  // عرض DocumentDate هنا

                        ImportanceId = t.ImportanceId,
                        CreatedByUser = language == Intalio.Core.Language.EN ?
                        $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
                        SendingEntity = sendingEntity,
                        ReceivingEntity = t.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, t.DocumentReceiverEntity.Select(t =>
                        {
                            var receiverName = string.Empty;
                            if (t.Structure != null)
                            {
                                receiverName = t.Structure.Name;
                                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    receiverName = t.Structure.NameAr;
                                }
                                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    receiverName = t.Structure.NameFr;
                                }
                            }
                            else if (t.EntityGroup != null)
                            {
                                receiverName = t.EntityGroup.Name;
                                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                {
                                    receiverName = t.EntityGroup.NameAr;
                                }
                                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                {
                                    receiverName = t.EntityGroup.NameFr;
                                }
                            }
                            return receiverName;
                        }).ToList()) : string.Empty,
                        IsSigned = (t.IsSigned ?? false) || t.Transfer.Any(tr => tr.IsSigned)

                    };
                }).ToList());
            }
        }
        /// <summary>
        /// List searched documents
        /// </summary>
        /// <remarks>
        /// SearchComparisonOperator must be added in search control Custom Properties.<br/><br/>
        /// Search comparison operator values:<br/>Like ,Equal ,LessThanOrEqual ,LessThan ,GreaterThanOrEqual ,GreaterThan ,NotEqual ,Contains<br/><br/>
        /// The default value is related to the control type. The default value: <br/>
        /// <li><b>Equal</b> is used for controls: <i>checkbox</i>, <i>number</i>, <i>datetime</i>, <i>time</i></li>
        /// <li><b>Like</b> is used for controls: <i>textfield</i>, <i>textarea</i>, <i>select</i>, <i>radio</i>, <i>tags</i> and<i>"other controls"</i></li>
        /// </remarks>
        /// <param name="model"></param>
        /// <param name="additionInputs"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static (int, List<SearchListViewModel>) AdvanceSearch(AdvanceSearchModel model, List<FormBuilderInputs> additionInputs, int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
              List<SortExpression> sortExpression, Intalio.Core.Language language = Intalio.Core.Language.EN,string simpleSearch = null)
        {
            var delegation = model.DelegationId != default ? ManageDelegation.GetByDelegationId(userId, model.DelegationId.Value) : null;
            var categoryIds = new List<short>();
            var inputs = new List<FormBuilderInputs>();
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    inputs.Add(new FormBuilderInputs
                    {
                        Text = "CreatedDate",
                        Value = delegation.StartDate.ToString(),
                        Type = Intalio.Core.SearchType.Datetime,
                        SearchSign = SearchSign.GreaterThanOrEqual
                    });
                else if (!delegation.ShowOldCorespondence)
                    inputs.Add(new FormBuilderInputs
                    {
                        Text = "CreatedDate",
                        Value = delegation.FromDate.ToString(),
                        Type = Intalio.Core.SearchType.Datetime,
                        SearchSign = SearchSign.GreaterThanOrEqual
                    });

                inputs.Add(new FormBuilderInputs
                {
                    Text = "CreatedDate",
                    Value = delegation.ToDate.ToString(),
                    Type = Intalio.Core.SearchType.Datetime,
                    SearchSign = SearchSign.LessThanOrEqual
                });
            }

            List<QueryContainer> mustQuery = new List<QueryContainer>();
            List<QueryContainer> mustNotQuery = new List<QueryContainer>();

            List<QueryContainer> mustQuerySimpleSearch = new List<QueryContainer>();
            List<QueryContainer> mustNotQuerySimpleSearch= new List<QueryContainer>();

            NestedQuery nestedQuery = new NestedQuery();
            nestedQuery.Path = "transfers";

            List<QueryContainer> nestedMustQuery = new List<QueryContainer>();
            List<QueryContainer> nestedMustNotQuery = new List<QueryContainer>();

            if (model.CategoryId != default && !additionInputs.Any(p => p.Text == "categoryId"))
            {
                var retValue = ManageCategory.GetSearchAttribute(model.CategoryId.Value);
                inputs = new FormBuilderUtility().GetFormBuilderInputs(retValue.Attribute, model.CategoryFormData);
                for (int i = 0; i < inputs.Count; i++)
                {
                    if (!inputs[i].Text.ToLower().StartsWith("form."))
                    {
                        inputs[i].Text = "form." + inputs[i].Text;
                    }
                }
                inputs.Add(new FormBuilderInputs
                {
                    Text = "categoryId",
                    Value = model.CategoryId.ToString(),
                    Type = Intalio.Core.SearchType.Numeric,
                    SearchSign = SearchSign.Equal
                });
            }

            if (!categoryIds.IsNullOrEmpty())
            {
                mustQuery.Add(new TermsQuery()
                {
                    Field = "categoryId",
                    Terms = categoryIds.Select(c => (object)c)
                });
            }

            var configuration = ManageAdvanceSearchConfiguration.GetConfiguration();
            if (!string.IsNullOrEmpty(configuration.Content))
            {
                inputs.AddRange(new FormBuilderUtility().GetFormBuilderInputs(configuration.Content, model.FormData));
            }
            if (additionInputs != null && additionInputs.Any())
            {
                inputs.AddRange(additionInputs);
            }
            foreach (var input in inputs)
            {
                input.Text = input.Text.Substring(0, 1).ToLower() + input.Text.Substring(1);
                if (input.Text.Contains('.'))
                {
                    var terms = input.Text.Split('.');
                    input.Text = terms[0].Substring(0, 1).ToLower() + terms[0].Substring(1);
                    for (int i = 1; i < terms.Length; i++)
                    {
                        input.Text = input.Text + "." + terms[i].Substring(0, 1).ToLower() + terms[i].Substring(1);
                    }
                }
                switch (input.SearchSign)
                {
                    case SearchSign.Contains:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                            case Intalio.Core.SearchType.Date:
                            case Intalio.Core.SearchType.Numeric:
                                break;
                            default:
                                if (!input.Text.StartsWith("form."))
                                {
                                    if (input.Text.StartsWith("transfers."))
                                    {
                                        nestedMustQuery.Add(new WildcardQuery()
                                        {
                                            Field = input.Text,
                                            Value = "*" + input.Value + "*",
                                            CaseInsensitive = true
                                        });
                                    }
                                    else
                                    {
                                        mustQuery.Add(new WildcardQuery()
                                        {
                                            Field = keywordsList.Contains(input.Text.ToLower()) ? input.Text + ".keyword" : input.Text,
                                            Value = "*" + input.Value + "*",
                                            CaseInsensitive = true
                                            
                                        });
                                    }
                                }
                                break;
                        }
                        break;
                    case SearchSign.Like:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                            case Intalio.Core.SearchType.Date:
                            case Intalio.Core.SearchType.Numeric:
                                break;
                            default:
                                if (input.Text.StartsWith("form."))
                                {
                                    mustQuery.Add(new PrefixQuery()
                                    {
                                        Field = input.Text,
                                        Value = input.Value,
                                        CaseInsensitive = true
                                    });
                                }
                                else
                                {
                                    if (input.Text.StartsWith("transfers."))
                                    {
                                        nestedMustQuery.Add(new WildcardQuery()
                                        {
                                            Field = input.Text,
                                            Value = input.Value + "*",
                                            CaseInsensitive = true
                                        });
                                    }
                                    else
                                    {
                                        mustQuery.Add(new WildcardQuery()
                                        {
                                            Field = input.Text,
                                            Value = input.Value + "*",
                                            CaseInsensitive = true
                                        });
                                    }
                                }
                                break;
                        }
                        break;
                    case SearchSign.Equal:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Date:
                                if (input.Text.StartsWith("form."))
                                {
                                    DateTime value = DateTime.Parse(input.Value);
                                    var script = string.Format("if (doc['{0}'].size() > 0) { ZonedDateTime dt = ZonedDateTime.parse(doc['{0}'].value); return dt.getYear() == {1} && dt.getMonth().getValue() == {2} && dt.getDayOfMonth() == {3};} else {return false;}", input.Text, value.Year, value.Month, value.Day);
                                    mustQuery.Add(new ScriptQuery()
                                    {
                                        Script = new InlineScript(script)
                                    });
                                }
                                else
                                {
                                    if (input.Text.StartsWith("transfers."))
                                    {
                                        nestedMustQuery.Add(new MatchQuery()
                                        {
                                            Field = input.Text,
                                            Query = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        });
                                    }
                                    else
                                    {
                                        mustQuery.Add(new MatchQuery()
                                        {
                                            Field = input.Text,
                                            Query = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        });
                                    }
                                }
                                break;
                            case Intalio.Core.SearchType.Numeric:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new TermQuery()
                                    {
                                        Field = input.Text,
                                        Value = input.Value
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new TermQuery()
                                    {
                                        Field = input.Text,
                                        Value = input.Value
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Boolean:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = input.Value.ToLower()
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = input.Value.ToLower()
                                    });
                                }
                                break;
                            default:
                                if (input.Text == "keyword")
                                {
                                    mustQuery.Add(new WildcardQuery()
                                    {
                                        Field = input.Text,
                                        Value = input.Value,
                                        CaseInsensitive = true
                                    });
                                }
                                else
                                {
                                    if (input.Text.StartsWith("transfers."))
                                    {
                                        nestedMustQuery.Add(new MatchQuery()
                                        {
                                            Field = input.Text,
                                            Query = input.Value
                                        });
                                    }
                                    else
                                    {
                                        mustQuery.Add(new MatchQuery()
                                        {
                                            Field = input.Text,
                                            Query = input.Value
                                        });
                                    }
                                }
                                break;
                        }
                        break;
                    case SearchSign.GreaterThanOrEqual:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThanOrEqualTo = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThanOrEqualTo = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Date:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThanOrEqualTo = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThanOrEqualTo = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Numeric:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThanOrEqualTo = Convert.ToInt64(input.Value),
                                        LessThan = Int64.MaxValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThanOrEqualTo = Convert.ToInt64(input.Value),
                                        LessThan = Int64.MaxValue
                                    });
                                }
                                break;
                            default: break;
                        }
                        break;
                    case SearchSign.GreaterThan:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThan = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThan = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Date:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThan = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThan = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        LessThan = DateTime.MaxValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Numeric:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThan = Convert.ToInt64(input.Value),
                                        LessThan = Int64.MaxValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        GreaterThan = Convert.ToInt64(input.Value),
                                        LessThan = Int64.MaxValue
                                    });
                                }
                                break;
                            default: break;
                        }
                        break;
                    case SearchSign.LessThanOrEqual:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThanOrEqualTo = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThanOrEqualTo = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Date:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThanOrEqualTo = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThanOrEqualTo = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Numeric:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThanOrEqualTo = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThanOrEqualTo = Convert.ToInt64(input.Value),
                                        GreaterThan = Int64.MinValue
                                    });
                                }
                                break;
                            default: break;
                        }
                        break;
                    case SearchSign.LessThan:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThan = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThan = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Date:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThan = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new DateRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThan = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                        GreaterThan = DateTime.MinValue
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Numeric:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThan = Convert.ToInt64(input.Value),
                                        GreaterThan = Int64.MinValue
                                    });
                                }
                                else
                                {
                                    mustQuery.Add(new LongRangeQuery()
                                    {
                                        Field = input.Text,
                                        LessThan = Convert.ToInt64(input.Value),
                                        GreaterThan = Int64.MinValue
                                    });
                                }
                                break;
                            default: break;
                        }
                        break;
                    case SearchSign.NotEqual:
                        switch (input.Type)
                        {
                            case Intalio.Core.SearchType.Datetime:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustNotQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                    });
                                }
                                else
                                {
                                    mustNotQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = DateTime.Parse(input.Value).ToString(Constant.DATETIME_ZONE),
                                    });
                                }
                                break;
                            case Intalio.Core.SearchType.Date:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustNotQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                    });
                                }
                                else
                                {
                                    mustNotQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = DateTime.Parse(input.Value).ToString("yyyy-MM-dd"),
                                    });
                                }
                                break;
                            default:
                                if (input.Text.StartsWith("transfers."))
                                {
                                    nestedMustNotQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = input.Value,
                                    });
                                }
                                else
                                {
                                    mustNotQuery.Add(new MatchQuery()
                                    {
                                        Field = input.Text,
                                        Query = input.Value,
                                    });
                                }
                                break;
                        }
                        break;
                }
            }

            List<ISort> sortClauses = new List<ISort>();
            if (sortExpression.Any())
            {
                foreach (var item in sortExpression)
                {
                    if (item.PropertyName.ToLower() == "referencenumber" || item.PropertyName.ToLower() == "subject")
                    {
                        item.PropertyName = item.PropertyName + ".keyword";
                    }
                    sortClauses.Add(new FieldSort { Field = item.PropertyName.Substring(0, 1).ToLower() + item.PropertyName.Substring(1), Order = (SortOrder)item.Order });
                }
            }
            QueryContainer nestedQueryContainer = new BoolQuery { Must = nestedMustQuery, MustNot = nestedMustNotQuery };
            nestedQuery.Query = nestedQueryContainer;
            mustQuery.Add(nestedQuery);
            QueryContainer query = new BoolQuery { Must = mustQuery, MustNot = mustNotQuery };
            
            var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
            var result = Search(startIndex, pageSize, language, userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Configuration.SearchAssignedStructureSearchUsersDocuments, query, sortClauses,simpleSearch);
            ManageActivityLog.AddFullActivityLog(null, null, (int)ActivityLogs.Search, userId, JsonConvert.SerializeObject(model), "");
            return result;
        }

        /// <summary>
        /// ManageCorrespondenceSearch
        /// It is used to return all correspondences for admin only to mange them using filters (ref number & subject)
        /// </summary>
        /// <param name="model"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        /// 
        //public static async Task<(int, List<SearchTransferListViewModel>)> ManageCorrespondenceSearch(SearchModel model, int startIndex, int pageSize, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
        //    List<SortExpression> sortExpression, Intalio.Core.Language language = Intalio.Core.Language.EN)
        //{
        //    ManageActivityLog.AddFullActivityLog(null, null, (int)ActivityLogs.Search, userId, JsonConvert.SerializeObject(model), "");
        //    var delegation = model.DelegationId != default ? ManageDelegation.GetByDelegationId(userId, model.DelegationId.Value) : null;
        //    var categoryIds = new List<short>();
        //    ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
        //    if (delegation != null)
        //    {
        //        userId = delegation.FromUserId;
        //        structureIds = delegation.StructureIds;
        //        categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
        //        isStructureReceiver = delegation.IsStructureReceiver;
        //        privacyLevel = delegation.PrivacyLevel;
        //        if (filter == null)
        //            filter = new ExpressionBuilderFilters();

        //        if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
        //            filter.Add("Transfer[CreatedDate]", delegation.StartDate, Intalio.Core.Operator.GreaterThanOrEqual);
        //        else if (!delegation.ShowOldCorespondence)
        //            filter.Add("Transfer[CreatedDate]", delegation.FromDate, Intalio.Core.Operator.GreaterThanOrEqual);

        //        filter.Add("Transfer[CreatedDate]", delegation.ToDate, Intalio.Core.Operator.LessThanOrEqualTo);
        //        filter.Add("Privacy.Level", privacyLevel, Intalio.Core.Operator.LessThanOrEqualTo);
        //    }
        //    ExpressionBuilderFilters categoryFilter = new ExpressionBuilderFilters();
        //    if (model.DocumentId != default)
        //    {
        //        filter.Add("Id", model.DocumentId, Intalio.Core.Operator.NotEqual);
        //    }
        //    if (model.Category != default)
        //    {
        //        filter.Add("CategoryId", model.Category, Intalio.Core.Operator.Equals);
        //    }

        //    if (!categoryIds.IsNullOrEmpty())
        //    {
        //        foreach (var category in categoryIds)
        //        {
        //            categoryFilter.Add("CategoryId", category, Intalio.Core.Operator.Equals);
        //        }
        //    }

        //    if (model.Status != default)
        //    {
        //        filter.Add("StatusId", model.Status, Intalio.Core.Operator.Equals);
        //    }
        //    if (model.Priority != default)
        //    {
        //        filter.Add("PriorityId", model.Priority, Intalio.Core.Operator.Equals);
        //    }
        //    if (model.DocumentSender != default)
        //    {
        //        filter.Add("SendingEntityId", model.DocumentSender, Intalio.Core.Operator.Equals);
        //    }
        //    if (!string.IsNullOrEmpty(model.ReferenceNumber))
        //    {
        //        filter.Add("ReferenceNumber", model.ReferenceNumber, Intalio.Core.Operator.Contains);
        //    }
        //    if (!string.IsNullOrEmpty(model.Subject))
        //    {
        //        filter.Add("Subject", model.Subject, Intalio.Core.Operator.Contains);
        //    }
        //    if (!string.IsNullOrEmpty(model.FromDate))
        //    {
        //        DateTime from = DateTime.Parse(model.FromDate);
        //        filter.Add("CreatedDate.Date", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);
        //    }
        //    if (!string.IsNullOrEmpty(model.ToDate))
        //    {
        //        DateTime to = DateTime.Parse(model.ToDate);
        //        filter.Add("CreatedDate.Date", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);
        //    }
        //    if (model.FromUser != default)
        //    {
        //        filter.Add("Transfer[FromUserId]", model.FromUser, Intalio.Core.Operator.Equals);
        //    }
        //    if (model.ToUser != default)
        //    {
        //        filter.Add("Transfer[ToUserId]", model.ToUser, Intalio.Core.Operator.Equals);
        //    }
        //    if (model.FromStructure != default)
        //    {
        //        filter.Add("Transfer[FromStructureId]", model.FromStructure, Intalio.Core.Operator.Equals);
        //    }
        //    if (model.ToStructure != default)
        //    {
        //        filter.Add("Transfer[ToStructureId]", model.ToStructure, Intalio.Core.Operator.Equals);
        //    }
        //    if (!string.IsNullOrEmpty(model.FromTransferDate))
        //    {
        //        filter.Add("Transfer[CreatedDate]", DateTime.Parse(model.FromTransferDate), Intalio.Core.Operator.GreaterThanOrEqual);
        //    }
        //    if (!string.IsNullOrEmpty(model.ToTransferDate))
        //    {
        //        filter.Add("Transfer[CreatedDate]", DateTime.Parse(model.ToTransferDate), Intalio.Core.Operator.LessThanOrEqualTo);
        //    }
        //    if (model.DocumentReceiver != default)
        //    {
        //        filter.Add("DocumentReceiverEntity[StructureId]", model.DocumentReceiver, Intalio.Core.Operator.Equals);
        //    }
        //    if (model.IsOverdue)
        //    {
        //        filter.Add("DueDate", DateTime.Now, Intalio.Core.Operator.LessThan);
        //        filter.Add("StatusId", (short)DocumentStatus.InProgress, Intalio.Core.Operator.Equals);
        //    }

        //    using (Document item = new Document())
        //    {
        //        var catFilterExp = ExpressionBuilder.GetExpression<Document>(categoryFilter, ExpressionBuilderOperator.Or);
        //        var filterExp = ExpressionBuilder.GetExpression<Document>(filter, ExpressionBuilderOperator.And);
        //        var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
        //        var countResult = await item.GetSearchCount(userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp, catFilterExp, model.Keyword);
        //        var itemList = await item.ListSearchCorrespondenceAsync(startIndex, pageSize, userId, structureIds, isStructureReceiver, privacyLevel, searchAssignedStructures.StructureIds, Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp, catFilterExp, sortExpression.OrderByExpression<Document>(), model.Keyword);
        //        return (countResult, itemList.Select(t =>
        //        {
        //            var sendingEntity = string.Empty;
        //            if (t.SendingEntity != null)
        //            {
        //                sendingEntity = t.SendingEntity.Name;
        //                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.SendingEntity.NameAr))
        //                {
        //                    sendingEntity = t.SendingEntity.NameAr;
        //                }
        //                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.SendingEntity.NameFr))
        //                {
        //                    sendingEntity = t.SendingEntity.NameFr;
        //                }
        //            }
        //            return new SearchTransferListViewModel
        //            {
        //                Id = t.Id,
        //                Subject = t.Subject,
        //                CategoryId = t.CategoryId,
        //                ReferenceNumber = t.ReferenceNumber,
        //                StatusId = t.StatusId,
        //                CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
        //                PriorityId = t.PriorityId,
        //                PrivacyId = t.PrivacyId,
        //                DueDate = t.DueDate.HasValue ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : "",
        //                ImportanceId = t.ImportanceId,
        //                CreatedByUser = language == Intalio.Core.Language.EN ?
        //                $"{t.CreatedByUser.Firstname} {t.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.CreatedByUser.Id, language)}",
        //                SendingEntity = sendingEntity,
        //                ReceivingEntity = t.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, t.DocumentReceiverEntity.Select(t =>
        //                {
        //                    var receiverName = string.Empty;
        //                    if (t.Structure != null)
        //                    {
        //                        receiverName = t.Structure.Name;
        //                        if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
        //                        {
        //                            receiverName = t.Structure.NameAr;
        //                        }
        //                        else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
        //                        {
        //                            receiverName = t.Structure.NameFr;
        //                        }
        //                    }
        //                    else if (t.EntityGroup != null)
        //                    {
        //                        receiverName = t.EntityGroup.Name;
        //                        if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
        //                        {
        //                            receiverName = t.EntityGroup.NameAr;
        //                        }
        //                        else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
        //                        {
        //                            receiverName = t.EntityGroup.NameFr;
        //                        }
        //                    }
        //                    return receiverName;
        //                }).ToList()) : string.Empty,
        //            };
        //        }).ToList());
        //    }
        //}
        public static async Task<(int, List<SearchTransferListViewModel>)> ManageCorrespondenceSearch(
            SearchModel model, int startIndex, int pageSize, long userId,
              List<SortExpression> sortExpression, Intalio.Core.Language language = Intalio.Core.Language.EN)
        {
            ManageActivityLog.AddFullActivityLog(null, null, (int)ActivityLogs.Search, userId, JsonConvert.SerializeObject(model), "");
            ExpressionBuilderFilters filter = new ExpressionBuilderFilters();
            var categoryIds = new List<short>();

            //ExpressionBuilderFilters docfilter = new ExpressionBuilderFilters();
            ExpressionBuilderFilters categoryFilter = new ExpressionBuilderFilters();
        
            if (model.Category != default)
            {
                filter.Add("Document.CategoryId", model.Category, Intalio.Core.Operator.Equals);
            }

            if (!categoryIds.IsNullOrEmpty())
            {
                foreach (var category in categoryIds)
                {
                    categoryFilter.Add("Document.CategoryId", category, Intalio.Core.Operator.Equals);
                }
            }
            if (model.DocumentId != default)
            {
                filter.Add("DocumentId", model.DocumentId, Intalio.Core.Operator.NotEqual);
            }


            if (!string.IsNullOrEmpty(model.ReferenceNumber))
            {
                filter.Add("Document.ReferenceNumber", model.ReferenceNumber, Intalio.Core.Operator.Contains);
            }
            if (!string.IsNullOrEmpty(model.Subject))
            {
                filter.Add("Document.Subject", model.Subject, Intalio.Core.Operator.Contains);
            }
            if (model.Priority != default)
            {
                filter.Add("Document.PriorityId", model.Priority, Intalio.Core.Operator.Equals);
            }
            if (model.Status != default)
            {
                filter.Add("StatusId", model.Status, Intalio.Core.Operator.Equals);
            }
            if (!string.IsNullOrEmpty(model.FromDate))
            {
                DateTime from = DateTime.Parse(model.FromDate);
                filter.Add("CreatedDate.Date", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);
            }
            if (!string.IsNullOrEmpty(model.ToDate))
            {
                DateTime to = DateTime.Parse(model.ToDate);
                filter.Add("CreatedDate.Date", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);
            }
        
            if (model.FromUser != default)
            {
                filter.Add("FromUserId", model.FromUser, Intalio.Core.Operator.Equals);
            }
            if (model.ToUser != default)
            {
                filter.Add("ToUserId", model.ToUser, Intalio.Core.Operator.Equals);
            }
            if (model.FromStructure != default)
            {
                filter.Add("FromStructureId", model.FromStructure, Intalio.Core.Operator.Equals);
            }
         

            if (model.ToStructure != default)
            {
                filter.Add("ToStructureId", model.ToStructure, Intalio.Core.Operator.Equals);
            }
            if (!string.IsNullOrEmpty(model.FromTransferDate))
            {
                DateTime from = DateTime.Parse(model.FromTransferDate);

                filter.Add("CreatedDate.Date", from.Date, Intalio.Core.Operator.GreaterThanOrEqual);


             

            }
            if (!string.IsNullOrEmpty(model.ToTransferDate))
            {
                //filter.Add("CreatedDate", (DateTime.Parse(model.ToTransferDate)), Intalio.Core.Operator.LessThanOrEqualTo);

                DateTime to = DateTime.Parse(model.ToTransferDate);

                filter.Add("CreatedDate.Date", to.Date, Intalio.Core.Operator.LessThanOrEqualTo);

            }
            if (model.DocumentReceiver != default)
            {
                //filter.Add("Document.DocumentReceiverEntity[StructureId]", DateTime.Parse(model.ToTransferDate), Intalio.Core.Operator.LessThanOrEqualTo);
                //filter.Add("DocumentReceiverEntity[StructureId]", model.DocumentReceiver, Intalio.Core.Operator.Equals);
                //filter.Add("Document.DocumentReceiverEntity[StructureId]", model.DocumentReceiver, Intalio.Core.Operator.Equals);
            }
            if (model.DocumentSender != default)
            {
                filter.Add("Document.SendingEntityId", model.DocumentSender, Intalio.Core.Operator.Equals);
            }
            if (model.IsOverdue)
            {
                filter.Add("DueDate", DateTime.Now, Intalio.Core.Operator.LessThan);
                filter.Add("StatusId", (short)DocumentStatus.InProgress, Intalio.Core.Operator.Equals);
            }


            try
            {
                using (Transfer item = new Transfer())
                {
                    var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                    //var filterExp = Utility.FilterExcCustom.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                    //var docfilterExp = ExpressionBuilder.GetExpression<Document>(docfilter, ExpressionBuilderOperator.And);
                    //var searchAssignedStructures = ManageSearchAssignedSecurity.GetSearchAssignedSecurity(userId, structureIds);
                    var countResult = await item.GetManageCorrespondenceSearchCount(userId, Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp, model.Keyword, model.DocumentReceiver);
                    var itemList = await item.ListManageCorrespondenceSearchAsync(startIndex, pageSize, userId, Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp, sortExpression.OrderByExpression<Transfer>(), model.Keyword, model.DocumentReceiver);
                    return (countResult, itemList.Select(t =>
                    {
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }
                        var isOverdue = false;
                        if (t.DueDate.HasValue)
                        {
                            if (t.ClosedDate.HasValue)
                            {
                                isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                            }
                            else
                            {
                                isOverdue = DateTime.Now.Date > t.DueDate.Value;
                            }
                        }

                        
                        return new SearchTransferListViewModel
                        {

                            Id = t.Id,
                            WorkflowStepId = t.WorkflowStepId,
                            Cced = t.Cced,
                            IsSigned= t.Document.IsSigned.HasValue ? t.Document.IsSigned.Value : false,
                            DocumentId = t.DocumentId.Value,
                            Subject = t.Document.Subject,
                            CategoryId = t.Document.CategoryId,
                            ReferenceNumber = t.Document.ReferenceNumber,
                            StatusId = t.StatusId.Value,
                            CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                            PriorityId = t.Document.PriorityId,
                            PrivacyId = t.Document.PrivacyId,
                            DueDate = t.DueDate.HasValue ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : "",
                            IsOverDue = isOverdue,
                            ImportanceId = t.Document.ImportanceId,
                            CreatedByUser = language == Intalio.Core.Language.EN ?
                            $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                            SendingEntity = sendingEntity,
                            DocumentCarbonCopy = t.Document.DocumentCarbonCopy.Count > 0 ? string.Join(Constants.SEPARATOR, t.Document.DocumentCarbonCopy.Select(t =>
                            {
                                var carboncopyName = string.Empty;
                             
                                    carboncopyName = t.Structure.Name;
                             
                                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    carboncopyName = t.Structure.NameAr;
                                }
                                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    carboncopyName = t.Structure.NameFr;
                                }

                                return carboncopyName;
                            }).ToList()) : string.Empty,
                            

                            ReceivingEntity = t.Document.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, t.Document.DocumentReceiverEntity.Select(t =>
                            {
                                var receiverName = string.Empty;
                                if (t.Structure != null)
                                {
                                    receiverName = t.Structure.Name;
                                    if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                    {
                                        receiverName = t.Structure.NameAr;
                                    }
                                    else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                    {
                                        receiverName = t.Structure.NameFr;
                                    }
                                }
                                else if (t.EntityGroup != null)
                                {
                                    receiverName = t.EntityGroup.Name;
                                    if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                    {
                                        receiverName = t.EntityGroup.NameAr;
                                    }
                                    else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                    {
                                        receiverName = t.EntityGroup.NameFr;
                                    }
                                }
                                return receiverName;
                            }).ToList()) : string.Empty,
                            IsLocked = !t.ToUserId.HasValue && t.OwnerUserId != null ? true : false,
                            ToStructureId = t.ToStructureId.Value,

                            ToStructure = t.ToStructure != null && t.ToStructure.Name != null ? (language == Intalio.Core.Language.EN ? t.ToStructure.Name : 
                            (language == Intalio.Core.Language.AR ? (t.ToStructure.NameAr == null ? t.ToStructure.Name : t.ToStructure.NameAr) : 
                            (t.ToStructure.NameFr == null ? t.ToStructure.Name : t.ToStructure.NameFr))) : "",

                            FromStructure = t.FromStructure != null && t.FromStructure.Name != null ? (language == Intalio.Core.Language.EN ? t.FromStructure.Name :
                            (language == Intalio.Core.Language.AR ? (t.ToStructure.NameAr == null ? t.FromStructure.Name : t.FromStructure.NameAr) :
                            (t.FromStructure.NameFr == null ? t.FromStructure.Name : t.ToStructure.NameFr))) : "",

                            ToUserId = t.ToUserId.HasValue ? t.ToUserId.Value : null,
                            ToUser = t.ToUser != null ? language == Intalio.Core.Language.EN ?
                            $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}" : "",
                            DocumentStatusId = t.Document.StatusId,
                            OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        };
                    }).ToList());
                }

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public static async Task<(int, List<SearchTransferListViewModel>)> ManageStructureUsersCorrespondencesSearch(
            StructureUserCorrespondenceSearchModel model, int startIndex, int pageSize, long userId,
              List<SortExpression> sortExpression, Intalio.Core.Language language = Intalio.Core.Language.EN)
        {
            ManageActivityLog.AddFullActivityLog(null, null, (int)ActivityLogs.Search, userId, JsonConvert.SerializeObject(model), "");
            ExpressionBuilderFilters filter = new ExpressionBuilderFilters();            
            if (model.UserInbox.HasValue)
            { 
                filter.Add("ToUserId", model.UserInbox, Intalio.Core.Operator.Equals);
                if (model.UserStructure != null && model.UserStructure != default && Configuration.EnablePerStructure)
                {
                    filter.Add("ToStructureId", model.UserStructure, Intalio.Core.Operator.Equals);
                }
            }
            if (model.UserSent != default)
            {
                filter.Add("FromUserId", model.UserSent, Intalio.Core.Operator.Equals);
                filter.Add("ToUserId", model.UserSent, Intalio.Core.Operator.NotEqual);
                if (model.UserStructure != null && model.UserStructure != default && Configuration.EnablePerStructure)
                {
                    filter.Add("FromStructureId", model.UserStructure, Intalio.Core.Operator.Equals);
                }

            }
            if (model.Privacy != null)
            {
                filter.Add("Document.Privacy.Level", model.Privacy, Intalio.Core.Operator.Equals);
            }
            filter.Add("Document.Privacy.Level", model.UserPrivacyId, Intalio.Core.Operator.LessThanOrEqualTo);
            try
            {
                using (Transfer item = new Transfer())
                {
                    var filterExp = ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And);
                    var countResult = await item.GetManageStructureUsersCorrespondencesSearchCount(userId, Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp);
                    var itemList = await item.ListManageStructureUsersCorrespondencesSearchAsync(startIndex, pageSize, userId, Configuration.SearchAssignedStructureSearchUsersDocuments, filterExp, sortExpression.OrderByExpression<Transfer>());
                    return (countResult, itemList.Select(t =>
                    {
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }
                        var isOverdue = false;
                        if (t.DueDate.HasValue)
                        {
                            if (t.ClosedDate.HasValue)
                            {
                                isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                            }
                            else
                            {
                                isOverdue = DateTime.Now.Date > t.DueDate.Value;
                            }
                        }


                        return new SearchTransferListViewModel
                        {

                            Id = t.Id,
                            WorkflowStepId = t.WorkflowStepId,
                            Cced = t.Cced,
                            IsSigned = t.Document.IsSigned.HasValue ? t.Document.IsSigned.Value : false,
                            DocumentId = t.DocumentId.Value,
                            Subject = t.Document.Subject,
                            CategoryId = t.Document.CategoryId,
                            ReferenceNumber = t.Document.ReferenceNumber,
                            StatusId = t.StatusId.Value,
                            CreatedDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                            PriorityId = t.Document.PriorityId,
                            PrivacyId = t.Document.PrivacyId,
                            DueDate = t.DueDate.HasValue ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : "",
                            IsOverDue = isOverdue,
                            ImportanceId = t.Document.ImportanceId,
                            CreatedByUser = language == Intalio.Core.Language.EN ?
                            $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                            SendingEntity = sendingEntity,
                            DocumentCarbonCopy = t.Document.DocumentCarbonCopy.Count > 0 ? string.Join(Constants.SEPARATOR, t.Document.DocumentCarbonCopy.Select(t =>
                            {
                                var carboncopyName = string.Empty;

                                carboncopyName = t.Structure.Name;

                                if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                {
                                    carboncopyName = t.Structure.NameAr;
                                }
                                else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                {
                                    carboncopyName = t.Structure.NameFr;
                                }

                                return carboncopyName;
                            }).ToList()) : string.Empty,


                            ReceivingEntity = t.Document.DocumentReceiverEntity.Count > 0 ? string.Join(Constants.SEPARATOR, t.Document.DocumentReceiverEntity.Select(t =>
                            {
                                var receiverName = string.Empty;
                                if (t.Structure != null)
                                {
                                    receiverName = t.Structure.Name;
                                    if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                    {
                                        receiverName = t.Structure.NameAr;
                                    }
                                    else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                    {
                                        receiverName = t.Structure.NameFr;
                                    }
                                }
                                else if (t.EntityGroup != null)
                                {
                                    receiverName = t.EntityGroup.Name;
                                    if (language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                    {
                                        receiverName = t.EntityGroup.NameAr;
                                    }
                                    else if (language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                    {
                                        receiverName = t.EntityGroup.NameFr;
                                    }
                                }
                                return receiverName;
                            }).ToList()) : string.Empty,
                            IsLocked = t.OpenedDate != null/*!t.ToUserId.HasValue && t.OwnerUserId != null ? true : false*/,
                            ToStructureId = t.ToStructureId.Value,

                            ToStructure = t.ToStructure != null && t.ToStructure.Name != null ? (language == Intalio.Core.Language.EN ? t.ToStructure.Name :
                            (language == Intalio.Core.Language.AR ? (t.ToStructure.NameAr == null ? t.ToStructure.Name : t.ToStructure.NameAr) :
                            (t.ToStructure.NameFr == null ? t.ToStructure.Name : t.ToStructure.NameFr))) : "",

                            FromStructure = t.FromStructure != null && t.FromStructure.Name != null ? (language == Intalio.Core.Language.EN ? t.FromStructure.Name :
                            (language == Intalio.Core.Language.AR ? (t.ToStructure.NameAr == null ? t.FromStructure.Name : t.FromStructure.NameAr) :
                            (t.FromStructure.NameFr == null ? t.FromStructure.Name : t.ToStructure.NameFr))) : "",

                            ToUserId = t.ToUserId.HasValue ? t.ToUserId.Value : null,
                            ToUser = t.ToUser != null ? language == Intalio.Core.Language.EN ?
                            $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}" : "",
                            DocumentStatusId = t.Document.StatusId,
                        };
                    }).ToList());
                }

            }
            catch (Exception ex)
            {
                throw;
            }
        }
        #endregion

        #region Private Methods

        private static (int, List<SearchListViewModel>) Search(int startIndex, int pageSize, Intalio.Core.Language Language, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, List<long> searchAssignedStructures, bool searchAssignedStructureSearchUsersDocuments,
            QueryContainer filterContainer = null, List<ISort> sortClauses = null,string simpleSearch = null)
        {
            List<QueryContainer> queryContainer = new List<QueryContainer>();
            List<SearchAssignedSecurity> AssignedStructures = searchAssignedStructures != null
            ? new SearchAssignedSecurity().GetSearchAssignedSecurity(userId, searchAssignedStructures)
            : new List<SearchAssignedSecurity>();
            if (userId > 0)
            {
                queryContainer.Add(new TermQuery()
                {
                    Field = "createdByUserId",
                    Value = userId
                });
                
                queryContainer.Add(new NestedQuery()
                {
                    Path = "transfers",
                    Query = new TermQuery()
                    {
                        Field = "transfers.fromUserId",
                        Value = userId
                    }
                });
                queryContainer.Add(new NestedQuery()
                {
                    Path = "transfers",
                    Query = new TermQuery()
                    {
                        Field = "transfers.toUserId",
                        Value = userId
                    }
                });
                if(simpleSearch != null)
                {
                    queryContainer.Add(new TermQuery()
                    {
                        Field = "subject",
                        Value = simpleSearch
                    });
                    queryContainer.Add(new TermQuery()
                    {
                        Field = "referenceNumber",
                        Value = simpleSearch
                    });
                }
            }
            if (!searchAssignedStructures.IsNullOrEmpty())
            {
                //structureIds.AddRange(searchAssignedStructures);
                //structureIds = structureIds.Distinct().ToList();
                foreach (var AssignedStructure in AssignedStructures)
                {
                if (searchAssignedStructureSearchUsersDocuments)
                {
                    queryContainer.Add(
                        new BoolQuery
                        {
                            Must = new List<QueryContainer> {
                                new ScriptQuery
                                {
                                    Script = new InlineScript($" doc['privacyLevel'].value <= {AssignedStructure.PrivacyLevel}")
                                }
                            },
                            Should = new List<QueryContainer>
                            {
                                new BoolQuery
                                {
                                    Must = new List<QueryContainer>
                                    {
                                        new NestedQuery()
                                        {
                                            Path="transfers",
                                            Query=new BoolQuery
                                            {
                                                Must = new List<QueryContainer>
                                                {
                                                    new TermsQuery()
                                                    {
                                                        Field="transfers.toStructureId",
                                                        Terms= structureIds.Select(s => (object)s)
                                                    }
                                                },
                                                MustNot = new List<QueryContainer>
                                                {
                                                    new ExistsQuery()
                                                    {
                                                        Field="transfers.toUserId"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                },
                                new BoolQuery
                                {
                                    Must = new List<QueryContainer>
                                    {
                                        new NestedQuery()
                                        {
                                            Path="transfers",
                                            Query=new TermsQuery()
                                            {
                                                Field="transfers.toStructureId",
                                                Terms= new List<object> { AssignedStructure.ToStructureId }
                                            }
                                        }
                                    }
                                },
                                new BoolQuery
                                {
                                    Must = new List<QueryContainer>
                                    {
                                        new NestedQuery()
                                        {
                                            Path="transfers",
                                            Query=new TermsQuery()
                                            {
                                                Field="transfers.fromStructureId",
                                                Terms= new List<object> { AssignedStructure.ToStructureId }
                                            }
                                        }
                                    }
                                }
                            },
                            MinimumShouldMatch = 1
                        });
                }
                else
                {
                    queryContainer.Add(
                       new BoolQuery
                       {
                           Must = new List<QueryContainer> {
                                new ScriptQuery
                                {
                                    Script = new InlineScript($" doc['privacyLevel'].value <= {AssignedStructure.PrivacyLevel}")
                                }

                           },
                           Should = new List<QueryContainer>
                           {
                            new BoolQuery
                            {
                                Must = new List<QueryContainer>
                                {
                                    new NestedQuery()
                                    {
                                        Path="transfers",
                                        Query=new BoolQuery
                                        {
                                            Must = new List<QueryContainer>
                                            {
                                                new TermsQuery()
                                                {
                                                    Field="transfers.toStructureId",
                                                    Terms= structureIds.Select(s => (object)s)
                                                }
                                            },
                                            MustNot = new List<QueryContainer>
                                            {
                                                new ExistsQuery()
                                                {
                                                    Field="transfers.toUserId"
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            new BoolQuery
                            {

                                Must = new List<QueryContainer>
                                {
                                    new NestedQuery()
                                    {
                                        Path="transfers",
                                        Query=new BoolQuery
                                        {
                                            Must = new List<QueryContainer>
                                            {
                                                new TermsQuery()
                                                {
                                                    Field="transfers.toStructureId",
                                                    Terms= new List<object> { AssignedStructure.ToStructureId }
                                                }
                                            },
                                            MustNot = new List<QueryContainer>
                                            {
                                                new ExistsQuery()
                                                {
                                                    Field="transfers.toUserId"
                                                }
                                            } , 

                                        }
                                    }
                                }
                            },
                            new BoolQuery
                            {
                                Must = new List<QueryContainer>
                                {
                                    new NestedQuery()
                                    {
                                        Path="transfers",
                                        Query=new BoolQuery
                                        {
                                            Must = new List<QueryContainer>
                                            {
                                                new TermsQuery()
                                                {
                                                    Field="transfers.fromStructureId",
                                                    Terms= new List<object> { AssignedStructure.ToStructureId }
                                                }
                                            },
                                             MustNot = new List<QueryContainer>
                                            {
                                                new ExistsQuery()
                                                {
                                                    Field="transfers.toUserId"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                           },
                           MinimumShouldMatch = 1
                       });
                }
            }
            }
            else
            {
                queryContainer.Add(new BoolQuery
                {
                    Must = new List<QueryContainer>
                        {
                            new NestedQuery()
                            {
                                Path="transfers",
                                Query=new BoolQuery
                                {
                                    Must = new List<QueryContainer>
                                    {
                                        new TermsQuery()
                                        {
                                            Field="transfers.toStructureId",
                                            Terms= structureIds.Select(s => (object)s)
                                        }
                                    },
                                    MustNot = new List<QueryContainer>
                                    {
                                        new ExistsQuery()
                                        {
                                            Field="transfers.toUserId"
                                        }
                                    }
                                }
                            },
                            new ScriptQuery
                            {
                                Script = new InlineScript($"{isStructureReceiver.ToString().ToLower()}==true")
                            },
                            new ScriptQuery
                            {
                                Script = new InlineScript($" doc['privacyLevel'].value <= {privacyLevel}")
                            }
                        }
                });
            }

            QueryContainer query = new BoolQuery
            {
                Must = new List<QueryContainer> { filterContainer },
                Should = queryContainer,
                MinimumShouldMatch = 1
            };
            var item = Search(startIndex, pageSize, query, sortClauses);
            List<Structure> sendingEntities = new List<Structure>();
            List<User> createdByUsers = new List<User>();
            List<DocumentReceiverEntity> documentReceiverEntities = new List<DocumentReceiverEntity>();

            List<long> sendingEntityIds = item.Item2.Where(d => d.SendingEntityId.HasValue).Select(x => (long)x.SendingEntityId).Distinct().ToList();
            List<long> createdByUserIds = item.Item2.Where(d => d.CreatedByUserId.HasValue).Select(x => (long)x.CreatedByUserId).Distinct().ToList();
            List<long> documentIds = item.Item2.Select(x => x.Id).ToList();
            if (sendingEntityIds.Any())
            {
                sendingEntities = new Structure().ListByIds(sendingEntityIds);
            }
            if (createdByUserIds.Any())
            {
                createdByUsers = new User().ListByIds(createdByUserIds);
            }
            if (documentIds.Any())
            {
                documentReceiverEntities = new DocumentReceiverEntity().ListByDocumentIdsIncludeEntityGroupAndStructure(documentIds);
            }
            List<SearchListViewModel> result = new List<SearchListViewModel>();
            result = (from t in item.Item2
                      join sendingEntity in sendingEntities on t.SendingEntityId equals sendingEntity.Id into se
                      from sendingEntityResult in se.DefaultIfEmpty()
                      join createdByUser in createdByUsers on t.CreatedByUserId equals createdByUser.Id
                      let documentReceiverEntity = documentReceiverEntities.Where(dr => dr.DocumentId == t.Id)
                      let sendingEntityName = sendingEntityResult != null ?
                                                 (Language == Intalio.Core.Language.AR ? sendingEntityResult.NameAr : (Language == Intalio.Core.Language.FR ? sendingEntityResult.NameFr : sendingEntityResult.Name))
                                                 : string.Empty
                      select new SearchListViewModel
                      {
                          Id = t.Id,
                          Subject = t.Subject,
                          CategoryId = t.CategoryId.Value,
                          ReferenceNumber = t.ReferenceNumber,
                          StatusId = t.StatusId.Value,
                          CreatedDate = t.CreatedDate.Value.ToString(Constants.DATE_FORMAT),
                          PriorityId = t.PriorityId,
                          PrivacyId = t.PrivacyId,
                          DueDate = t.DueDate.HasValue ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : "",
                          ImportanceId = t.ImportanceId,
                          CreatedByUser = Language == Intalio.Core.Language.EN ?
                          $"{createdByUser.Firstname} {createdByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(createdByUser.Id, Language)}",
                          SendingEntity = sendingEntityName ?? sendingEntityResult.Name,
                          ReceivingEntity = documentReceiverEntity.Count() > 0 ? string.Join(Constants.SEPARATOR, documentReceiverEntity
                                  .Select(t =>
                                  {
                                      var receiverName = string.Empty;
                                      if (t.Structure != null)
                                      {
                                          receiverName = t.Structure.Name;
                                          if (Language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.Structure.NameAr))
                                          {
                                              receiverName = t.Structure.NameAr;
                                          }
                                          else if (Language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.Structure.NameFr))
                                          {
                                              receiverName = t.Structure.NameFr;
                                          }
                                      }
                                      else if (t.EntityGroup != null)
                                      {
                                          receiverName = t.EntityGroup.Name;
                                          if (Language == Intalio.Core.Language.AR && !string.IsNullOrEmpty(t.EntityGroup.NameAr))
                                          {
                                              receiverName = t.EntityGroup.NameAr;
                                          }
                                          else if (Language == Intalio.Core.Language.FR && !string.IsNullOrEmpty(t.EntityGroup.NameFr))
                                          {
                                              receiverName = t.EntityGroup.NameFr;
                                          }
                                      }
                                      return receiverName;
                                  }).ToList()) : string.Empty,
                      }).ToList();

            return (item.Item1, result);
        }

        private static (int, List<DocumentCrawlingModel>) Search(int startIndex, int pageSize, QueryContainer query = null, List<ISort> sortClauses = null)
        {
            if (sortClauses == null)
            {
                sortClauses = new List<ISort> { new FieldSort { Field = "id", Order = SortOrder.Descending } };
            }
            var sourceFilterInterface = new Union<bool, ISourceFilter>(new SourceFilter
            {
                Excludes = new[] { "ocrContent", "form", "transfers" }
            });
            ISearchRequest searchRequest = new SearchRequest(Configuration.CrawlerDefaultIndexName)
            {
                From = startIndex,
                Size = pageSize,
                Query = query,
                Sort = sortClauses,
                SearchType = Elasticsearch.Net.SearchType.QueryThenFetch,
                TrackTotalHits = true,
                Source = sourceFilterInterface
            };
            //var json = CreateClient(Configuration.CrawlerUserName, Configuration.CrawlerPassword).RequestResponseSerializer.SerializeToString(searchRequest, SerializationFormatting.Indented);
            ISearchResponse<DocumentCrawlingModel> response = CreateClient(Configuration.CrawlerUserName, Configuration.CrawlerPassword).Search<DocumentCrawlingModel>(searchRequest);
            if (response.ApiCall.HttpStatusCode == null)
            {
                throw new Exception("Crawling service not configured");
            }
            int countResult = Configuration.CrawlerMaxResultWindow;
            countResult = response.Total > countResult ? countResult : (int)response.Total;
            return (countResult, response.Documents.ToList());
        }

        private static IElasticClient CreateClient(string userName, string password)
        {
            if (_client == null)
            {
                var nodes = new List<Uri>();
                foreach (var url in Core.Configuration.CrawlerServerUrls)
                {
                    nodes.Add(new Uri(url.TrimEnd('/')));
                }
                var connectionPool = new StaticConnectionPool(nodes);
                ConnectionSettings connectionSettings = new ConnectionSettings(connectionPool)
                    .BasicAuthentication(userName, password)
                    .DefaultIndex(Core.Configuration.CrawlerDefaultIndexName);
                _client = new ElasticClient(connectionSettings);
            }
            return _client;
        }

        #endregion
    }
}
