﻿import Intalio from './common.js'
import { IdentityService } from './lookup.js'
import Distribution from './distributionList.js'
class FollowUpPanelIndex extends Intalio.Model {
    constructor() {
        super();
        this.followUpId = 0;
    }
}
function checkInputs() {
    let allEmpty = $("#formPostFollowUpPanelIndex input[type='text']").filter(function () {
        return this.value.trim() !== "";
    }).length === 0;  // Check if all inputs are empty

    $("#btnSubmit").prop("disabled", allEmpty);
}
class FollowUpPanelIndexView extends Intalio.View {
    constructor(element, model) {
        super(element, "followUpPanelIndex", model);
    }
    render() {
        var self = this;
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#btnClose').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSubmit').focus();
                }
                else {
                    $('#txtEvent').focus();
                }
            }
        });
        $('#txtEvent').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnClose').focus();
                }
                else {
                    $('#txtEventDate').focus();
                }
            }
        });
        $('#txtEventDate').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtEvent').focus();
                }
                else {
                    $('#txtTransferredTo').focus();
                }
            }
        });
        $('#txtTransferredTo').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtEventDate').focus();
                }
                else {
                    $('#txtTransferredDate').focus();
                }
            }
        });
        $('#formPostFollowUpPanelIndex').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                e.preventDefault();
                $('#btnSubmit').trigger("click");
            }
        });
        $('#txtTransferredDate').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtTransferredTo').focus();
                }
                else {
                    $('#txtResponsibleUser').focus();
                }
            }
        });
        $('#txtResponsibleUser').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtTransferredDate').focus();
                }
                else {
                    $('#txtFollowUpPanelStatus').focus();
                }
            }
        });
        $('#txtFollowUpPanelStatus').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtResponsibleUser').focus();
                }
                else {
                    $('#txtNotes').focus();
                }
            }
        });
        $('#txtNotes').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtFollowUpPanelStatus').focus();
                }
                else {
                    $('#txtEvent').focus();
                }
            }
        });
        $('#btnSubmit').attr('disabled', 'disabled');
        $("#formPostFollowUpPanelIndex input[type='text']").on("input", checkInputs);
        var eventDate = $('#txtEventDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            allowInput: false, 
            onReady: function (selectedDates, dateStr, instance) {
                $(instance._input).css({
                    'background-color': '#fff',
                    'color': '#000',
                    'cursor': 'pointer'
                });
            },
            onOpen: function (ct) {
                this.set({
                });
            }
        });
        $("#txtEventDate_img").click(function () {
            eventDate.toggle();
        });

        var transferredDate = $('#txtTransferredDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            allowInput: false, 
            onReady: function (selectedDates, dateStr, instance) {
                $(instance._input).css({
                    'background-color': '#fff',
                    'color': '#000',
                    'cursor': 'pointer'
                });
            },
            onOpen: function (ct) {
                this.set({
                });
            }
        });
        $("#txtTransferredDate_img").click(function () {
            transferredDate.toggle();
        });

        $('#btnSubmit').on('click', function () {

            var $form = $('#formPostFollowUpPanelIndex');
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            isValid = isValid && !($("#btnSubmit").prop("disabled"));
            if (isValid) {
                let params = {
                    FollowUpId: self.model.followUpId,
                    Event: $('#txtEvent').val(),
                    EventDate: $('#txtEventDate').val(),
                    TransferredTo: $('#txtTransferredTo').val(),
                    TransferredDate: $('#txtTransferredDate').val(),
                    ResponsibleUser: $('#txtResponsibleUser').val(),
                    Notes: $('#txtNotes').val(),
                    FollowUpPanelStatus: $('#txtFollowUpPanelStatus').val(),
                };
                var btn = $('#btnSubmit');
                btn.button('loading');
                var btnClose = $('#btnClose');
                var btnCloseX = $('#committeeClose');
                btnClose.attr('disabled', 'disabled');
                btnCloseX.attr('disabled', 'disabled');
                Common.ajaxPost('/FollowUp/CreateFollowUpPanel', params, function (data) {
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    btnCloseX.removeAttr('disabled');
                    if (data.message !== null && data.message !== "") {
                        setTimeout(function () {
                            Common.alertMsg(data.message);
                        }, 300);
                    } else {
                        Common.showScreenSuccessMsg();
                        document.getElementById('hdId').value = data.id;
                        $("#followUpPanelGrdItems").DataTable().ajax.reload();
                        $('#modalFollowUpPanelIndex').modal('hide');
                    }
                }, function () { btn.button('reset'); btnClose.removeAttr('disabled'); btnCloseX.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);
            }
        });
    };
}
export default { FollowUpPanelIndex, FollowUpPanelIndexView };