﻿import Intalio from './common.js'
import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import { Helper } from './lookup.js'


function viewVersion(versionNumber, fileId, documentId, transferId, delegationId) {
    $("#modalExportOptions > .modal-dialog").removeClass("modal-lg").addClass("modal-xl");
    $("#versionViewerContainer").show();
    $("#versionGridContainer").removeClass("col-lg-12").addClass("col-lg-6");
    var isDraft = transferId === null || typeof transferId === 'undefined' ? true : false;
    $("#versionViewerFrame").attr("src", window.ViewerUrl + "/templates?documentId=" + fileId + "&language=" + window.language + "&token=" +
        window.IdentityAccessToken + "&version=" + versionNumber + "&ctsDocumentId=" + documentId +
        "&ctsTransferId=" + transferId + "&delegationId=" + delegationId + "&viewermode=view&isDraft=" + isDraft);

}
var gLocked = false;
var table;
class ExportOptions extends Intalio.Model {
    constructor() {
        super();
        this.isSpecific = false;
        this.fileId = null;
        this.transferId = null;
        this.documentId = null;
        this.categoryId = null;
        this.delegationId = null;
        this.hasEditAccess = false;
        this.allowRestore = true;
        this.purposes = [];
        this.transferToType = TransferType.Send;
        this.transferToUser = null;
        this.transferToStructure = null;
        this.customAttributeDueDate = null;
        this.documentId = null;
        this.transferId = null;
        this.isBroadcast = false;
        this.broadcastIds = [];
        this.fromVip = false;
        this.isStructureSender = false;
        this.structureIds = [];
        this.enableSendingRules = false;
        this.enableTransferToUsers = false;
        this.delegationId;
        this.fromStructureId = null;
        this.action = null;
        this.receivingEntities = [];
        this.transferId = null;
        this.actionsComponentId = null;
        this.fromExport = false;
        this.documentCarbonCopy = [];
        this.fromResend = false;
        this.referenceNumber = null;
    }
}
class ExportOptionsView extends Intalio.View {
    constructor(element, model, callback) {
        super(element, "exportOptions", model);
        this.callback = callback;

    }
    render(callback) {
        $.fn.dataTable.render.moment = function (from, to, locale) {
            // Argument shifting
            if (arguments.length === 1) {
                locale = 'en';
                to = from;
                from = 'YYYY-MM-DD';
            }
            else if (arguments.length === 2) {
                locale = 'en';
            }

            return function (d, type, row) {
                if (!d) {
                    return type === 'sort' || type === 'type' ? 0 : d;
                }

                var m = window.moment(new Date(d), from, locale, true);

                // Order and type get a number value from Moment, everything else
                // sees the rendered value
                return m.format(type === 'sort' || type === 'type' ? 'x' : to);
            };
        };
        $("#versionViewerContainer").hide();
        Common.gridCommon();
        var selfModel = this;

        var selectedRadio = true;
        var tableData = selfModel.model.receivingEntities
                    .concat(selfModel.model.documentCarbonCopy)
                    .map(e => {
                        if(!e.element) return e;
                        return {...e, isExternal: $(e.element).data('external')}
                    });


        const externalData = tableData.filter(e => Boolean(e.isExternal)).map(e => e.id);

        table = $(selfModel.refs['grdExportOptions']).DataTable({
            processing: true,
            ordering: false,
            pageLength: 10,
            // "data": tableData,
            "columns": [
                {
                    title: '', width: '16px', "orderable": false,
                    "render": function (data, type, row)
                    {
                        let isDisabled = false;
                        if(window.HasG2G == "False" && row.isExternal && row.govCorrespondence)
                        {
                            isDisabled = true;
                        }
                        return `<input type='checkbox' onclick='event.stopPropagation();' data-id="${row.id}" ${isDisabled ? 'disabled=disabled' : 'checked'}/>`;
                    }
                },
                { title: "Id", data: "id", visible: false, "orderable": false },
                //{ title: Resources.Name, data: "text", render: $.fn.dataTable.render.text(), "autoWidth": true },
                {
                    title: Resources.Name, data: "text", render: (data, type, full, meta) => {
                        if(full.parentName)
                        {
                            data = full.parentName + window.Seperator + data;
                        }
                        return (data  + '  ' + (full.isCC ? '<i class="fa fa-cc font-16 text-warning"></i>' : ''))
                    }, "autoWidth": true
                },
                { title: Resources.Type, data: "isExternal", render: (data, type, full, meta) => { return (data ? Resources.External : Resources.Internal) }, "autoWidth": true },
                {
                    title: Resources.GovernmentCorrespondence, data: "govCorrespondence", render: (data, type, full, meta) => {
                        return (data ? Resources.Yes : Resources.No)
                    }, "autoWidth": true
                },
            ],
            dom: 'trpi'
        });
        const finalFn = () => {
            table.clear();
            table.rows.add(tableData);
            table.draw();
            GridCommon.AddCheckBoxEvents('grdExportOptions');
            tableData.filter(row =>
                window.HasG2G != "False" || !row.isExternal || !row.govCorrespondence
            ).forEach(row => GridCommon.Add('grdExportOptions', String(row.id)))
        };
        Common.ajaxPostJSON("Document/FilterG2GExternal",
            JSON.stringify(externalData),
            (e) => {
                const s = new Set(e);
                tableData.forEach(e => {
                    e.govCorrespondence = s.has(parseInt(e.id));
                });
                finalFn();
        }, finalFn, true);

        $("#btnExportOptionsClose, .ExportOptionsModalClose").on('click', function () {
            $('#modalExportOptions').modal('hide');
        });

        $('#_btnExportOptionsSave').on('click', function () {
            var btn = $('#_btnExportOptionsSave');
            btn.button('loading');
            var btnClose = $('#btnExportOptionsClose');
            var btnCloseX = $('.ExportOptionsModalClose');
            btnClose.attr('disabled', 'disabled');
            btnCloseX.attr('disabled', 'disabled');
            //Get only the selected options
            const table = $(selfModel.refs['grdExportOptions']).DataTable();
            //const selectedRows = $('#grdExportOptions')
            //                        .DataTable()
            //                        .rows()
            //                        .data()
            //                        .toArray()
            //                        .filter(
            //                            e => GridCommon
            //                            .GetSelectedRows('grdExportOptions')
            //                            .includes(String(e.id))
            //);
            const selectedRows = $('#grdExportOptions')
                .find('input[type="checkbox"]:checked')
                .map((_, el) => {
                    const $checkbox = $(el);
                    const id = Number($checkbox.data('id'));
                    const $row = $checkbox.closest('tr');
                    const rowData = table.row($row).data();
                    return rowData;
                })
                .get();
            function exportCallback()
            {
                if (selfModel.model.action == "Attribute.Export") {
                    Common.mask(document.body, "body-mask");
                    let modalWrapper = $(".modal-window");
                    let modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();
                    //let allPurposes = new Helper().get();
                    var defaultPurposeForExport = window.ToViewPurpose /*allPurposes.filter(p => p.id === 13 || p.text.includes("To View") || p.text.includes("للعرض"))*/;
    
                    modelIndex.purposes = new Helper().getPurpose();
                    modelIndex.transferToType = selfModel.model.transferToType;
                    modelIndex.documentId = selfModel.model.documentId;
                    modelIndex.fromVip = selfModel.model.fromVip;
                    modelIndex.carbonCopy = selfModel.model.carbonCopy;
                    modelIndex.transferId = selfModel.model.transferId;
                    modelIndex.action = "Attribute.Export";
    
                    if (selfModel.model.transferToType == TransferType.BroadcastSend || selfModel.model.transferToType == TransferType.BroadcastComplete) {
                        modelIndex.isBroadcast = true;
                        modelIndex.isDraft = true;
                        modelIndex.broadcastIds = selfModel.model.broadcastIds;
                    }
                    modelIndex.customAttributeDueDate = selfModel.model.customAttributeDueDate;
                    var ExportOptionsModal = {
                        'WithAttachment': $("#chkAttachments").is(":checked"),
                        //'WithEntities': $("#chkEntities").is(":checked"),
                        'WithLinkedCorrespondences': $("#chkLinkedCorrespondences").is(":checked"),
                        'WithNotes': $("#chkNotes").is(":checked"),
                        //'SpecificVersionId': $("input[name='Version']:checked").val(),
                        'withNonArchivedAttachments': $("#chkNonArchivedAttachments").is(":checked"),
    
                    };
                    modelIndex.CopyOptionsModal = ExportOptionsModal;
                    modelIndex.fromResend = selfModel.model.fromResend;
                    modelIndex.incomingDocumentId = selfModel.model.incomingDocumentId;
                    modelIndex.externalReferenceNumber = selfModel.model.referenceNumber;
                    // if (selfModel.model.fromResend) {
                        modelIndex.receivingEntities = selectedRows.filter(e => !e.isExternal && !e.isCC);
                        modelIndex.receivingEntitiesWithCC = selectedRows.filter(e => !e.isExternal /*&& !e.isCC*/);
                    // }
                    $('#modalExportOptions').modal('hide');
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    btnCloseX.removeAttr('disabled');
                    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex,
                        function () {
                            tryCloseModal(selfModel.model.actionsComponentId)
                            if (typeof selfModel.callback === 'function') {
                                selfModel.callback();
                            }
                        } /*self.model.callback*/);
                    if (defaultPurposeForExport.length != 0) {
                        modelIndex.defaultPurposeForExport = defaultPurposeForExport ? defaultPurposeForExport : null;

                        function exportG2GExternal()
                        {
                            if(window.HasG2G)
                            {
                                const selected = selectedRows
                                .filter(x => x.isExternal && x.govCorrespondence)
                                .map(e => ({id: e.id, isCC: e.isCC}))
                                if(selected.length > 0)
                                {
                                    Common.ajaxPostJSON('/Document/ExportToG2G?documentId=' + selfModel.model.documentId, JSON.stringify(
                                    selected
                                    ), function(data)
                                    {
                                        if(!data?.isError)
                                        {
                                            return Common.showScreenSuccessMsg();
                                        }
                                        Common.alertMsg(Resources[data.message] ?? data.message);
                                    }, null, true);
                                }
                            }
                        }
                        sendToReceivingEntityIndexView.sendToReceivingEntitySubmit(exportG2GExternal);
                    }
                    else {
                        if (arr.length > 0) {
                            Common.unmask("body-mask");
                            Common.showConfirmMsg(Resources.ThereAreExternalStructureDoYouWantToContinueExporting, function () {
                                Common.mask(document.body, "body-mask");
                                sendToReceivingEntityIndexView.render();
                            })
                        } else {
                            sendToReceivingEntityIndexView.render();
                        }
                    }
                }
                else {
                    if (window.EnablePerStructure) {
                        var createdByStructureId = $("#hdLoggedInStructureId").val();
    
                    } else {
                        var createdByStructureId = $("#hdStructureId").val();
                        if (sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture") !== null) {
                            createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
                        }
    
                    }
                    var saveParams = {
                        'CategoryId': selfModel.model.categoryId,
                        'CreatedByStructureId': createdByStructureId,
                        'IsCopy': true,
                        'DocumentCopyId': selfModel.model.documentId,
                        'TransferId': selfModel.model.transferId,
                        'DelegationId': selfModel.model.delegationId,
                        'CopyOptionsModal': {
                            'WithAttachment': $("#chkAttachments").is(":checked"),
                            'WithEntities': $("#chkEntities").is(":checked"),
                            'WithLinkedCorrespondences': $("#chkLinkedCorrespondences").is(":checked"),
                            'WithNotes': $("#chkNotes").is(":checked"),
                            'SpecificVersionId': $("input[name='Version']:checked").val()
                        },
                        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    };
                    Common.ajaxPost('/Document/Save', saveParams, function (data) {
                        TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
                        $('#modalExportOptions').modal('hide');
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        window.location.href = "/#document/" + data + (selfModel.model.delegationId ? ("/" + selfModel.model.delegationId) : "");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        if (selfModel.model.categoryId == window.FollowUpCategory) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                        }
                    }, function () {
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        Common.showScreenErrorMsg();
                    });
                }
            }
            if(selectedRows.some(e => e.isExternal && !e.govCorrespondence))
            {
                Common.showConfirmMsg(Resources.ThereAreExternalStructureDoYouWantToContinueExporting, exportCallback, function()
                {
                    $('#modalExportOptions').modal('hide');
                    btn.button('reset');
                    btnClose.removeAttr('disabled');
                    btnCloseX.removeAttr('disabled');
                });
            }
            else
            {
                exportCallback();
            }

        })
    }
}
function format(row) {
    var html = '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr><th style="width: 20%;padding:5px">' + Resources.Comment + ':</th>' +
        '<td style="width: 80%;padding:5px;word-break: break-all;">' + (row.data().comment || '') + '</td>' +
        '</tr>' +
        '</table>';
    return html;
}
export default { ExportOptions, ExportOptionsView };
