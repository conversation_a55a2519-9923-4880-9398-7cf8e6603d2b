import Intalio from './common.js'

class Roles extends Intalio.Model
{
    constructor()
    {
        super();
    }
    get()
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/ListAllRoles', null,
            function (data)
            {
                var options = [];
                for (var i = 0; i < data.length; i++)
                {
                    var option = {};
                    option.id = data[i].id;
                    option.text = data[i].name;
                    options.push(option);
                }
                retValue = options;
            },
            function (msg) { console.log(msg); },
            false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);

        return retValue;
    }
}
var userArray = new Array();
class User extends Intalio.Model
{
    constructor()
    {
        super();
        this.user = null;
        this.users = userArray;
    }
    get(id)
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetUser',
            { id: id },
            function (data)
            {
                if (data)
                {
                    retValue = data.fullName;
                }
            },
            function (msg) { console.log(msg); },
            false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        this.user = retValue;
        return retValue;
    }
    getFullUser(id, language)
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetUser',
            { id: id, language: language },
            function (data)
            {
                if (data)
                {
                    retValue = data;
                }
            },
            function (msg) { console.log(msg); },
            false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        return retValue;
    }
    getWithCaching(id, language)
    {
        let retValue = this.users.find(function (obj)
        {
            return obj.id === id && language === language;
        });
        if (retValue === undefined)
        {
            Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetUser',
                { id: id, language: language },
                function (data)
                {
                    if (data)
                    {
                        var userName = data.fullName;
                        if (data.attributes != null && data.attributes.length > 0)
                        {
                            let firstNameMapping = "", lastNameMapping = "", firstName = "", lastName = "";
                            if (language === "ar")
                            {
                                firstNameMapping = Intalio.parameters.firstNameAr;
                                lastNameMapping = Intalio.parameters.lastNameAr;
                            }
                            else if (language === "fr")
                            {
                                firstNameMapping = Intalio.parameters.firstNameFr;
                                lastNameMapping = Intalio.parameters.lastNameFr;
                            }
                            var firstNameObj = $.grep(data.attributes, function (e)
                            {
                                return e.text === firstNameMapping ? e.value : "";
                            });
                            var lastNameObj = $.grep(data.attributes, function (e)
                            {
                                return e.text === lastNameMapping ? e.value : "";
                            });
                            firstName = firstNameObj.length > 0 ? firstNameObj[0].value : "";
                            lastName = lastNameObj.length > 0 ? lastNameObj[0].value : "";
                            if (firstName !== "" && lastName !== "")
                            {
                                userName = firstName + " " + lastName;
                            }
                        }
                        retValue = { "id": id, "name": userName, "language": language };
                        userArray.push(retValue);
                    } else
                    {
                        retValue = { "id": "", "name": "", "language": language };
                    }
                },
                function (msg) { console.log(msg); },
                false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        }
        return retValue;
    }
}

var userStructuresWithParentArray = new Array();
var structureArray = new Array();
class Structure extends Intalio.Model
{
    constructor()
    {
        super();
        this.userStructuresWithParent = userStructuresWithParentArray;
        this.structures = structureArray;
    }
    get(id)
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetStructure', { id: id },
            function (data)
            {
                if (data)
                {
                    retValue = data.name;
                }
            },
            function (msg) { console.log(msg); }, false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        return retValue;
    }
    getFullStructure(id, language)
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetStructure', { id: id, language: language },
            function (data)
            {
                if (data)
                {
                    retValue = data;
                }
            },
            function (msg) { console.log(msg); }, false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        return retValue;
    }
    getExternalFullStructure(id, language)
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetExternalStructure', { id: id, language: language },
            function (data)
            {
                if (data)
                {
                    retValue = data;
                }
            },
            function (msg) { console.log(msg); }, false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        return retValue;
    }
    getWithCaching(id, language, isExternal)
    {
        if (typeof isExternal === "undefined")
        {
            isExternal = false;
        }
        let retValue = this.structures.find(function (obj)
        {
            return obj.id === id && language === language;
        });
        if (retValue === undefined)
        {
            var data = isExternal ? this.getExternalFullStructure(id, language) : this.getFullStructure(id, language);
            if (data)
            {
                var structureName = data.name;
                if (data.attributes != null && data.attributes.length > 0)
                {
                    var attributeLang = $.grep(data.attributes, function (e)
                    {
                        return e.text === Intalio.parameters.structureNameAr && window.language === "ar" ? e.value : (e.text === Intalio.parameters.structureNameFr && window.language === "fr" ? e.value : "");
                    });
                    if (attributeLang.length > 0)
                    {
                        structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                    }
                }
                retValue = { "id": id, "name": structureName, "language": language };
                structureArray.push(retValue);
            }
        }
        return retValue.name;
    }
    getUserStructuresWithParent(language)
    {
        let retValue = this.userStructuresWithParent;
        if (retValue.length === 0)
        {
            Common.ajaxPostWithHeaders(window.IdentityUrl + '/api/ListStructuresByIdsWithParent', { language: language }, function (data)
            {
                if (data)
                {
                    retValue = data;
                }
            }, null, null, null, { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
            userStructuresWithParentArray = retValue;
        }
        return retValue;
    }
}

var userStructureSendingRuleArray = new Array();
class SendingRules extends Intalio.Model
{
    constructor()
    {
        super();
        this.userStructureSendingRule = userStructureSendingRuleArray;
    }
    get(structureId, delegationId)
    {
        let retValue = this.userStructureSendingRule.find(function (obj)
        {
            return obj.structureId === structureId && obj.delegationId === delegationId;
        });
        if (retValue === undefined)
        {
            retValue = {
                structureId: structureId, delegationId: delegationId, data: this.getData('/SendingRule/GetStructureIdsSendingRules?structureId=' + structureId + '&delegationId=' + delegationId)
            };
            this.userStructureSendingRule.push(retValue);
        }
        return retValue.data;
    }
}

class Group extends Intalio.Model
{
    constructor()
    {
        super();
    }
    get(id)
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetAllGroups', null, function (data)
        {
            if (data)
            {
                retValue = this.data.find(function (obj)
                {
                    return obj.id === id;
                })
            }
        }, function (msg) { console.log(msg); }, false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        return retValue.name;
    }
    list()
    {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/GetAllGroups', null,
            function (data)
            {
                if (data)
                {
                    retValue = data;
                }
            },
            function (msg) { console.log(msg); },
            false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);
        return retValue;
    }
}

var categoriesArray = new Array();
class Categories extends Intalio.Model
{
    constructor()
    {
        super();
        this.categories = categoriesArray;
    }
    get(language)
    {
        let retValue = this.categories.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/SecurityMatrix/ListCategories')
            };
            this.categories.push(retValue);
        }
        return retValue.data;
    }
}

var purposesArray = new Array();
class Purposes extends Intalio.Model
{
    constructor()
    {
        super();
        this.purposes = purposesArray;
    }
    get(language)
    {
        let retValue = this.purposes.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/Purpose/ListPurposes')
            };
            purposesArray.push(retValue);
        }
        return retValue.data;
    }
}

var securityMatrixArray = new Array();
class SecurityMatrix extends Intalio.Model
{
    constructor()
    {
        super();
        this.securityMatrix = securityMatrixArray;
    }
    get(language, delegationId)
    {
        if (delegationId == null) {
            return window.UserSecurityMatrix;
        }

        let retValue = this.securityMatrix.find(function (obj)
        {
            return obj.language === language && obj.delegationId === delegationId;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, delegationId: delegationId, data: this.getData('/SecurityMatrix/List?delegationId=' + delegationId)
            };
            securityMatrixArray.push(retValue);
        }
        return retValue.data;
    }
}

var nodesArray = new Array();
var treeNodesArray = new Array();
class Nodes extends Intalio.Model
{
    constructor()
    {
        super();
        this.nodes = nodesArray;
        this.treeNodes = treeNodesArray;
    }
    get(language)
    {
        let retValue = this.nodes.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/Node/ListNodes')
            };
            nodesArray.push(retValue);
        }
        return retValue.data;
    }
    getTreeNodes(language, delegationId)
    {
        let retValue = this.treeNodes.find(function (obj)
        {
            return obj.language === language && obj.delegationId === delegationId;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, delegationId: delegationId, data: this.getData('/Node/ListTreeNodes?delegationId=' + delegationId)
            };
            treeNodesArray.push(retValue);
        }
        return retValue.data;
    }
}

var importancesArray = new Array();
class Importances extends Intalio.Model
{
    constructor()
    {
        super();
        this.importances = importancesArray;
    }
    get(language)
    {
        let retValue = this.importances.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/Importance/ListImportances')
            };
            importancesArray.push(retValue);
        }
        return retValue.data;
    }
}

var privaciesArray = new Array();
class Privacies extends Intalio.Model
{
    constructor()
    {
        super();
        this.privacies = privaciesArray;
    }
    get(language)
    {
        let retValue = this.privacies.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/Privacy/ListPrivacies')
            };
            privaciesArray.push(retValue);
        }
        return retValue.data;
    }
}

var priorityArray = new Array();
class Priorities extends Intalio.Model
{
    constructor()
    {
        super();
        this.priorities = priorityArray;
    }
    get(language)
    {
        let retValue = this.priorities.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/Priority/ListPriorities')
            };
            priorityArray.push(retValue);
        }
        return retValue.data;
    }
}

var statusesArray = new Array();
var allstatusesArray = new Array();
class Statuses extends Intalio.Model
{
    constructor()
    {
        super();
        this.statuses = statusesArray;
        this.allstatuses = allstatusesArray;//with draft
    }
    get(language)
    {
        let retValue = this.statuses.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined)
        {
            retValue = {
                language: language, data: this.getData('/Status/ListStatuses')
            };
            statusesArray.push(retValue);
        }
        return retValue.data;
    }
    findById(id, language)
    {
        let retValue = this.allstatuses.find(function (obj)
        {
            return obj.language === language;
        });
        if (retValue === undefined || retValue.data.length === 0)
        {
            retValue = {
                language: language, data: this.getData('/Status/ListStatuses')
            };
            allstatusesArray.push(retValue);
        }
        if (retValue.data)
        {
            retValue = retValue.data.find(function (obj) { return obj.id === id; })
        }
        return retValue;
    }
}

var delegationsArray = new Array();
class Delegations extends Intalio.Model
{
    constructor()
    {
        super();
        this.delegations = delegationsArray;
    }
    get(id)
    {
        let retValue = this.delegations.find(function (obj)
        {
            return obj.id === id;
        });
        if (retValue === undefined)
        {
            retValue = {
                id: id, data: this.getData('/Delegation/GetByDelegationId?delegationId=' + id)
            };
            delegationsArray.push(retValue);
        }
        return retValue.data;
    }
}

class SearchRoles extends Intalio.Model {
    constructor() {
        super();
    }
    get() {
        let retValue = null;
        Common.ajaxGetWithHeaders(window.IdentityUrl + '/api/ListAllRoles', null,
            function (data) {
                var options = [];
                for (var i = 0; i < data.length; i++) {
                    var option = {};
                    option.id = data[i].id;
                    option.text = data[i].name;
                    options.push(option);
                }
                retValue = options;
            },
            function (msg) { console.log(msg); },
            false, "", { "Authorization": 'Bearer ' + window.IdentityAccessToken }, false);

        return retValue;
    }
}

export default { Roles, User, Structure, Group, Categories, Nodes, Purposes, Importances, Privacies, Priorities, Statuses, SecurityMatrix, SendingRules, Delegations };
