﻿using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Intalio.CTS.Core.Model
{
    public class DocumentViewModel
    {
        public long? Id { get; set; }
        public long? TransferId { get; set; }

        public string Subject { get; set; }
        public string CategoryName { get; set; }
        public string BasicAttributes { get; set; }
        public string CustomAttributes { get; set; }
        public string CustomAttributesTranslation { get; set; }
        public string FormData { get; set; }
        public List<ReceivingEntityModel> Receivers { get; set; }
        public long? SendingEntityId { get; set; }
        public long? Sender { get; set; }
        public string? Receiver { get; set; }
        public string DueDate { get; set; }
        public string CreatedDate { get; internal set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public List<long> CarbonCopy { get; set; }
        public short? ImportanceId { get; set; }
        public short? ClassificationId { get; set; }
        public short? DocumentTypeId { get; set; }
        public int? TemplateId { get; set; }
        public long FromStructureId { get; set; }
        public FileViewModel ScannedFile { get; set; }
        public ValueText SendingEntity { get; set; }
        public List<ReceivingEntityModel> ReceivingEntities { get; set; }
        public List<ValueText> CarbonCopies { get; set; }
        public ValueText Classification { get; set; }
        public ValueText DocumentType { get; set; }
        public bool Register { get; set; }
        public string Body { get; set; }
        public string ExternalReferenceNumber { get; set; }
        public string Keyword { get; set; }

        [Required]
        [Range(1, int.MaxValue)]
        public short CategoryId { get; set; }
        public long CreatedByStructureId { get; set; }
        public bool? IsLocked { get; set; }
        public bool EnableEdit { get; set; }
        public bool? FullControl { get; set; }
        public bool? AssigneeOperationPermission { get; set; }
        public string DocumentDate { get; set; }
        public short? StatusId { get; set; }
        public bool? IsTaskCreator { get; set; }
        public bool IsExternalSender { get; set; }
        public bool IsExternalReceiver { get; set; }
        public long? G2GInternalId { get; set; }
        public bool EnableAttributeEditSign { get; set; }

        public string instruction { get; set; }
        public long? DelegationId { get; set; }
        public bool IsCopy { get; set; }
        public long? DocumentCopyId { get; set; }
        public long? FollowUpId { get; set; }
        public short PurposeId { get; set; }
        public long StructureId { get; set; }
        public DateTime? TransferDueDate { get; set; }
        public string TransferInstruction { get; set; }
        public byte[] VoiceNote { get; set; }
        public bool VoiceNotePrivacy { get; set; }
        public ReplyType TransferToType { get; set; }
        public List<long> StructureReceivers { get; set; }
        public List<IdCCType> StructureReceiversWithCC { get; set; }
        public bool WithSign { get; set; } = false;
        public long? SignatureTemplateId { get; set; }
        //public ICollection<DocumentReceiverEntity> DocumentReceiverEntity { get; set; }
        public long? RequestStatus { get; set; } = 0;
        public bool? ExportIsOriginal { get; set; } = true;
        public bool? FromResend { get; set; } = false;
        public bool? FromRejectedDocument { get; set; } = false;
        public bool? FromAccept { get; set; } = false;

        public ICollection<ReceivingEntityModel> DocumentCarbonCopy { get; set; }

        public long? ExportedDocumentId { get; set; }

        public HashSet<Note> Note { get; set; }
        public ICollection<NonArchivedAttachments> NonArchivedAttachments { get; set; }
        public HashSet<LinkedDocument> LinkedDocumentDocument { get; set; }
        public HashSet<LinkedDocument> LinkedDocumentLinkedDocumentNavigation { get; set; }
        public HashSet<Attachment> Attachment { get; set; }

        public CopyOptionsModal CopyOptionsModal { get; set; }
        public DocumentForm DocumentForm { get; set; }
        public DocumentReceiverEntity DocumentReceiverEntity { get; set; }


        public DocumentViewModel()
        {
            Receivers = new List<ReceivingEntityModel>();
            ReceivingEntities = new List<ReceivingEntityModel>();
        }
    }
    public class CopyOptionsModal
    {
        public bool WithAttachment { get; set; }
        public bool WithNotes { get; set; }
        public bool WithLinkedCorrespondences { get; set; }
        public bool WithEntities { get; set; }
        public string SpecificVersionId { get; set; }
        public bool WithNonArchivedAttachments { get; set; } = false;
        public bool WithPublicAttachmentsOnly { get; set; } = false;


    }
    public class IdCCType
    {
        public long Id { get; set; }
        public bool IsCC { get; set; }
    }
}