﻿import Intalio from './common.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import DocumentDetails from './documentDetails.js'

import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import VisualTrackingModal from './visualTrackingModal.js'
import VisualTracking from './visualTracking.js'
import ReAssign from './reAssign.js'
class DocumentManageCorrespondence extends Intalio.Model {
    constructor() {
        super();
        this.categories = null;
        this.priorities = null;
        this.statuses = null;
        this.delegationUsers = null;
        this.documentId = null;
        this.fromLink = false;
        this.isSigned = false;
        this.showOCRContentField = window.EnableOCR && (window.CrawlerServerUrl && window.CrawlerServerUrl.split(Splitter).length > 0);
    }
}



var table = null;
var firstTime = true;
var gLocked = false;
var gTableName = "grdSearchItems";
var gFromLink = false;
var gMaxTagsCharCount = 749;
function isFilled() {
    if ($("#cmbSearchFilterCategory").val() === null && $("#cmbSearchFilterStatus").val() === null && $("#txtSearchFilterReferenceNumber").val().trim() === ""
        && $("#searchFilterFromDate").val() === "" && $("#searchFilterToDate").val() === "" && $("#cmbSearchFilterPriority").val() === null && $("#txtSearchFilterSubject").val().trim() === ""
        && $("#cmbSearchFilterDocumentSender").val() === null && $("#cmbSearchFilterDocumentReceiver").val() === null && $("#cmbSearchFilterFromUser").val() === null && $("#cmbSearchFilterToUser").val() === null
        && $("#cmbSearchFilterFromStructure").val() === null && $("#cmbSearchFilterToStructure").val() === null && $("#searchFilterFromTransferDate").val() === "" && $("#searchFilterToTransferDate").val() === ""
        && !$("#chkSearchFilterOverdue").is(":checked") && $("#searchFilterKeyword").val().trim() === "" && ($("#txtSearchOCRContent").val() ? $("#txtSearchOCRContent").val().trim() === "" : true)) {
        return false;
    }
    return true;
}
function search(self) {
    if (firstTime) {
        var columns = [];
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, TreeNodes.Search, gTableName);
        var showCheckBoxes = self.model.fromLink || buttons.length > 0;
        columns.push({
            visible: showCheckBoxes,
            title: '<input id="chkAll" type="checkbox" />',
            width: '16px',
            "orderable": false,
            "render": function (data, type, row) {
                var searchSelectedDocIds = GridCommon.GetSelectedRows(gTableName);
                var index = searchSelectedDocIds.indexOf(row.id);
                if (index > -1) {
                    return "<input type='checkbox' checked=true onclick='event.stopPropagation();' data-id=" + row.id + " />";
                }
                else {
                    return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                }
            }
        });
        

        columns.push({ title: "Id", data: "id", visible: false, "orderable": false });
        columns.push({
            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta) {
                var categories = self.model.categories;
                for (var i = 0; i < categories.length; i++) {
                    if (categories[i].id === data) {
                        return categories[i].text;
                    }
                }

                return "";
            }
        });
        columns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.ToStructure, data: "toStructure", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.FromStructure, data: "fromStructure", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        //columns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.ToUser, data: "toUser", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({
            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta) {
                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
            }
        });
        columns.push({
            title: Resources.Status, data: "statusId", "orderable": false, width: "50px",
            "render": function (data) {
                var statuses = self.model.statuses;
                for (var i = 0; i < statuses.length; i++) {
                    if (statuses[i].id === data) {
                        return "<div class='label' style='background-color:" + (statuses[i].color !== null ? statuses[i].color : "#27c24c") + "'>" + statuses[i].text + "</div>";
                    }
                }
                return "";
            }
        });
       
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("type", "button");
                btnView.setAttribute("clickattr", "openSearchDocument(" + full.documentId + "," + delegationId + "," + self.model.fromManageCorrespondance + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                return btnView.outerHTML;
            }
        });

        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                if (full.openedDate === "" && full.statusId != 3 && !full.isLocked) {
                    
                    let btnUnlock = document.createElement("button");
                    btnUnlock.setAttribute("class", "btn btn-warning btn-xs mr-sm toDraft");
                    btnUnlock.setAttribute("title", Resources.ReturnToDraft);
                    btnUnlock.setAttribute("type", "button");
                    btnUnlock.setAttribute("clickattr", "returnToDraft(" + full.documentId + ")");
                    btnUnlock.innerHTML = "<i class='fa fa-repeat fa-white'/>";
                    return btnUnlock.outerHTML;
                } else {
                    return null;
                }
                
            }
        });

        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                if (full.statusId != 3) {

                    let btnCancel = document.createElement("button");
                    btnCancel.setAttribute("class", "btn btn-danger btn-xs mr-sm CancelTransfer");
                    btnCancel.setAttribute("title", Resources.Cancel);
                    btnCancel.setAttribute("type", "button");
                    btnCancel.setAttribute("clickattr", "CancelTransfer(" + full.id + "," + delegationId + ")");
                    btnCancel.innerHTML = "<i class='fa fa-ban fa-white'/>";
                    return btnCancel.outerHTML;
                } else {
                    return null;
                }
               
            }
        });

        
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                if (full.toUserId != null && full.statusId != 3) {
                    if (full.workflowStepId == null) {
                   
                        let btnReAssign = document.createElement("button");
                        btnReAssign.setAttribute("class", "btn btn-success btn-xs mr-sm showReassignButton");
                        btnReAssign.setAttribute("title", Resources.ReAssign);
                        btnReAssign.setAttribute("type", "button");
                        btnReAssign.setAttribute("clickattr", "reAssign(" + full.id + "," + delegationId + "," + full.toUserId + "," + full.toStructureId + "," + full.privacyId + ")");
                        btnReAssign.innerHTML = "<i class='fa fa-user'/>";
                        return btnReAssign.outerHTML;
                    }
                    return null;

                 
                }
                else {
                    return null;
                }
            }
        });
   
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                if (full.isLocked && full.statusId != 3 ) {
                    let btnUnlock = document.createElement("button");
                    btnUnlock.setAttribute("class", "btn btn-success btn-xs mr-sm showUnlockButton");
                    btnUnlock.setAttribute("title", Resources.Unlock);
                    btnUnlock.setAttribute("type", "button");
                    btnUnlock.setAttribute("clickattr", "unlock(" + full.id + "," + delegationId + ")");
                    btnUnlock.innerHTML = "<i class='fa fa-unlock'/>";
                    return btnUnlock.outerHTML;
                }
                else {
                    return null;
                }
            }
        });
        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var statuses = self.model.statuses;
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                if (full.documentStatusId == 3) {
                    let btnView = document.createElement("button");
                    btnView.setAttribute("class", "btn btn-xs btn-danger mr-sm UnArchive");
                    btnView.setAttribute("title", Resources.UnArchive);
                    btnView.setAttribute("type", "button");
                    btnView.setAttribute("clickattr", "UnArchive(" + full.id + ")");
                    btnView.innerHTML = "<i class='fa fa-lock'/>";
                    return btnView.outerHTML;
                }
                else {
                    return null;
                }
            }
        });

        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var statuses = self.model.statuses;
                var delegationId = null;
                if (full.cced)
 { 
                    let btncced = document.createElement("button");
                    btncced.setAttribute("class", "btn btn-xs mr-sm");
                    btncced.setAttribute("style","background-color:transparent")
                    btncced.setAttribute("title", Resources.CarbonCopy);
                    btncced.setAttribute("type", "button");
                    btncced.innerHTML = "<i class='fa fa-cc text-warning'></i>&nbsp;";
                    return btncced.outerHTML;
                }

               
                else {
                    return null;
                }
            }
        });

        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (full.isOverDue) {
                    let btnoverdue = document.createElement("button");
                    btnoverdue.setAttribute("class", "btn btn-xs mr-sm");
                    btnoverdue.setAttribute("style", "background-color:transparent")
                    btnoverdue.setAttribute("title", Resources.OverDue);
                    btnoverdue.setAttribute("type", "button");
                    btnoverdue.innerHTML = "<i class='fa fa-clock-o fa-lg text-danger'>";
                    return btnoverdue.outerHTML;
                }

                else {
                    return null;
                }
            }
        });

        if (!gFromLink) {
            var row = SecurityMatrix.getRowActions(securityMatrix, columns, TreeNodes.Search);
            const editBtn = columns.find(e => e.actionName == "Edit");
            if(editBtn)
            {
                const defaultContent = editBtn.defaultContent;
                editBtn.defaultContent = undefined;
                editBtn.data = null;
                editBtn.render = function(data, type, full, meta)
                {
                    if(full.statusId !== SystemStatus.Completed && !full.isSigned)
                    {
                        return defaultContent;
                    }
                    return "";
                }
            }
        }
        table = $("#grdSearchItems")
            .on('draw.dt', function () {
                $('#grdSearchItems tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Search/ManageCorespondence",
                    "type": "POST",
                    "datatype": "json",
                    "data": function (d) {
                        var model = {};
                        model.documentId = self.model.documentId;
                        model.category = $("#cmbSearchFilterCategory").val();
                        model.status = $("#cmbSearchFilterStatus").val();
                        model.referenceNumber = $("#txtSearchFilterReferenceNumber").val().trim() !== "" && typeof $("#txtSearchFilterReferenceNumber").val() !== "undefined" ? $("#txtSearchFilterReferenceNumber").val().trim() : "";
                        model.fromDate = $("#searchFilterFromDate").val() !== "" && typeof $("#searchFilterFromDate").val() !== "undefined" ? $("#searchFilterFromDate").val() : "";
                        model.toDate = $("#searchFilterToDate").val() !== "" && typeof $("#searchFilterToDate").val() !== "undefined" ? $("#searchFilterToDate").val() : "";
                        model.priority = $("#cmbSearchFilterPriority").val();
                        model.documentSender = $("#cmbSearchFilterDocumentSender").val();
                        model.documentReceiver = $("#cmbSearchFilterDocumentReceiver").val();
                        model.fromUser = $("#cmbSearchFilterFromUser").val();
                        model.toUser = $("#cmbSearchFilterToUser").val();
                        model.fromStructure = $("#cmbSearchFilterFromStructure").val();
                        model.toStructure = $("#cmbSearchFilterToStructure").val();
                        model.fromTransferDate = $("#searchFilterFromTransferDate").val() !== "" && typeof $("#searchFilterFromTransferDate").val() !== "undefined" ? $("#searchFilterFromTransferDate").val() : "";
                        model.toTransferDate = $("#searchFilterToTransferDate").val() !== "" && typeof $("#searchFilterToTransferDate").val() !== "undefined" ? $("#searchFilterToTransferDate").val() : "";
                        model.keyword = $("#searchFilterKeyword").val().trim() !== "" && typeof $("#searchFilterKeyword").val() !== "undefined" ? $("#searchFilterKeyword").val().trim() : "";
                        if (self.model.delegationUsers.length > 0) {
                            model.delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                        }

                        model.referenceNumber = $("#txtSearchFilterReferenceNumber").val().trim() !== "" && typeof $("#txtSearchFilterReferenceNumber").val() !== "undefined" ? $("#txtSearchFilterReferenceNumber").val().trim() : "";
                        model.subject = $("#txtSearchFilterSubject").val().trim() !== "" && typeof $("#txtSearchFilterSubject").val() !== "undefined" ? $("#txtSearchFilterSubject").val().trim() : "";
                        model.isOverdue = $("#chkSearchFilterOverdue").is(":checked");
                        if (self.model.showOCRContentField) {
                            model.ocrContent = $("#txtSearchOCRContent").val();
                        }
                        if (self.model.delegationUsers.length > 0) {
                            model.delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                        }
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.Model = JSON.stringify(model);
                        return d;
                    },
                    "dataSrc": function (response) {

                        $('#grdSearchItems_processing').css('display', 'none');
                        if (response.recordsTotal > 0) {
                            return response.data;
                        } else {
                            if (response.message != undefined && response.message != "") {
                                Common.showScreenErrorMsg(response.message);
                            }
                            response.data = [] //since datatables will be checking for the object as array
                            return response.data;
                        }
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json) {
                    gLocked = false;
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>trpi',
                buttons: !gFromLink ? buttons : []
            });
        if (showCheckBoxes) {
            GridCommon.AddCheckBoxEvents(gTableName);
        }
        $('#grdSearchItems tbody').on('click', ".view", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', ".showUnlockButton", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', ".showReassignButton", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', ".toDraft", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', ".CancelTransfer", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('dblclick', 'tr', function () {
            var onclick = $(this).find(".view").attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', ".UnArchive", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row.data(), self.model.importances, self.model.priorities, self.model.privacies)).show();
                tr.addClass('shown');
            }
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        if (!gFromLink) {
            SecurityMatrix.InitToolbarColor();
            SecurityMatrix.InitContextMenu(securityMatrix, TreeNodes.Search);
        }
    } else {
        GridCommon.Refresh(gTableName);
        gLocked = false;
    }
    firstTime = false;
}
function format(row, importances, priorities, privacies) {
    var importance = "", priority = "", privacy = "";
    for (let i = 0; i < priorities.length; i++) {
        if (priorities[i].id === row.priorityId) {
            priority = priorities[i].text;
        }
    }
    for (let i = 0; i < privacies.length; i++) {
        if (privacies[i].id === row.privacyId) {
            privacy = privacies[i].text;
        }
    }
    for (let i = 0; i < importances.length; i++) {
        if (importances[i].id === row.importanceId) {
            importance = importances[i].text;
        }
    }
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.sendingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.receivingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Priority + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Importance + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + importance + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.createdByUser || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.DueDate + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.dueDate, null, window.CalendarType) || '') + '</td>' +
        '</tr>' +
        '</table>';
}
function openSearchDocument(id, delegationId, fromLink) {

    if (true) {
        var params = { id: id };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocumentEdit', params, function (response) {
       
            if (response && response === "NoAccess") {
                Common.alertMsg(Resources.NoPermission);
            } else {
                if (!response.id) {
                    return;
                }
                var wrapper = $(".modal-window");
                var model = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                model.documentId = response.id;
                model.reference = response.referenceNumber;
                model.subject = response.subject;
                var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, model);
                linkedCorrespondenceDocument.render();
                
                model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                model.readonly = true;
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.sendingEntity = response.sendingEntity;
                model.receivingEntities = response.receivingEntities;
                model.createdByUser = response.createdByUser;
                model.attachmentVersion = response.attachmentVersion;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.showVisualTrackingOnly = false;
                model.fromManageCorrespondance = fromLink;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                var tabs = [];
                var nodeId = /*$('[data-inherit="' + TreeNode.Inbox + '"]').first().data("id")*/ TreeNodes.Search;
                if (nodeId !== undefined && $.isNumeric(nodeId)) {
                    tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                    model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
                }
                model.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("notes") &&
                        !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory") && !element.Name.includes("attachments");
                });
                model.tabsWithStatic = tabs;
                model.showBackButton = false;
                model.isModal = true;
                model.attachmentId = response.attachmentId;
                wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
                var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();
                $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');

                var title = response.categoryName;
                if (response.referenceNumber) {
                    title += ' - ' + response.referenceNumber;
                }
                if (response.createdByUser) {
                    title += ' - ' + response.createdByUser;
                }
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocumentTitle']).html(title);
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                    $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                    if ($(this).data("remove") != true)
                        return;
                    $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                    swal.close();
                    //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                    //    $('body').addClass('modal-open');
                    //}
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    else {
        var params = { id: id };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
            if (response && response === "NoAccess") {
                Common.alertMsg(Resources.NoPermission);
            } else {
                Common.setActiveSidebarMenu("liSearch");
                $(".delegation").removeClass("active");
                $("#searchContainerDiv").hide();
                
                var model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.documentModel = response;
                model.readonly = true;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.fromSearch = true;
                model.attachmentId = response.attachmentId;
                model.attachmentVersion = response.attachmentVersion;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                var tabs = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].Tabs;
                model.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                        !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
                });
                model.tabsWithStatic = tabs;
                model.tabsActions = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].SecurityTabs;
                var wrapper = $(".content-wrapper");
                var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();
                $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');
                if (!gFromLink) {
                    $(document).off('click', '.btn-back');
                    $(document).on('click', '.btn-back', function () {
                        $("#searchContainerDiv").show();
                        view.remove();
                        $(".toRemove").remove();
                    });

                    $(document).off('click', '.btn-export');
                    $(document).on('click', '.btn-export', function () {
                        var wrapper = $(".modal-window");
                        var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                        model.documentId = response.id;
                        model.delegationId = delegationId;
                        var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                        reportCorrespondenceDetailExportView.render();

                        $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function () { });
                        $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function () { });
                        $("#modalReportCorrespondenceDetailExport").modal("show");
                    });
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    //var wrapper = $(".modal-window");
    //var model = new VisualTracking.VisualTracking();
    //var VisualTrackingModalviews = new VisualTrackingModal.VisualTrackingModalView(wrapper, model);
    //VisualTrackingModalviews.render();

    //model = new VisualTracking.VisualTracking();
    //model.documentId = id;
    //model.delegationId = delegationId;


    //wrapper = $(VisualTrackingModalviews.refs['searchAll_visualTrackingDiv']);
    //var _visualTrackingView = new VisualTracking.VisualTrackingView(wrapper, model);
    //_visualTrackingView.render();

    //$(VisualTrackingModalviews.refs['visualTrackingPopup']).off("hidden.bs.modal");
    //$(VisualTrackingModalviews.refs['visualTrackingPopup']).off("shown.bs.modal");
    //$(VisualTrackingModalviews.refs['visualTrackingPopup']).on('shown.bs.modal', function () {
    //});
    //$(VisualTrackingModalviews.refs['visualTrackingPopup']).on('hidden.bs.modal', function () {
    //    $(linkedCorrespondenceDocument.refs[VisualTrackingModalviews.model.ComponentId]).remove();
    //    swal.close();
    //    if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
    //        $('body').addClass('modal-open');
    //    }
    //});

    //$(VisualTrackingModalviews.refs['visualTrackingPopup']).modal("show");

}

function createUsersSelect2() {
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchUsers';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbSearchFilterFromUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#fromUserSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                //return { "text": "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term) {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbSearchFilterFromUser").val('').trigger('change');
    $("#cmbSearchFilterToUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#toUserSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                //return { "text": "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term) {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbSearchFilterToUser").val('').trigger('change');
}
function userDataForSelect2(data, term) {
    var termSearch = term.term ? term.term : "";
    var retVal = [];
    $.each(data, function (key, val) {

        var fullName = val.fullName;
        if (window.language != 'en') {
            fullName = getFullNameByLangauge(val);
            fullName = fullName.trim() == "" ? val.fullName : fullName;
        }
        var allNames = getFullNameInAllLangauge(val);
        if (allNames.length == 0) allNames.push(fullName);
        if (termSearch != "" &&
            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
            return;
        }
        let isExist = retVal.some(function (usr) { return usr.id === val.id; });
        if (!isExist) {
            var item = {};
            item.id = val.id;
            item.text = fullName;
            item.isStructure = false;
            item.dataId = val.id;
            retVal.push(item);
        }
    });
    return retVal;
}
function createStructuresSelect2() {
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbSearchFilterDocumentSender").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#documentSenderSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data) {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    $("#cmbSearchFilterDocumentSender").val('').trigger('change');
    $("#cmbSearchFilterDocumentReceiver").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#documentReceiverSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data) {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    $("#cmbSearchFilterDocumentReceiver").val('').trigger('change');
    $("#cmbSearchFilterFromStructure").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#fromStructureSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data) {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    $("#cmbSearchFilterFromStructure").val('').trigger('change');
    $("#cmbSearchFilterToStructure").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#toStructureSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data) {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    $("#cmbSearchFilterToStructure").val('').trigger('change');

}
function getStructureName(data) {
    var structureName = data.name;
    if (data.attributes != null && data.attributes.length > 0) {
        var attributeLang = $.grep(data.attributes, function (e) {
            return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
        });
        if (attributeLang.length > 0) {
            structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
        }
    }
    return structureName;
}
function structureDataForSelect2(data) {
    var retVal = [];
    if (typeof data !== 'undefined' && data.items) {
        for (var i = 0; i < data.items.length; i++) {
            retVal.push({
                id: data.items[i].id,
                text: getStructureName(data.items[i])
            });
        }
    } else if (data) {
        for (var i = 0; i < data.length; i++) {
            retVal.push({
                id: data[i].id,
                text: getStructureName(data[i])
            });
        }
    }
    return retVal;
}
function unlock(id, delegationId) {
    Common.showConfirmMsg(Resources.UnlockConfirmation, function () {

        var params = { "Id": id };
        if (delegationId !== null) {
            params.DelegationId = delegationId;
        }
        Common.ajaxPost('/Transfer/UnLock', params, function (response) {
            if (response === "False") {
                Common.showScreenErrorMsg();
            }
            else if (response === "FileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);
            } else {
                search(self);
            }
            gLocked = false;
        });
    }, function () { gLocked = false; });
}
function recall(id, delegationId) {
    Common.showConfirmMsg(Resources.RecallConfirmation, function () {
        Common.ajaxPost('/Transfer/Recall',
            {
                'id': id, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function () {
                gLocked = false;
                swal.close()
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();
                if (gSelectedRowId === id) {
                    $(".withBorders-o").addClass("waitingBackground");
                    $("#sentDocumentDetailsContainer").empty();
                }
            }, function () { gLocked = false; Common.showScreenErrorMsg(); }, false);
    }, function () { gLocked = false; });
}

function returnToDraft(documentId) {

    Common.showConfirmMsg(Resources.ReturnToDraftConfirmation, function () {

        var params = { "id": documentId };
        Common.ajaxGet('/Document/CheckDocumentAttachmentLocked/', { "documentId": documentId }, function (retval) {
            if (!retval) {
                Common.ajaxPost('/Document/ReturnToDraft', params, function (response) {
                    if (!response?.success) {
                        response.message && response.message != "" ? Common.alertMsg(response.message) : common.showScreenErrorMsg();
                    }
                    else {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        search(self);
                    }
                    gLocked = false;
                });
            } else {
                gLocked = false;
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);            }
        });
    }, function () { gLocked = false; });

}

function CancelTransfer(TransferId, delegationId) {

    Common.showConfirmMsg(Resources.CancelConfirmation, function () {
        Common.ajaxPost('/Transfer/Cancel',
            {
                'id': TransferId, 'delegationId': delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            function () {
                gLocked = false;
                TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                search(self);
            }, function () { gLocked = false; Common.showScreenErrorMsg(); }, false);
    }, function () { gLocked = false; });

}

function reAssign(transferId, delegationId, toUserId, toStructureId, documentPrivacyId) {

    var model = new ReAssign.ReAssign();
    var structurIds = [];
    structurIds.push(toStructureId);
    model.structureIds = structurIds;
    model.transferId = transferId;
    model.toUserId = toUserId;
    model.documentPrivacyId = documentPrivacyId;
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    let wrapper = $(".modal-window");
    wrapper.empty();
    let view = new ReAssign.ReAssignView(wrapper, model);
    view.render();

    $(".modalReassign").off("hidden.bs.modal");
    $(".modalReassign").off("shown.bs.modal");
    $(".modalReassign").on('shown.bs.modal', function () { });
    $(".modalReassign").on('hidden.bs.modal', function () { });
    $(".modalReassign").modal("show");
    
}
function UnArchive(transferId) {
    Common.showConfirmMsg(Resources.UnArchiveConfirmation, function () {

        var params = { 'id': transferId };
        Common.ajaxPost('/Transfer/UnArchive?transferId=' + transferId, null, function () {
            gLocked = false;
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
            search(self);
        }, function () { gLocked = false; Common.showScreenErrorMsg(); }, false);
    }, function () { gLocked = false; });


}

class DocumentManageCorrespondenceView extends Intalio.View {
    constructor(element, model) {
        super(element, "managecorrespondence", model);
    }
    render() {
        var self = this;
        gFromLink = self.model.fromLink;
        $.fn.select2.defaults.set("theme", "bootstrap");
        firstTime = true;
       
     
   

        $('#formPost').keydown(function (e) {
            if (!$(e.target).parent().hasClass('bootstrap-tagsinput')) {
                var code = e.keyCode || e.which;
                if (code === 13) {
                    e.preventDefault();
                    $('#btnSearchFilter').trigger("click");
                }
            }
        });
        $('#txtSearchFilterReferenceNumber').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 13) {
                e.preventDefault();
                $('#btnSearchFilter').trigger("click");
            }
        });
        $("#btnSearchFilter").on('click', function (e) {
            if (isFilled()) {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        $("#grdSearchItems tbody").empty();
                        search(self);
                        $('.search').hide();
                        $("#expandIcon").show();
                        $('.btn-scroll').fadeIn();
                        $('.gridResult').fadeIn();
                        $("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                        $("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
                    } catch (e) {
                        gLocked = false;
                    }
                }
            } else {
                Common.alertMsg(Resources.FillSearchCritirea);
            }
        });
        $("#btnSearchFilterClear").on('click', function () {
            $("#cmbSearchFilterCategory").val('').trigger('change');
            $("#cmbSearchFilterStatus").val('').trigger('change');
            $("#cmbSearchFilterPriority").val('').trigger('change');
            $("#cmbSearchFilterDocumentSender").val('').trigger('change');
            $("#cmbSearchFilterDocumentReceiver").val('').trigger('change');
            $("#cmbSearchDocumentReceiver").val('').trigger('change');
            $("#cmbSearchFilterFromUser").val('').trigger('change');
            $("#cmbSearchFilterToUser").val('').trigger('change');
            $("#cmbSearchFilterFromStructure").val('').trigger('change');
            $("#cmbSearchFilterToStructure").val('').trigger('change');
            $("#txtSearchFilterReferenceNumber").val('');
            $("#txtSearchFilterSubject").val('');
            $("#searchFilterFromDate").val('').trigger('change');
            $("#searchFilterToDate").val('').trigger('change');
            $("#searchFilterFromTransferDate").val('').trigger('change');
            $("#searchFilterToTransferDate").val('').trigger('change');
            $("#chkSearchFilterOverdue").prop("checked", false);
            if (self.model.showOCRContentField) {
                $("#txtSearchOCRContent").val('');
            }
            $("#searchFilterKeyword").val('')
            if (self.model.delegationUsers.length > 0) {
                $("#cmbSearchFilterDelegation").val('').trigger('change');
            }
            $('.with-border').addClass('without-border').removeClass('with-border');
            $("#expandIcon").hide();
            $("#grdSearchItems tbody").empty();
        });
        createUsersSelect2();
        createStructuresSelect2();

        $("#cmbSearchFilterCategory").val('').trigger('change');
        $('#cmbSearchFilterCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#categorySearchFilterContainer')
        });
        $('#cmbSearchFilterStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#statusSearchFilterContainer')
        });
        $("#cmbSearchFilterStatus").val('').trigger('change');
        $('#cmbSearchFilterPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#prioritySearchFilterContainer')
        });
        $("#cmbSearchFilterPriority").val('').trigger('change');
        var searchFromTransferDate = $('#searchFilterFromTransferDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#searchFilterToTransferDate').val() && jQuery('#searchFilterToTransferDate').val() !== "" ? jQuery('#searchFilterToTransferDate').val() : false
                });
                if (!(jQuery('#searchFilterFromTransferDate').val() && jQuery('#searchFilterFromTransferDate').val() !== "")) {
                    this.set({
                        maxDate: moment().format('DD/MM/YYYY')
                    });
                }
            }
        });
        $('#grdSearchItems tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row.data(), self.model.importances, self.model.priorities, self.model.privacies)).show();
                tr.addClass('shown');
            }
        });
        $("#searchFilterFromTransferDate_img").click(function () {
            searchFromTransferDate.toggle();
        });
        var searchToTransferDate = $('#searchFilterToTransferDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#searchFilterFromTransferDate').val() && jQuery('#searchFilterFromTransferDate').val() !== "" ? jQuery('#searchFilterFromTransferDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#searchFilterToTransferDate_img").click(function () {
            searchToTransferDate.toggle();
        });
        var searchFromDate = $('#searchFilterFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#searchFilterToDate').val() && jQuery('#searchFilterToDate').val() !== "" ? jQuery('#searchFilterToDate').val() : false
                });
                if (!(jQuery('#searchFilterFromDate').val() && jQuery('#searchFilterFromDate').val() !== "")) {
                    this.set({
                        maxDate: moment().format('DD/MM/YYYY')
                    });
                }
            }
        });
        $("#searchFilterFromDate_img").click(function () {
            searchFromDate.toggle();
        });
        var searchToDate = $('#searchFilterToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#searchFilterFromDate').val() && jQuery('#searchFilterFromDate').val() !== "" ? jQuery('#searchFilterFromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#searchFilterToDate_img").click(function () {
            searchToDate.toggle();
        });
        if (self.model.delegationUsers.length > 0) {
            $('#cmbSearchFilterDelegation').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: false,
                placeholder: "",
                dropdownParent: $('#delegationSearchFilterContainer')
            });
        }

        $('#btnSearchFilterClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSearchFilter').focus();
                }
                else {
                    $('#txtSearchFilterReferenceNumber').focus();
                }
            }
        });
        $('.btn-scroll').on('click', function () {
            if ($('.gridResult').is(":visible")) {
                $('.search').fadeIn();
                $('.gridResult').hide();
                $("#expandIcon").removeClass("fa-expand").addClass("fa-compress");
                $("#resultExpandIcon").removeClass("fa-expand").addClass("fa-compress");
            } else {
                $('.gridResult').fadeIn();
                $('.search').hide();
                $("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                $("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
            }
        });
        $('span[aria-labelledby=select2-cmbSearchFilterCategory-container]').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSearchFilterClear').focus();
                }
                else {
                    $('#txtSearchFilterReferenceNumber').focus();
                }
            }
        });
        $('#btnSearchFilterClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSearchFilter').focus();
                }
                else {
                    $('#cmbSearchFilterCategory').focus();
                }
            }
        });
   
        $('#cmbSearchFilterCategory').focus();

        if (self.model.fromLink) {
            $('.panel.panel-default').addClass('borderTop-1');
        }

    }
}
export default { DocumentManageCorrespondence, DocumentManageCorrespondenceView };
