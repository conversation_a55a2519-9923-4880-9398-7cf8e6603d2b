﻿IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'TransferOpenedByUser')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (
        N'TransferOpenedByUser',
        N'This correspondence has an open transfer by another user.',
        N'Cette correspondance a un transfert ouvert par un autre utilisateur.',
        N'هذه المراسلة لديها تحويل مفتوح من قبل مستخدم آخر.',
        1
    )
END

-- for migration
ALTER TABLE Basket
ADD StructureId BIGINT NULL;
GO


IF NOT EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
	INSERT INTO Parameter (Keyword,[Description], Content, IsSystem)
	VALUES (N'RCVersion','RC version of CTS', 'RC35', 0)
END

IF EXISTS (SELECT 1 FROM Parameter WHERE Keyword=N'RCVersion')
BEGIN
update Parameter set Content='RC35' where Keyword='RCVersion'

END