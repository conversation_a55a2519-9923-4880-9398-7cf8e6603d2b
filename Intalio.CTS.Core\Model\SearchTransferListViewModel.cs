﻿
namespace Intalio.CTS.Core.Model
{
    public class SearchTransferListViewModel
    {
        public long Id { get; set; }
        public long DocumentId { get; set; }
        public short CategoryId { get; set; }
        public string ReferenceNumber { get; set; }
        public string Subject { get; set; }
        public string CreatedDate { get; set; }
        public int StatusId { get; set; }
        public short? PriorityId { get; set; }
        public short? PrivacyId { get; set; }
        public short? ImportanceId { get; set; }
        public string SendingEntity { get; set; }
        public string ReceivingEntity { get; set; }
        public string CreatedByUser { get; set; }
        public string DueDate { get; set; }
        public bool IsLocked { get; set; }
        public long? ToStructureId { get; set; }
        public string ToStructure { get; set; }
        public string FromStructure { get; set; }
        public long? ToUserId { get; set; }
        public string ToUser { get; set; }
        public long DocumentStatusId { get; set; }
        public string DocumentCarbonCopy { get; set; }
        public bool IsOverDue { get; set; }
        public long? WorkflowStepId { get; set; }
        public bool? IsSigned { get; set; }
        public bool Cced { get; set; }
        public string OpenedDate { get; set; }



    }
}
