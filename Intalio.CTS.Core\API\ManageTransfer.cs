﻿using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.Core.Utility;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Threading.Tasks;
using System.Net;

using System.IO;
using System.Text;
using AngleSharp.Text;
using Aspose.Pdf.Text;
using StorageAttachmentModel = Intalio.Core.Model.StorageAttachmentModel;
using Newtonsoft.Json.Linq;
using Aspose.Words.Layout;
using System.Diagnostics;
using static iTextSharp.text.pdf.AcroFields;
using Microsoft.AspNetCore.SignalR;
using Serilog;
using Microsoft.AspNetCore.Razor.TagHelpers;



namespace Intalio.CTS.Core.API
{
    public static class ManageTransfer
    {
        #region Internal Methods

        /// <summary>
        /// Close transfer by id
        /// </summary>
        /// <param name="id"></param>
        internal static void Close(long id)
        {
            var transfer = new Transfer().Find(id);
            if (transfer != null && transfer.ClosedDate == null)
            {
                transfer.StatusId = (short)DocumentStatus.Completed;
                transfer.ClosedDate = DateTime.Now;
                transfer.UpdateStatusAndClosedDate();
            }
        }

        internal static void StartSigning(long documentId, long? transferId)
        {
            if (transferId != null && transferId!= 0)
            {
                var transfer = new Transfer().Find(transferId.Value);
                if (transfer != null && transfer.ClosedDate == null)
                {
                    transfer.StatusId = (short)DocumentStatus.Completed;
                    transfer.ClosedDate = DateTime.Now;
                    transfer.IsSigning = true;
                    transfer.UpdateStatusAndClosedDate();
                }
            }
            else
            {
                
            }

        }

        internal static void EndSigning(long documentId, long? transferId)
        {
            if (transferId != null && transferId != 0)
            {
                var transfer = new Transfer().Find(transferId.Value);
                if (transfer != null && transfer.ClosedDate != null)
                {
                    transfer.StatusId = (short)DocumentStatus.InProgress;
                    transfer.ClosedDate = null;
                    transfer.IsSigning = false;
                    transfer.UpdateStatusAndClosedDate();
                }
            }
            else
            {

            }
        }

        internal static void StartExporting(long documentId, long? transferId)
        {
            if (transferId != null && transferId != 0)
            {
                var transfer = new Transfer().Find(transferId.Value);
                if (transfer != null && transfer.ClosedDate == null)
                {
                    transfer.StatusId = (short)DocumentStatus.Completed;
                    transfer.ClosedDate = DateTime.Now;
                    transfer.IsExporting = true;
                    transfer.UpdateStatusAndClosedDate();
                }
            }
        }

        internal static void EndExporting(long documentId, long? transferId, bool exportStatus)
        {
            if (transferId != null && transferId != 0)
            {
                var transfer = new Transfer().Find(transferId.Value);
                if (transfer != null && transfer.ClosedDate != null)
                {
                    if (!exportStatus)
                    {
                        transfer.StatusId = (short)DocumentStatus.InProgress;
                        transfer.ClosedDate = null;
                    }
                    transfer.IsExporting = false;
                    transfer.UpdateStatusAndClosedDate();
                }
            }
        }

        internal static async Task<bool> Create(long userId, List<TransferModel> transfers, long? FromDelegatedUserId, bool closeParentTransfer = false, bool updateDocumentStatus = false ,Intalio.Core.Language Language = Intalio.Core.Language.EN)
        {
            bool retValue = false;
            List<Transfer> transferList = new List<Transfer>();
            List<long> structureIds = new List<long>();
            List<long> userIds = new List<long>();
            List<long> parentTransfersIds = new List<long>();
            List<long> documentsIds = new List<long>();
            List<ValueText> documentCced = new List<ValueText>();

            foreach (var transfer in transfers)
            {
                if (transfer.FromStructureId.HasValue)
                {
                    if (!structureIds.Contains(transfer.FromStructureId.Value))
                    {
                        structureIds.Add(transfer.FromStructureId.Value);
                    }
                }
                if (transfer.ToStructureId.HasValue)
                {
                    if (!structureIds.Contains(transfer.ToStructureId.Value))
                    {
                        structureIds.Add(transfer.ToStructureId.Value);
                    }
                }
                if (transfer.ToUserId.HasValue)
                {
                    if (!userIds.Contains(transfer.ToUserId.Value))
                    {
                        userIds.Add(transfer.ToUserId.Value);
                    }
                }
                DateTime? dueDate = null;
                if (!string.IsNullOrEmpty(transfer.DueDate))
                {
                    dueDate = DateTime.Parse(transfer.DueDate);
                }
                long? VoiceNoteStorageId = null;
                if (transfer.VoiceNote != null)
                {
                    var file = new FileViewModel()
                    {
                        Name = DateTime.Now.GetCurrentTimeStamp().ToString(),
                        FileName = DateTime.Now.GetCurrentTimeStamp().ToString(),
                        FileSize = transfer.VoiceNote.Length,
                        ContentType = "audio/wav",
                        Data = transfer.VoiceNote,
                        Extension = ".wav"
                    };
                    var data = SetFileFormData(file, null, transfer.DocumentId.ToString(), null);
                    var StorageretValue = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/Upload", Configuration.IdentityAccessToken, data);
                    if (StorageretValue.Success)
                    {
                        VoiceNoteStorageId = Convert.ToInt64(StorageretValue.Result);
                    }
                }
                transferList.Add(new Transfer
                {
                    DocumentId = transfer.DocumentId,
                    StatusId = (short)DocumentStatus.InProgress,
                    PurposeId = transfer.PurposeId,
                    FromStructureId = transfer.FromStructureId,
                    FromUserId = userId,
                    ToStructureId = transfer.ToStructureId,
                    ParentTransferId = transfer.ParentTransferId,
                    ToUserId = transfer.ToUserId,
                    CreatedByUserId = userId,
                    DueDate = dueDate,
                    Cced = transfer.CCed,
                    Instruction = transfer.Instruction,
                    VoiceNoteStorageId = VoiceNoteStorageId,
                    VoiceNotePrivacy = transfer.VoiceNotePrivacy,
                    WorkflowStepId = transfer.WorkflowStepId,
                    FromDelegatedUserId = FromDelegatedUserId,
                    PrivateInstruction = transfer.PrivateInstruction,
                    PriorityId = transfer.PriorityId,
                    FromRecall = transfer.FromRecall
                });

                if (closeParentTransfer && transfer.ParentTransferId.HasValue)
                {
                    parentTransfersIds.Add(transfer.ParentTransferId.Value);
                }

                if (updateDocumentStatus)
                {
                    if (!documentCced.Any(t => t.Id == transfer.DocumentId))
                    {
                        documentCced.Add(new ValueText { Id = transfer.DocumentId, Text = "true" });
                    }

                    if (!transfer.CCed)
                    {
                        documentCced.Find(t => t.Id == transfer.DocumentId).Text = "false";
                    }
                }

                //Add to audit trail
                var note = "";

                if (transfer.ToUserId != null)
                {
                    var userName = IdentityHelperExtension.GetFullName(transfer.ToUserId.Value, Language.EN);
                    note += TranslationUtility.Translate("Transferdtouser", Language) + " : " + userName;

                }
                if (transfer.ToStructureId != null)
                {
                    if (transfer.ToUserId != null)
                        note +=" " + TranslationUtility.Translate("And", Language) + " ";
                    var structure = ManageStructure.GetStructure(transfer.ToStructureId.Value, Language);

                    string structureName = structure.Name; 

                    var nameAttribute = structure.Attributes?.FirstOrDefault(attr =>
                        (Language == Language.AR && attr.Text == "NameAr") ||
                        (Language == Language.EN && attr.Text == "Name"));

                    if (nameAttribute != null && !string.IsNullOrEmpty(nameAttribute.Value))
                    {
                        structureName = nameAttribute.Value;
                    }

                    note += TranslationUtility.Translate("TransferdtoStructure", Language) + " : " + structureName;

                }

                ManageActivityLog.AddActivityLog(transfer.DocumentId, transfer.Id, (int)ActivityLogs.Transfer, userId, note: note);
            }

            documentsIds.AddRange(documentCced.Where(t => t.Text == "false").Select(t => t.Id).ToList());

            if (transferList.Count > 0)
            {
                ManageStructure.Provision(structureIds);
                ManageUser.Provision(userIds);
                new Transfer().Insert(transferList);
                if (closeParentTransfer)
                {
                    parentTransfersIds = parentTransfersIds.Distinct().ToList();
                    CloseParentTransferByList(parentTransfersIds);
                }
                ManageDocument.UpdateDocumentStatusByIds(documentsIds, DocumentStatus.InProgress);
                retValue = true;
            }
            return retValue;
        }

        /// <summary>
        /// Close transfers by ids
        /// </summary>
        /// <param name="ids"></param>
        internal static void CloseParentTransferByList(List<long> ids)
        {
            if (ids.Count > 0)
            {
                List<Transfer> transfers = new Transfer().ListByIds(ids);
                if (transfers.Count > 0)
                {
                    new Transfer().UpdateStatusAndClosedDateByList(transfers);
                }
            }
        }

        /// <summary>
        /// Create and complete transfers based on document receiver
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="transferId"></param>
        /// <param name="item"></param>
        /// <param name="dueDate"></param>
        /// <param name="instruction"></param>
        /// <param name="structureId"></param>
        /// <returns></returns>
        internal static bool CompleteAndSendToReceiverEntity(long userId, long structureId, List<long> structureIds, bool isStructureSender, long? transferId, short purposeId, Document item, DateTime? dueDate, string instruction)
        {
            bool retValue = false;
            List<Transfer> transfers = new List<Transfer>();
            foreach (var receiver in item.DocumentReceiverEntity)
            {
                if (receiver.EntityGroupId.HasValue)
                {
                    List<StructureUserGroup> structureUserGroups = ManageEntityGroup.ListStructuresAndUserInGroupById(receiver.EntityGroupId.Value);
                    foreach (StructureUserGroup structureUser in structureUserGroups)
                    {
                        transfers.Add(new Transfer
                        {
                            DocumentId = item.Id,
                            StatusId = (short)DocumentStatus.Completed,
                            PurposeId = purposeId,
                            FromStructureId = structureId,
                            FromUserId = userId,
                            ToStructureId = structureUser.StructureId,
                            ToUserId = structureUser.UserId,
                            ParentTransferId = transferId,
                            CreatedByUserId = userId,
                            DueDate = dueDate,
                            Cced = true,
                            Instruction = instruction,
                            CreatedDate = DateTime.Now,
                            ClosedDate = DateTime.Now
                        });
                    }
                }
                else
                {
                    transfers.Add(new Transfer
                    {
                        DocumentId = item.Id,
                        StatusId = (short)DocumentStatus.Completed,
                        PurposeId = purposeId,
                        FromStructureId = structureId,
                        FromUserId = userId,
                        ToStructureId = receiver.StructureId,
                        ParentTransferId = transferId,
                        CreatedByUserId = userId,
                        DueDate = dueDate,
                        Cced = true,
                        Instruction = instruction,
                        CreatedDate = DateTime.Now,
                        ClosedDate = DateTime.Now
                    });
                }
            }
            if (transfers.Count > 0)
            {
                new Transfer().Insert(transfers);
                if (!transferId.HasValue)
                {
                    bool isLastOpenedTransfer = new Transfer().CheckIfLastOpenTransfer(transfers.First().Id, item.Id, true);
                    if (isLastOpenedTransfer && item.AttachmentId.HasValue)
                    {
                        item.StatusId = (short)DocumentStatus.Completed;
                        item.ClosedDate = DateTime.Now;
                        item.UpdateStatusAndClosedDate();
                        foreach (var transfer in transfers)
                        {
                            ManageActivityLog.AddActivityLog(item.Id, transfer.Id, (int)ActivityLogs.Complete, userId);
                        }
                    }
                }
                retValue = true;
            }
            return retValue;
        }

        //method to check if the Inbox Mode parametr is Inbox Default With Grouping
        internal static bool IsInboxModeWithGrouping()
        {
            return Configuration.InboxMode == "InboxDefaultWithGrouping";
        }

        #endregion

        #region Public Methods



        public static byte[] PrintDeliveryNote(long documentId, short categoryId)
        {

            var item = new DeliveryNoteTemplate().FindByCategoryId(categoryId);
            if (item != null)
            {
                var data = new DeliveryNoteTemplateData().Find(item.DeliveryNoteTemplateDataId.Value);
                if (data != null)
                {
                    byte[] newFile = ManageBookmark.ReplaceBookmark(data.Data, documentId, true);
                    return newFile;
                }
                else
                {
                    return null;
                }


            }
            else
            {
                return null;
            }

        }

        /// <summary>
        /// Check purposes in use
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public static List<short> CheckPurposesInUse(List<short> ids)
        {
            List<short> retValue = new List<short>();
            foreach (var id in ids)
            {
                if (new Transfer().CheckPurposeInUse(id))
                {
                    retValue.Add(id);
                }
            }
            return retValue;
        }
        /// <summary>
        /// List in progress transfers. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="structureId"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <param name="fromStructure"></param>
        /// <returns></returns>
        public static (int, List<TransferListViewModel>) ListInbox(int startIndex, int pageSize, long userId, List<long> structureIds, long structureId,
            bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN, bool fromStructure = false)
        {
            using (Transfer item = new Transfer())
            {
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                var categoryIds = new List<short>();
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                    if (filter == null)
                        filter = new ExpressionBuilderFilters();

                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                        filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                    else if (!delegation.ShowOldCorespondence)
                        filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                    filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                    filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
                }

                if (IsInboxModeWithGrouping() && filter.Count() == 0)
                    filter.Add("Id", 0, Operator.GreaterThan);

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                int countResult;
                List<Transfer> itemList;
                //check if the Inbox Mode parametr is Inbox Default With Grouping so get data with document grouping
                if (IsInboxModeWithGrouping())
                {
                    countResult = item.GetInboxCountWithGrouping(structureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
                    itemList = item.ListInboxWithGrouping(structureId, startIndex, pageSize, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Document>(), fromStructure);
                }
                else
                {
                    countResult = item.GetInboxCount(structureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
                    itemList = item.ListInbox(structureId, startIndex, pageSize, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Transfer>(), fromStructure);
                }
                return (countResult, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr ))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr ))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }

                    List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.Document.DocumentReceiverEntity)
                    {
                        if (!receiver.EntityGroupId.HasValue && !receiver.StructureId.HasValue)
                            continue;
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                        receivers.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            IsEntityGroup = isEntityGroup,
                            Text = text
                        });
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    string linkedCorrespondencesReferenceNo = string.Empty;

                    foreach (var linkedCorrespondence in t.Document.LinkedDocumentDocument)
                    {
                        if (!string.IsNullOrEmpty(linkedCorrespondence.LinkedDocumentNavigation?.ReferenceNumber))
                            linkedCorrespondencesReferenceNo += (linkedCorrespondencesReferenceNo.Length > 0 ? ", " : "") + linkedCorrespondence.LinkedDocumentNavigation.ReferenceNumber;
                    }
                    var isEnLang = language == Language.EN;
                    if(t.ExportedDocument!=null)
                    {
                        var exportedDate = t.ExportedDocument.Transfer.Any() ? t.ExportedDocument.Transfer.FirstOrDefault(s => s.IsSigned == true)?.ExportedDate : null;
                        return new TransferListViewModel
                        {
                            Id = t.Id,
                            DocumentId = t.DocumentId.Value,
                            Subject = t.Document.Subject,
                            CategoryId = t.Document.CategoryId,
                            ReferenceNumber = t.Document.CategoryId != Configuration.FollowUpCategory ? t.Document.ReferenceNumber : linkedCorrespondencesReferenceNo,
                            TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                            TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT), // format > HH:MM
                            CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                            DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            DocumentDueDate = t.Document.DueDate != null ? t.Document.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Status = t.Document.StatusId,
                            OwnerUserId = t.OwnerUserId,
                            OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                            IsOverDue = isOverdue,
                            IsRead = t.OpenedDate != null ? true : false,
                            IsLocked = t.OwnerUserId != null ? true : false,
                            SentToUser = t.ToUserId != null ? true : false,
                            SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                            FromUserId = t.FromUserId,
                            ToUserId = t.ToUserId,
                            FromStructureId = t.FromStructureId,
                            ToStructureId = t.ToStructureId,
                            FromUser = t.FromUserId.HasValue ? (isEnLang ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUserId.Value, language)}") : string.Empty,
                            ToUser = t.ToUserId.HasValue ? (isEnLang ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUserId.Value, language)}") : string.Empty,
                            FromStructure = fromStructure,
                            ToStructure = toStructure,
                            LockedBy = t.OwnerUser != null ? (isEnLang ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                            LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (isEnLang ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                            LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                            OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                            ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                            CreatedByUser = (isEnLang ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUserId, language)}"),
                            CreatedByUserId = t.Document.CreatedByUserId,
                            Instruction = t.Instruction,
                            Cced = t.Cced,
                            PurposeId = t.PurposeId,
                            PriorityId = t.Document.PriorityId,
                            PrivacyId = t.Document.PrivacyId,
                            ImportanceId = t.Document.ImportanceId,
                            SendingEntity = sendingEntity,
                            CreatedByStructureId = t.Document.CreatedByStructureId,
                            ReceivingEntityId = receivers,
                            ReceivingEntity = receivingEntity,
                            HasAttachment = t.Document.Attachment.Any(),
                            AttachmentCount = t.Document.Attachment.Count(),
                            DocumentForm = t.Document.DocumentForm,
                            WorkflowStepId = t.WorkflowStepId,
                            Note = t.Document.Note,
                            DocumentDate = t.Document.DocumentDate.HasValue ? t.Document.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Body = t.Document.DocumentForm.Body != null ? t.Document.DocumentForm.Body : string.Empty,
                            OriginalDocumentId = t.Document?.AttachmentId,
                            delegationId = delegationId,
                            RequestStatus = ((RequestStatuses)t.RequestStatus).ToString(),
                            RejectionReason = t.RejectionReason,
                            CategoryName = t.Document.Category.Name,
                            Document = t.Document,
                            //NumberOfDays = (long)(DateTime.Now - t.CreatedDate.Date).Days,
                            TransferNumberOfDays = (DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(t.CreatedDate).DayNumber),
                            SignedBy = (t.SignedByUserId.HasValue ? (isEnLang ? $"{t.SignedByUser.Firstname} {t.SignedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.SignedByUserId.Value, language)}") : string.Empty),
                            ExportedDate = exportedDate.HasValue ? exportedDate.Value.ToString(Constants.DATE_FORMAT) : "",
                            //ExportedNumberOfDays = t.ExportedDate.HasValue ? (DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(t.ExportedDate.Value).DayNumber) : null)
                            ExportedNumberOfDays = exportedDate.HasValue ? DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(exportedDate.Value).DayNumber : null
                        };
                    }
                    else

                    {
                        return new TransferListViewModel
                        {
                            Id = t.Id,
                            DocumentId = t.DocumentId.Value,
                            Subject = t.Document.Subject,
                            CategoryId = t.Document.CategoryId,
                            ReferenceNumber = t.Document.CategoryId != Configuration.FollowUpCategory ? t.Document.ReferenceNumber : linkedCorrespondencesReferenceNo,
                            TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                            TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT), // format > HH:MM
                            CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                            DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            DocumentDueDate = t.Document.DueDate != null ? t.Document.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Status = t.Document.StatusId,
                            OwnerUserId = t.OwnerUserId,
                            OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                            IsOverDue = isOverdue,
                            IsRead = t.OpenedDate != null ? true : false,
                            IsLocked = t.OwnerUserId != null ? true : false,
                            SentToUser = t.ToUserId != null ? true : false,
                            SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                            FromUserId = t.FromUserId,
                            ToUserId = t.ToUserId,
                            FromStructureId = t.FromStructureId,
                            ToStructureId = t.ToStructureId,
                            FromUser = t.FromUserId.HasValue ? (isEnLang ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUserId.Value, language)}") : string.Empty,
                            ToUser = t.ToUserId.HasValue ? (isEnLang ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUserId.Value, language)}") : string.Empty,
                            FromStructure = fromStructure,
                            ToStructure = toStructure,
                            LockedBy = t.OwnerUser != null ? (isEnLang ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                            LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (isEnLang ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                            LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                            OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                            ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                            CreatedByUser = (isEnLang ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUserId, language)}"),
                            CreatedByUserId = t.Document.CreatedByUserId,
                            Instruction = t.Instruction,
                            Cced = t.Cced,
                            PurposeId = t.PurposeId,
                            PriorityId = t.Document.PriorityId,
                            PrivacyId = t.Document.PrivacyId,
                            ImportanceId = t.Document.ImportanceId,
                            SendingEntity = sendingEntity,
                            CreatedByStructureId = t.Document.CreatedByStructureId,
                            ReceivingEntityId = receivers,
                            ReceivingEntity = receivingEntity,
                            HasAttachment = t.Document.Attachment.Any(),
                            AttachmentCount = t.Document.Attachment.Count(),
                            DocumentForm = t.Document.DocumentForm,
                            WorkflowStepId = t.WorkflowStepId,
                            Note = t.Document.Note,
                            DocumentDate = t.Document.DocumentDate.HasValue ? t.Document.DocumentDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Body = t.Document.DocumentForm.Body != null ? t.Document.DocumentForm.Body : string.Empty,
                            OriginalDocumentId = t.Document?.AttachmentId,
                            delegationId = delegationId,
                            RequestStatus = ((RequestStatuses)t.RequestStatus).ToString(),
                            RejectionReason = t.RejectionReason,
                            CategoryName = t.Document.Category.Name,
                            Document = t.Document,
                            //NumberOfDays = (long)(DateTime.Now - t.CreatedDate.Date).Days,
                            SignedBy = (t.SignedByUserId.HasValue ? (isEnLang ? $"{t.SignedByUser.Firstname} {t.SignedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.SignedByUserId.Value, language)}") : string.Empty),
                            TransferNumberOfDays = (DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(t.CreatedDate).DayNumber),
                            //ExportedDate = exportedDate.HasValue ? exportedDate.Value.ToString(Constants.DATE_FORMAT) : "",
                            //ExportedNumberOfDays = t.ExportedDate.HasValue ? (DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(t.ExportedDate.Value).DayNumber) : null)
                            //ExportedNumberOfDays = exportedDate.HasValue ? DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(exportedDate.Value).DayNumber : null
                        };
                    }
                   
                }).ToList());
            }
        }

        /// <summary>
        /// List closed transfers. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static (int, List<TransferListViewModel>) ListCompleted(int startIndex, int pageSize, long userId, List<long> structureIds,
            bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                var categoryIds = new List<short>();
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                    if (filter == null)
                        filter = new ExpressionBuilderFilters();

                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                        filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                    else if (!delegation.ShowOldCorespondence)
                        filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                    filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                }
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = item.GetCompletedCount(userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp);
                var itemList = item.ListCompleted(startIndex, pageSize, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Transfer>());
                return (countResult.Result, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now > t.DueDate.Value;
                        }
                    }
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }

                    List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.Document.DocumentReceiverEntity)
                    {
                        if (!receiver.EntityGroupId.HasValue && !receiver.StructureId.HasValue)
                            continue;
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                        receivers.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            IsEntityGroup = isEntityGroup
                        });
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }

                    return new TransferListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId.Value,
                        Subject = t.Document.Subject,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                        CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        Status = t.Document.StatusId,
                        OwnerUserId = t.OwnerUserId,
                        OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                        IsOverDue = isOverdue,
                        IsRead = t.OpenedDate != null ? true : false,
                        IsLocked = t.OwnerUserId != null ? true : false,
                        SentToUser = t.ToUserId != null ? true : false,
                        SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ? (language == Language.EN ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        LockedBy = t.OwnerUser != null ? (language == Language.EN ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                        LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (language == Language.EN ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                        LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        CreatedByUser = language == Language.EN ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                        CreatedByUserId = t.Document.CreatedByUserId,
                        Instruction = t.Instruction,
                        Cced = t.Cced,
                        PurposeId = t.PurposeId,
                        PriorityId = t.Document.PriorityId,
                        PrivacyId = t.Document.PrivacyId,
                        ImportanceId = t.Document.ImportanceId,
                        SendingEntity = sendingEntity,
                        CreatedByStructureId = t.Document.CreatedByStructureId,
                        ReceivingEntityId = receivers,
                        ReceivingEntity = receivingEntity,
                        DocumentForm = t.Document.DocumentForm,
                        Note = t.Document.Note,
                        Body = t.Document.DocumentForm.Body != null ? t.Document.DocumentForm.Body : string.Empty

                    };
                }).ToList());
            }
        }

        /// <summary>
        /// Get in progress transfers count. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="isOverdue"></param>
        /// <param name="loggedInStructureId"></param>
        /// <returns></returns>
        public static (int Total, int Today, int Unread) GetInboxCounts(long userId, List<long> structureIds, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null, bool? isOverdue = null, bool fromStructure = false)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            var categoryIds = new List<short>();
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
            }
            using (Transfer item = new Transfer())
            {
                if (/*!fromStructure &&*/ Core.Configuration.EnablePerStructure && loggedInStructureId != null)
                    filter.Add("ToStructureId", loggedInStructureId, Operator.Equals);

                if (isOverdue.HasValue)
                {
                    if (isOverdue == true)
                    {
                        filter.Add("DueDate", DateTime.Now.Date, Operator.LessThan);
                    }
                    else
                    {
                        filter.Add("DueDate", DateTime.Now.Date, Operator.GreaterThanOrEqual);
                    }
                }


                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;


                int inboxToday, inboxUnread, inboxTotal;
                //check if the Inbox Mode parametr is Inbox Default With Grouping so get counts with document grouping
                if (IsInboxModeWithGrouping())
                {

                    inboxTotal = item.GetInboxCountWithGrouping(loggedInStructureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
                    inboxToday = item.GetInboxTodayCountWithGrouping(loggedInStructureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, isOverdue, filterExp, fromStructure);
                    inboxUnread = item.GetInboxUnreadCountWithGrouping(loggedInStructureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
                }
                else
                {
                    inboxTotal = item.GetInboxCount(loggedInStructureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
                    inboxToday = item.GetInboxTodayCount(loggedInStructureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, isOverdue, filterExp, fromStructure);
                    inboxUnread = item.GetInboxUnreadCount(loggedInStructureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
                }
                return (inboxTotal, inboxToday, inboxUnread);
            }
        }

        /// <summary>
        /// Get closed transfers count. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static (int Total, int Today) GetCompletedCounts(long userId, List<long> structureIds, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            var categoryIds = new List<short>();
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
            }
            using (var item = new Transfer())
            {
                if (Core.Configuration.EnablePerStructure && loggedInStructureId != null)
                    filter.Add("ToStructureId", loggedInStructureId, Operator.Equals);

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                var inboxTotal = item.GetCompletedCount(userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp);
                var inboxToday = item.GetCompletedTodayCount(userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp);
                return (inboxTotal.Result, inboxToday);
            }
        }
        /// <summary>
        /// Get transfer metadata
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<StorageAttachmentModel> GetTransferVoiceNoteById(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
            long id, long? delegationId = null, Language language = Language.EN)
        {
            var item = new Transfer().FindIncludeDocumentAndPrivacy(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, item.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            StorageAttachmentModel StorageretValue = null;
            var access = item.VoiceNoteStorageId != null ?
                             !item.VoiceNotePrivacy ? true : (item.ToUserId == userId || (!item.ToUserId.HasValue && structureIds.Contains(item.ToStructureId.Value) && isStructureReceiver && item.Document.Privacy.Level <= privacyLevel) || item.FromUserId == userId)
                             : false;
            if (access)
            {
                StorageretValue = await Intalio.Core.Helper.GetFileFromStorageAsync($"{Configuration.StorageServerUrl}/storage/GetFile?fileId={item.VoiceNoteStorageId}", Configuration.IdentityAccessToken);
                //ManageActivityLog.AddFullActivityLog(item.DocumentId, item.Id, (int)ActivityLogs.ViewTransfer, currentUserId, "", "");
            }
            return StorageretValue;
        }

        /// <summary>
        /// Get transfer metadata
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <param name="categoryId"></param>
        /// <returns></returns>
        public static async Task<TransferGridFormatModel> GetTransferInfoById(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
            long id, long? delegationId = null, Language language = Language.EN, long categoryId = 0, bool fromRejectedDocument = false)
        {
            using (var transfer = new Transfer())
            {
                var item = await transfer.GetTransferInfoByIdAsync(id);
                var delegation = delegationId.HasValue && delegationId.Value != 0 ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, item?.CreatedDate) : null;
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }
                var currentUserId = userId;
                var retValue = new TransferGridFormatModel();
                if (categoryId != 0)
                {
                    var category = new Category().Find(categoryId);
                    retValue.ShowAttachmentProperties = category.AttachmentProperties != null && JsonConvert.DeserializeObject<dynamic>(category.AttachmentProperties).components.Count > 0;
                }
                if (await ManageUserAccess.HaveTransferAccessAsync(id, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    bool _fullControl = false;
                    if (item.Document.CategoryId == Configuration.FollowUpCategory)
                    {
                        Assignee _assignee = ManageAssignee.FindByDocumentIdUserId(item.DocumentId.Value, userId);
                        if (_assignee != null)
                        {
                            _fullControl = _assignee.FollowUpSecurityId == 1 ? false : true;
                        }
                    }

                    var sendingEntity = string.Empty;
                    if (item.Document.SendingEntity != null)
                    {
                        sendingEntity = item.Document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(item.Document.SendingEntity.NameAr))
                        {
                            sendingEntity = item.Document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(item.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = item.Document.SendingEntity.NameFr;
                        }
                    }
                    List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in item.Document.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string parent = string.Empty;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr)  ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            if (receiver.Structure.Parent != null)
                            {
                                parent = language == Language.AR ? receiver.Structure.Parent.NameAr != null ? receiver.Structure.Parent.NameAr : receiver.Structure.Parent.Name
                                : language == Language.FR ? receiver.Structure.Parent.NameFr != null ? receiver.Structure.Parent.NameFr : receiver.Structure.Parent.Name
                                : receiver.Structure.Parent.Name;
                            }
                            structureText += text + Constants.SEPARATOR;
                        }
                        receivers.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            IsEntityGroup = isEntityGroup,
                            Text = text,
                            IsExternal = receiver.Structure.IsExternal,
                            ParentName = parent
                        });
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    string WorkflowInitiator = "";
                    bool isWorkflowReturned = false;
                    string nextStepUserName = "";
                    bool forSign = false;
                    var hasSignatureRegion = new AttachmentSignUser().FindByDocumentAndUser(item.DocumentId.Value, userId).Any();
                    if ((item.Purpose != null && item.Purpose.ForSignature) || (item.WorkflowStepId != null && hasSignatureRegion))
                        forSign = true;
                    if (item.WorkflowStepId != null)
                    {
                        WorkflowStep step = new WorkflowStep().FindIncludeInitiator(item.WorkflowStepId.Value);
                        WorkflowInitiator = language == Language.EN ? step.Workflow.InitatorUser.Firstname + " " + step.Workflow.InitatorUser.Lastname : language == Language.FR ? step.Workflow.InitatorUser.FirstnameFr + " " + step.Workflow.InitatorUser.LastnameFr : step.Workflow.InitatorUser.FirstnameAr + " " + step.Workflow.InitatorUser.LastnameAr;
                        isWorkflowReturned = step.UserId != step.Workflow.InitiatorUserId && userId == step.Workflow.InitiatorUserId;
                        WorkflowStep nextStep = new WorkflowStep().FindNextStep(step.WorkflowId, step.order + 1);
                        nextStepUserName = nextStep == null ? step.Workflow.InitatorUser.Firstname + " " + step.Workflow.InitatorUser.Lastname : nextStep?.User?.Firstname + " " + nextStep?.User?.Lastname;
                    }
                    var latestAttachmentVersion = (item.Document.AttachmentId != null && item.Document.AttachmentNavigation != null) ? await ManageAttachment.GetCurrentVersionNumber(item.Document.AttachmentNavigation.StorageAttachmentId) : "";

                    retValue = new TransferGridFormatModel
                    {
                        PriorityId = item.PriorityId,
                        Priority = item.Priority != null ? (language == Language.EN ? item.Priority?.Name : language == Language.FR ? item.Priority?.NameFr : item.Priority?.NameAr) : string.Empty,
                        PrivacyId = item.Document.PrivacyId,
                        ImportanceId = item.Document.ImportanceId,
                        SendingEntity = sendingEntity,
                        CreatedByStructureId = item.Document.CreatedByStructureId,
                        ReceivingEntityId = receivers,
                        ReceivingEntity = receivingEntity,
                        CreatedDate = item.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = item.DueDate != null ? item.DueDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        OpenedDate = item.OpenedDate != null ? item.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        ClosedDate = item.ClosedDate != null ? item.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        CreatedByUser = language == Language.EN ? $"{item.Document.CreatedByUser.Firstname} {item.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.Document.CreatedByUser.Id, language)}",
                        FromStructureId = item.FromStructureId,
                        FromStructure = item.FromStructure != null ? language == Language.EN ? item.FromStructure.Name :
                        language == Language.FR ? !string.IsNullOrEmpty(item.FromStructure.NameFr) ? item.FromStructure.NameFr :
                        item.FromStructure.Name : !string.IsNullOrEmpty(item.FromStructure.NameAr) ? item.FromStructure.NameAr : item.FromStructure.Name : string.Empty,
                        FromUser = item.FromUserId != null ? (language == Language.EN ? $"{item.FromUser.Firstname} {item.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.FromUser.Id, language)}") : string.Empty,
                        ToUser = item.ToUserId != null ? (language == Language.EN ? $"{item.ToUser.Firstname} {item.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.ToUser.Id, language)}") : string.Empty,
                        ToStructureId = item.ToStructureId,
                        ToStructure = item.ToStructure != null ? language == Language.EN ? item.ToStructure.Name :
                        language == Language.FR ? !string.IsNullOrEmpty(item.ToStructure.NameFr) ? item.ToStructure.NameFr :
                        item.ToStructure.Name : !string.IsNullOrEmpty(item.ToStructure.NameAr) ? item.ToStructure.NameAr : item.ToStructure.Name : string.Empty,
                        Purpose = item.Purpose != null ? (language == Language.EN ? item.Purpose?.Name : language == Language.FR ? item.Purpose?.NameFr : item.Purpose?.NameAr) : string.Empty,
                        Instruction = item.Instruction,
                        Subject = item.Document.Subject,
                        DocumentId = item.DocumentId.Value,
                        OwnerUserId = item.OwnerUserId,
                        SentToUser = item.ToUserId.HasValue,
                        Cced = item.Cced,
                        ByTemplate = item.Document.ByTemplate == true ? true : false,
                        ForSignature = forSign,
                        ParentTransferId = item.ParentTransferId,
                        VoiceNote = item.VoiceNoteStorageId != null,
                        WorkflowStepId = item.WorkflowStepId,
                        InitiatorUser = WorkflowInitiator,
                        IsWorkflowReturned = isWorkflowReturned,
                        HasReferenceNumber = item.Document.ReferenceNumber != null ? true : false,
                        IsFollowUp = (item.Document.CategoryId == Configuration.FollowUpCategory),
                        AssigneeOperationPermission = (item.Document.CreatedByUserId == userId),
                        FullControl = _fullControl,
                        StatusId = item.StatusId,
                        IsTaskCreator = item.Document.CreatedByUserId == userId,
                        HasAttachments = item.Document.Attachment.Count > 0,
                        HasUserCofigureSignature = item.Document?.Attachment?.Where(att => att.AttachmentSignUser.Where(asu => asu.UserId == currentUserId && asu.SignatureRegion.Any()).Any()).Any(),
                        nextStepUserName = nextStepUserName,
                        AllowSign = delegation != null ? delegation.AllowSign : true,
                        ShowAttachmentProperties = item.Document.Category.AttachmentProperties != null && JsonConvert.DeserializeObject<dynamic>(item.Document.Category.AttachmentProperties).components.Count > 0,
                        ReferenceNumber = item.Document.ReferenceNumber,
                        AttachmentExtention = item.Document.AttachmentNavigation != null ? item.Document.AttachmentNavigation.Extension : "",
                        AttachmentVersion = latestAttachmentVersion,
                        TemplateHasSignature = item.Document.TemplateHasSignature,
                        IsSigned = item.IsSigned,
                        IsDocumentSigned = item.Document.IsSigned ?? false,
                        DocumentCarbonCopy = item.Document.DocumentCarbonCopy.Select(x => new ReceivingEntityModel
                        {
                            Id = x.StructureId,
                            Text = language == Language.AR ? (string.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.Name : x.Structure.NameAr) : language == Language.FR ? (string.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.Name : x.Structure.NameFr) : x.Structure.Name,
                            IsExternal = x.Structure.IsExternal,
                            IsCC = true,
                            ParentName = (language == Language.AR ? !String.IsNullOrEmpty(x.Structure.Parent?.NameAr) ? x.Structure.Parent.NameAr : x.Structure.Parent?.Name
                                : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.Parent?.NameFr) ? x.Structure.Parent.NameFr : x.Structure.Parent?.Name
                                : x.Structure.Parent?.Name) ?? ""
                        }).ToList()
                    };
                    if ( item.ClosedDate.HasValue || !item.OwnerUserId.HasValue || (item.OwnerUserId.HasValue && item.OwnerUserId != userId) || item.Cced || fromRejectedDocument)
                    {
                        long structureId = UserContextAccessor.UserContext.StructureId;
                        int roleId = UserContextAccessor.UserContext.RoleId;
                      
                        var haveAccessStructureInbox = transfer.CheckHaveAccessStructureInboxAsync(item.Id, null, userId, structureIds, isStructureReceiver, privacyLevel, roleId, structureId);

                        if ((item.ToUserId.HasValue && item.ToUserId == userId && !item.Cced) || fromRejectedDocument || (haveAccessStructureInbox && (!item.OwnerUserId.HasValue || ( item.OwnerUserId.HasValue && item.OwnerUserId == userId))))
                        {
                            retValue.ViewMode = false;
                        }
                        else
                        {
                            retValue.ViewMode = true;
                        }
                    }
                    else
                    {
                        retValue.ViewMode = false;
                    }
                    ManageActivityLog.AddFullActivityLog(item.DocumentId, item.Id, (int)ActivityLogs.ViewTransfer, currentUserId, "", "");
                }
                return retValue;
            }
        }
        private static MultipartFormDataContent SetFileFormData(FileViewModel file, List<string> paths = null, string recordId = "", List<string> filingPlans = null)
        {
            var retValue = new MultipartFormDataContent();
            string fileName = Intalio.Core.Helper.GetFileName(file.Name);
            retValue.Add(new StringContent(fileName), "Name");
            retValue.Add(new StringContent(file.FileSize.ToString()), "FileSize");
            retValue.Add(new StringContent(file.Data.MD5Hash()), "MD5Checksum");
            retValue.Add(new StringContent(file.Extension), "Extension");
            if (!string.IsNullOrEmpty(file.Comment))
            {
                retValue.Add(new StringContent(file.Comment), "Comment");
            }
            if (!string.IsNullOrEmpty(file.ContentType))
            {
                retValue.Add(new StringContent(file.ContentType), "ContentType");
            }
            var fileContent = new ByteArrayContent(file.Data);
            //fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse(file.ContentType);
            retValue.Add(fileContent, "Data", file.Name);
            if (!paths.IsNull())
            {
                foreach (var path in paths)
                {
                    retValue.Add(new StringContent(path), "Paths");
                }
            }
            if (!string.IsNullOrEmpty(recordId))
            {
                retValue.Add(new StringContent(recordId), "RecordId");
            }
            if (!filingPlans.IsNull())
            {
                foreach (var filingPlan in filingPlans)
                {
                    retValue.Add(new StringContent(filingPlan), "FilingPlan");
                }
            }
            return retValue;
        }

        /// <summary>
        /// Create transfers based on document receiver and carbon copy
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="transferId"></param>
        /// <param name="item"></param>
        /// <param name="purposeId"></param>
        /// <param name="dueDate"></param>
        /// <param name="instruction"></param>
        /// <param name="VoiceNote"></param>
        /// <param name="structureId"></param>
        /// <param name="isBroadcast"></param>
        /// <param name="VoiceNotePrivacy"></param>
        /// 
        /// <returns></returns>
       
        public static async Task<bool> SendToReceiverEntity(long userId, long structureId, List<long> structureIds, bool isStructureSender, long? transferId, Document item, short purposeId, DateTime? dueDate, string instruction, Language language = Language.EN, byte[] VoiceNote = null, bool VoiceNotePrivacy = false, bool isBroadcast = false , long? requestStatus = null,long? exportedDocumentId = null)
        {
            bool retValue = false;
            List<Transfer> transfers = new List<Transfer>();
            long? VoiceNoteStorageId = null;
            if (VoiceNote != null)
            {
                var file = new FileViewModel()
                {
                    Name = DateTime.Now.GetCurrentTimeStamp().ToString(),
                    FileName = DateTime.Now.GetCurrentTimeStamp().ToString(),
                    FileSize = VoiceNote.Length,
                    ContentType = "audio/wav",
                    Data = VoiceNote,
                    Extension = ".wav"
                };
                var data = SetFileFormData(file, null, item.Id.ToString(), null);
                var StorageretValue = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/Upload", Configuration.IdentityAccessToken, data);
                if (StorageretValue.Success)
                {
                    VoiceNoteStorageId = Convert.ToInt64(StorageretValue.Result);
                }
            }
            List<string> receivingEntities = new List<string>();
            foreach (var receiver in item.DocumentReceiverEntity)
            {
                if (isBroadcast && receiver.EntityGroupId.HasValue)
                {
                    List<StructureUserGroup> structureUserGroups = ManageEntityGroup.ListStructuresAndUserInGroupById(receiver.EntityGroupId.Value);
                    foreach (StructureUserGroup structureUser in structureUserGroups)
                    {
                        transfers.Add(new Transfer
                        {
                            DocumentId = item.Id,
                            StatusId = (short)DocumentStatus.InProgress,
                            PurposeId = purposeId,
                            FromStructureId = structureId,
                            FromUserId = userId,
                            ToStructureId = structureUser.StructureId,
                            ToUserId = structureUser.UserId,
                            ParentTransferId = transferId,
                            CreatedByUserId = userId,
                            DueDate = dueDate,
                            Cced = isBroadcast,
                            Instruction = instruction,
                            VoiceNoteStorageId = VoiceNoteStorageId,
                            VoiceNotePrivacy = VoiceNotePrivacy
                        });
                    }
                }
                else
                {
                    if(requestStatus != null)
                    {
                        transfers.Add(new Transfer
                        {
                            DocumentId = item.Id,
                            StatusId = (short)DocumentStatus.InProgress,
                            PurposeId = purposeId,
                            FromStructureId = structureId,
                            FromUserId = userId,
                            ToStructureId = receiver.StructureId,
                            ParentTransferId = transferId,
                            CreatedByUserId = userId,
                            DueDate = dueDate,
                            Cced = isBroadcast,
                            Instruction = instruction,
                            VoiceNoteStorageId = VoiceNoteStorageId,
                            VoiceNotePrivacy = VoiceNotePrivacy,
                            RequestStatus = (long)RequestStatuses.Pending,
                            ExportedDocumentId = exportedDocumentId,
                        });
                    }
                    else
                    {
                        transfers.Add(new Transfer
                        {
                            DocumentId = item.Id,
                            StatusId = (short)DocumentStatus.InProgress,
                            PurposeId = purposeId,
                            FromStructureId = structureId,
                            FromUserId = userId,
                            ToStructureId = receiver.StructureId,
                            ParentTransferId = transferId,
                            CreatedByUserId = userId,
                            DueDate = dueDate,
                            Cced = isBroadcast,
                            Instruction = instruction,
                            VoiceNoteStorageId = VoiceNoteStorageId,
                            VoiceNotePrivacy = VoiceNotePrivacy,
                            
                        });
                    }
                    
                }

                string text = string.Empty;
                if (receiver.EntityGroupId.HasValue)
                {
                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                    : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                    : receiver.EntityGroup.Name;
                }
                else
                {
                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                   : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                   : receiver.Structure.Name;
                }
                receivingEntities.Add(text);
            }

            foreach (var carbonCopy in item.DocumentCarbonCopy)
            {
                if (requestStatus != null)
                {
                    transfers.Add(new Transfer
                    {
                        DocumentId = item.Id,
                        StatusId = (short)DocumentStatus.InProgress,
                        PurposeId = ManagePurpose.GetCCedPurpose(),
                        FromStructureId = structureId,
                        FromUserId = userId,
                        ToStructureId = carbonCopy.StructureId,
                        ParentTransferId = transferId,
                        CreatedByUserId = userId,
                        DueDate = dueDate,
                        Cced = true,
                        Instruction = instruction,
                        VoiceNoteStorageId = VoiceNoteStorageId,
                        VoiceNotePrivacy = VoiceNotePrivacy,
                        RequestStatus = (long)RequestStatuses.Pending,
                        ExportedDocumentId = exportedDocumentId,
                    });
                }
                else

                {
                    transfers.Add(new Transfer
                    {
                        DocumentId = item.Id,
                        StatusId = (short)DocumentStatus.InProgress,
                        PurposeId = ManagePurpose.GetCCedPurpose(),
                        FromStructureId = structureId,
                        FromUserId = userId,
                        ToStructureId = carbonCopy.StructureId,
                        ParentTransferId = transferId,
                        CreatedByUserId = userId,
                        DueDate = dueDate,
                        Cced = true,
                        Instruction = instruction,
                        VoiceNoteStorageId = VoiceNoteStorageId,
                        VoiceNotePrivacy = VoiceNotePrivacy,
                        
                    });
                }
                    
            }
            bool isExport = requestStatus != null;
            if (transfers.Count > 0 && (isBroadcast || ManageUserAccess.CanSend(structureId, userId, structureIds, transfers.Select(t => t.ToStructureId.Value).Distinct().ToList(), isStructureSender, null, isExport)))
            {
                new Transfer().Insert(transfers);
                ManageDocument.UpdateDocumentStatus(item, DocumentStatus.InProgress);
                retValue = true;
            }
            if (!retValue && VoiceNoteStorageId != null)
            {
                var storageretValue = await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteFile?fileId={VoiceNoteStorageId}", Configuration.IdentityAccessToken);
            }
            var newDocumentValue = JsonConvert.SerializeObject(new DocumentModel
            {
                Subject = item.Subject,
                FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                Classification = item.Classification != default(Intalio.CTS.Core.DAL.Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                DocumentType = item.DocumentType != default(Intalio.CTS.Core.DAL.DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                Importance = item.Importance != default(Intalio.CTS.Core.DAL.Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                Priority = item.Priority != default(Intalio.CTS.Core.DAL.Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                Privacy = item.Privacy != default(Intalio.CTS.Core.DAL.Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                   : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                   : item.SendingEntity.Name : String.Empty,
                Receivers = receivingEntities,
                ExternalReferenceNumber = item.ExternalReferenceNumber,
                Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                               .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                   : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                   : x.Structure.Name).ToList() : new List<string>()
            });
            if(requestStatus != null)
                ManageActivityLog.AddActivityLog(item.Id, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.Exported, userId, "", newDocumentValue);
            else
                ManageActivityLog.AddActivityLog(item.Id, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.Send, userId, "", newDocumentValue);

            return retValue;
        }






        public static async Task<bool> ExportToReceiverEntity(long userId, long structureId, List<long> structureIds, bool isStructureSender, long? transferId, Document item, short purposeId, DateTime? dueDate, string instruction, Language language = Language.EN, byte[] VoiceNote = null, bool VoiceNotePrivacy = false, bool isBroadcast = false)
        {
            bool retValue = false;
            List<Transfer> transfers = new List<Transfer>();
            long? VoiceNoteStorageId = null;
            if (VoiceNote != null)
            {
                var file = new FileViewModel()
                {
                    Name = DateTime.Now.GetCurrentTimeStamp().ToString(),
                    FileName = DateTime.Now.GetCurrentTimeStamp().ToString(),
                    FileSize = VoiceNote.Length,
                    ContentType = "audio/wav",
                    Data = VoiceNote,
                    Extension = ".wav"
                };
                var data = SetFileFormData(file, null, item.Id.ToString(), null);
                var StorageretValue = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/Upload", Configuration.IdentityAccessToken, data);
                if (StorageretValue.Success)
                {
                    VoiceNoteStorageId = Convert.ToInt64(StorageretValue.Result);
                }
            }
            List<string> receivingEntities = new List<string>();
            foreach (var receiver in item.DocumentReceiverEntity)
            {
                if (isBroadcast && receiver.EntityGroupId.HasValue)
                {
                    List<StructureUserGroup> structureUserGroups = ManageEntityGroup.ListStructuresAndUserInGroupById(receiver.EntityGroupId.Value);
                    foreach (StructureUserGroup structureUser in structureUserGroups)
                    {
                        transfers.Add(new Transfer
                        {
                            DocumentId = item.Id,
                            StatusId = (short)DocumentStatus.InProgress,
                            PurposeId = purposeId,
                            FromStructureId = structureId,
                            FromUserId = userId,
                            ToStructureId = structureUser.StructureId,
                            ToUserId = structureUser.UserId,
                            ParentTransferId = transferId,
                            CreatedByUserId = userId,
                            DueDate = dueDate,
                            Cced = isBroadcast,
                            Instruction = instruction,
                            VoiceNoteStorageId = VoiceNoteStorageId,
                            VoiceNotePrivacy = VoiceNotePrivacy,
                            RequestStatus = (long)RequestStatuses.Pending,

                        });
                    }
                }
                else
                {
                    transfers.Add(new Transfer
                    {
                        DocumentId = item.Id,
                        StatusId = (short)DocumentStatus.InProgress,
                        PurposeId = purposeId,
                        FromStructureId = structureId,
                        FromUserId = userId,
                        ToStructureId = receiver.StructureId,
                        ParentTransferId = transferId,
                        CreatedByUserId = userId,
                        DueDate = dueDate,
                        Cced = isBroadcast,
                        Instruction = instruction,
                        VoiceNoteStorageId = VoiceNoteStorageId,
                        VoiceNotePrivacy = VoiceNotePrivacy,
                        RequestStatus = (long)RequestStatuses.Pending,
                    });
                }

                string text = string.Empty;
                if (receiver.EntityGroupId.HasValue)
                {
                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                    : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                    : receiver.EntityGroup.Name;
                }
                else
                {
                    text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                   : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                   : receiver.Structure.Name;
                }
                receivingEntities.Add(text);
            }

            foreach (var carbonCopy in item.DocumentCarbonCopy)
            {
                transfers.Add(new Transfer
                {
                    DocumentId = item.Id,
                    StatusId = (short)DocumentStatus.InProgress,
                    PurposeId = ManagePurpose.GetCCedPurpose(),
                    FromStructureId = structureId,
                    FromUserId = userId,
                    ToStructureId = carbonCopy.StructureId,
                    ParentTransferId = transferId,
                    CreatedByUserId = userId,
                    DueDate = dueDate,
                    Cced = true,
                    Instruction = instruction,
                    VoiceNoteStorageId = VoiceNoteStorageId,
                    VoiceNotePrivacy = VoiceNotePrivacy,
                    RequestStatus = (long)RequestStatuses.Pending,
                });
            }
            if (transfers.Count > 0 && (isBroadcast || ManageUserAccess.CanSend(structureId, userId, structureIds, transfers.Select(t => t.ToStructureId.Value).Distinct().ToList(), isStructureSender)))
            {
                new Transfer().Insert(transfers);
                ManageDocument.UpdateDocumentStatus(item, DocumentStatus.InProgress);
                retValue = true;
            }
            if (!retValue && VoiceNoteStorageId != null)
            {
                var storageretValue = await Intalio.Core.Helper.HttpPostAsync($"{Configuration.StorageServerUrl}/storage/DeleteFile?fileId={VoiceNoteStorageId}", Configuration.IdentityAccessToken);
            }
            var newDocumentValue = JsonConvert.SerializeObject(new DocumentModel
            {
                Subject = item.Subject,
                FormData = item.DocumentForm != null ? item.DocumentForm.Form : string.Empty,
                Classification = item.Classification != default(Intalio.CTS.Core.DAL.Classification) ? language == Language.AR ? item.Classification.NameAr : language == Language.FR ? item.Classification.NameFr : item.Classification.Name : String.Empty,
                DocumentType = item.DocumentType != default(Intalio.CTS.Core.DAL.DocumentType) ? language == Language.AR ? item.DocumentType.NameAr : language == Language.FR ? item.DocumentType.NameFr : item.DocumentType.Name : String.Empty,
                DueDate = item.DueDate.HasValue ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                Importance = item.Importance != default(Intalio.CTS.Core.DAL.Importance) ? language == Language.AR ? item.Importance.NameAr : language == Language.FR ? item.Importance.NameFr : item.Importance.Name : String.Empty,
                Priority = item.Priority != default(Intalio.CTS.Core.DAL.Priority) ? language == Language.AR ? item.Priority.NameAr : language == Language.FR ? item.Priority.NameFr : item.Priority.Name : String.Empty,
                Privacy = item.Privacy != default(Intalio.CTS.Core.DAL.Privacy) ? language == Language.AR ? item.Privacy.NameAr : language == Language.FR ? item.Privacy.NameFr : item.Privacy.Name : String.Empty,
                SendingEntity = item.SendingEntity != default(Structure) ? language == Language.AR ? !string.IsNullOrEmpty(item.SendingEntity.NameAr) ? item.SendingEntity.NameAr : item.SendingEntity.Name
                                                   : language == Language.FR ? !string.IsNullOrEmpty(item.SendingEntity.NameFr) ? item.SendingEntity.NameFr : item.SendingEntity.Name
                                                   : item.SendingEntity.Name : String.Empty,
                Receivers = receivingEntities,
                ExternalReferenceNumber = item.ExternalReferenceNumber,
                Keyword = item.DocumentForm != null ? item.DocumentForm.Keyword : string.Empty,
                Body = item.DocumentForm != null ? item.DocumentForm.Body : string.Empty,
                CarbonCopies = item.DocumentCarbonCopy.Any() ? item.DocumentCarbonCopy.Where(x => x.Structure != default(Structure))
                                               .Select(x => language == Language.AR ? !String.IsNullOrEmpty(x.Structure.NameAr) ? x.Structure.NameAr : x.Structure.Name
                                                   : language == Language.FR ? !String.IsNullOrEmpty(x.Structure.NameFr) ? x.Structure.NameFr : x.Structure.Name
                                                   : x.Structure.Name).ToList() : new List<string>()
            });
            ManageActivityLog.AddActivityLog(item.Id, transferId.HasValue ? transferId.Value : null, (int)Core.ActivityLogs.Send, userId, "", newDocumentValue);
            return retValue;
        }




        public static bool IsAudioFile(byte[] bytes)
        {
            // Check for WAV file (RIFF signature)
            if (bytes.Length >= 4 && bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46)
            {
                return true;
            }
            else if (bytes.Length >= 2 && bytes[0] == 0xFF && (bytes[1] == 0xFB || bytes[1] == 0xF3))
            {
                return true;
            }
            // Default to false if no match is found
            return false;
        }

        /// <summary>
        /// Reply transfer to previous structure or user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="purposeId"></param>
        /// <param name="dueDate"></param>
        /// <param name="instruction"></param>
        /// <param name="structureId"></param>
        /// <param name="delegationId"></param>
        /// <param name="transferToType"></param>
        /// <param name="VoiceNote"></param>
        /// <param name="VoiceNotePrivacy"></param>
        /// <returns></returns>
        public static async Task<(bool Replied, string Message)> ReplyAsync(long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long documentId, long transferId, short purposeId, DateTime? dueDate, string instruction, byte[] VoiceNote, bool VoiceNotePrivacy, long structureId, long? delegationId, ReplyType transferToType, bool maintainTransfer = false, Language language = Language.EN)
        {
            bool retValue = false;
            Transfer parent = new Transfer().Find(transferId);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, parent.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (ManageUserAccess.HaveAccess(documentId, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                if (Configuration.AttachmentEditable)
                {
                    if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                    {
                        return (false, "FileInUse");
                    }
                }
                else
                {
                    if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                    {
                        return (false, "OriginalFileInUse");
                    }
                }
                if (parent != null)
                {
                    long? VoiceNoteStorageId = null;
                    if (VoiceNote != null)
                    {
                        var file = new FileViewModel()
                        {
                            Name = DateTime.Now.GetCurrentTimeStamp().ToString(),
                            FileName = DateTime.Now.GetCurrentTimeStamp().ToString(),
                            FileSize = VoiceNote.Length,
                            ContentType = "audio/wav",
                            Data = VoiceNote,
                            Extension = ".wav"
                        };
                        var data = SetFileFormData(file, null, documentId.ToString(), null);
                        var StorageretValue = await Intalio.Core.Helper.HttpMultipartPostAsync($"{Configuration.StorageServerUrl}/storage/Upload", Configuration.IdentityAccessToken, data);
                        if (StorageretValue.Success)
                        {
                            VoiceNoteStorageId = Convert.ToInt64(StorageretValue.Result);
                        }
                    }
                    Transfer reply = new Transfer
                    {
                        DocumentId = documentId,
                        StatusId = (short)DocumentStatus.InProgress,
                        PurposeId = purposeId,
                        FromStructureId = structureId,
                        FromUserId = userId,
                        ParentTransferId = transferId,
                        CreatedByUserId = userId,
                        DueDate = dueDate,
                        Cced = false,
                        Instruction = instruction,
                        ToStructureId = parent.FromStructureId,
                        VoiceNoteStorageId = VoiceNoteStorageId,
                        VoiceNotePrivacy = VoiceNotePrivacy
                    };
                    if (transferToType == ReplyType.ReplyToUser)
                    {
                        reply.ToUserId = parent.FromUserId;
                    }
                    else if (transferToType == ReplyType.ReplyToInitiator)
                    {
                        WorkflowStep currentStep = new WorkflowStep().Find(parent.WorkflowStepId.Value);
                        reply.ToStructureId = currentStep.Workflow.InitiatorStructureId;
                        reply.ToUserId = currentStep.Workflow.InitiatorUserId;
                        reply.WorkflowStepId = parent.WorkflowStepId.Value;
                    }
                    if (ManageUserAccess.CanSend(structureId, userId, structureIds, new List<long>() { reply.ToStructureId.Value }, isStructureSender))
                    {
                        reply.Insert();
                        if(!maintainTransfer)
                            Close(transferId);
                        if (reply.Id != default)
                        {
                            ManageActivityLog.AddActivityLog(documentId, reply.Id, (int)ActivityLogs.Reply, userId, note: TranslationUtility.Translate("ReplyBy", language) + " : " + $"{IdentityHelperExtension.GetFullName(userId, language)}");
                        }

                        retValue = true;
                    }
                }
            }
            return (retValue, string.Empty);
        }

        /// <summary>
        /// Close transfer by id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        public static void Close(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null)
        {
            var transfer = new Transfer().Find(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (transfer != null && transfer.ClosedDate == null && ManageUserAccess.HaveAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                transfer.StatusId = (short)DocumentStatus.Completed;
                transfer.ClosedDate = DateTime.Now;
                transfer.UpdateStatusAndClosedDate();
            }
        }

        /// <summary>
        /// View transfer (mark as read)
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        public static async void View(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null)
        {
            var currentUserId = userId;
            var transfer = await new Transfer().FindAsync(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (transfer != null && transfer.OpenedDate == null && ManageUserAccess.HaveActiveTransferAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                transfer.OpenedDate = DateTime.Now;
                if (!transfer.ToUserId.IsNull())
                {
                    if (delegationId.HasValue)
                    {
                        transfer.OwnerDelegatedUserId = currentUserId;
                    }
                    transfer.OwnerUserId = transfer.ToUserId;
                    transfer.UpdateOpenedDateAndOwner();
                }
                else
                {
                    transfer.UpdateOpenedDate();
                }
                ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.View, currentUserId);
            }
        }

        ////////////////////////////////////
        /// <summary>
        ///  (mark as Unread)
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        public static (bool,string) UnRead(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null, Language? language = null)
        {
            var currentUserId = userId;
            var transfer = new Transfer().Find(id);
            if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsWithOriginal(transfer.DocumentId.Value))

            //if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsByUser(transfer.DocumentId.Value, userId))
            {
                return (false, "FileInUse");
            }
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (transfer != null && transfer.OpenedDate.HasValue && ManageUserAccess.HaveActiveTransferAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                transfer.OpenedDate = null;
                if (transfer.ToUserId.IsNull())
                {
                    if (delegationId.HasValue)
                    {
                        transfer.OwnerDelegatedUserId = null;
                    }
                    transfer.OwnerUserId = null;
                    transfer.UpdateOpenedDateAndOwner();
                }
                else
                {
                    transfer.UpdateOpenedDate();
                }
                ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Unread, currentUserId);
            }
            return (true, "");
        }
        /// <summary>
        /// Lock transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<(bool Locked, long? OwnerUserId, long? OwnerDelegatedUserId)> Lock(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null)
        {
            bool retValue = false;
            var currentUserId = userId;
            var transfer = await new Transfer().FindAsync(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (transfer != null && transfer.OwnerUserId == null && await ManageUserAccess.HaveActiveTransferAccessAsync(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                if (delegation != null)
                {
                    transfer.OwnerDelegatedUserId = currentUserId;
                }
                transfer.OwnerUserId = userId;
                transfer.LockedDate = DateTime.Now;
                if (!transfer.OpenedDate.HasValue)
                {
                    transfer.OpenedDate = DateTime.Now;
                }
                if (!transfer.Lock())
                {
                    transfer = await new Transfer().FindAsync(id);
                }
                new EventReceivers().OnTransferLock(id, delegationId);
                retValue = true;
                ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Lock, currentUserId);
            }
            return (retValue, transfer.OwnerUserId, transfer.OwnerDelegatedUserId);
        }

        /// <summary>
        /// Unlock transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static (bool Success, string Message) UnLock(long userId, long id, int roleId, long? structureId, long? delegationId = null)
        {

            var currentUserId = userId;
            var transfer = new Transfer().Find(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
            }
            var retValue = (false, "False");
            //if (transfer != null && (transfer.OwnerUserId == userId || roleId == Convert.ToInt32(Role.Administrator)))
            if (!transfer.LockedDate.HasValue)
            {
                retValue = (true, "True");
                return retValue;
            }

            var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");

            if (transfer != null && (transfer.OwnerUserId == userId) || ifHaveManageCorrespondenceAccess)
            {
                if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsWithOriginal(transfer.DocumentId.Value))

                //if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsByUser(transfer.DocumentId.Value, userId))
                {
                    return (false, "FileInUse");
                }

                transfer.OwnerDelegatedUserId = null;
                transfer.OwnerUserId = null;
                transfer.LockedDate = null;
                transfer.UpdateLocked();
                new EventReceivers().OnTransferUnLock(id, delegationId);
                retValue.Item1 = true;

                ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Unlock, currentUserId);


            }
            return retValue;
        }



        public static APIResponseViewModel AcceptRequest(long userId, long id, int roleId, long? structureId,Language language=Language.EN, long? delegationId = null)
        {
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeDocumentAndTostructure(id);
            if (string.IsNullOrEmpty(transfer.Document.ReferenceNumber))
            {
                var returnedResult = new CategoryReferenceCounter().Generate(transfer.Document.CategoryId, userId, transfer.Document.CreatedByStructureId, transfer.Document.Id, id, language);
                transfer.Document.ReferenceNumber = returnedResult.Reference;
                transfer.Document.ReferenceSequence = returnedResult.Sequence;
                //to maintain reference sequence
                transfer.Document.UpdateReferenceNumber();
                new EventReceivers().OnDocumentReferenceNumberGenerated(transfer.Document.Id);
            }
            var retValue = new APIResponseViewModel();
            
            if (transfer != null)
            {
                if (transfer.RequestStatus == (long)RequestStatuses.Pending)
                {
                    transfer.RequestStatus = (long)RequestStatuses.Accepted;

                    transfer.UpdateRequestStatus();
                    retValue.Success = true;

                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Accept, currentUserId);
                    var strucureName = transfer.ToStructure.Name;
                    switch (language)
                    {
                        case Language.AR:
                            strucureName = transfer.ToStructure.NameAr;
                            break;
                        case Language.FR:
                            strucureName = transfer.ToStructure.NameFr;
                            break;
                        default:
                            strucureName = transfer.ToStructure.Name;
                            break;
                    }
                    ManageActivityLog.AddActivityLog(transfer.ExportedDocumentId.Value, transfer.Id, (int)ActivityLogs.Accept, currentUserId, note: "By structure: " + strucureName);
                }
                else
                {
                    retValue.Success = false;
                    retValue.Message = TranslationUtility.Translate("CantAcceptNotPending", language);
                }

            }
            else
            {
                retValue.Success = false;
                retValue.Message = TranslationUtility.Translate("RequestNotFound", language);
            }
            return retValue;
        }



        public static APIResponseViewModel RejectRequest(long userId, long id, string rejectionReason, Language language = Language.EN)
        {
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeDocumentAndTostructure(id);
            
            var retValue =new APIResponseViewModel();
            if (transfer != null)
            {
                if (transfer.RequestStatus == (long)RequestStatuses.Pending)
                {
                    transfer.RequestStatus = (long)RequestStatuses.Rejected;
                    transfer.RejectionReason = rejectionReason;
                    transfer.RejectedDate = DateTime.Now;
                    Document document = new Document().FindIncludeAll(transfer.DocumentId.Value);
                    document.ModifiedDate = DateTime.Now;
                    document.Update();
                    transfer.UpdateRequestStatus();
                    retValue.Success = true;

                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Reject, currentUserId);
                    ManageActivityLog.AddActivityLog(transfer.ExportedDocumentId.Value, null, (int)ActivityLogs.Reject, currentUserId, note: TranslationUtility.Translate("ByStructure", language) + " : " + transfer.ToStructure.Name + " " + TranslationUtility.Translate("RejectReason", language) + " " + transfer.RejectionReason);
                }
                else
                {
                    retValue.Success = false;
                    retValue.Message = TranslationUtility.Translate("CantRejectNotPending", language);
                }
            }
            else
            {
                retValue.Success = false;
                retValue.Message = TranslationUtility.Translate("RequestNotFound", language);
            }
            return retValue;
        }


        /// <summary>
        /// List transfer history
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<(int, List<TransferListViewModel>)> ListTransferHistory(int startIndex, int pageSize, long documentId, long? transferId, long userId, int roleId, long? structureId, List<long> structureIds,
    bool isStructureReceiver, short privacyLevel, long? delegationId = null, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            var retValue = (0, new List<TransferListViewModel>());
            var currentUserId = userId;
            DateTime? transferDate = null;
            if (transferId != null)
            {
                var transfer = new Transfer().Find(transferId.Value);
                transferDate = transfer.CreatedDate;
            }
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transferDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");

            if (await ManageUserAccess.HaveAccessAsync(documentId, userId, structureIds, isStructureReceiver, privacyLevel) || ifHaveManageCorrespondenceAccess)
            {
                using (Transfer item = new Transfer())
                {
                    var countResult = item.GetTransferHistoryCount(documentId);
                    List<Structure> Structures = new Structure().ListPrivateStructures();
                    var itemList = await item.ListTransferHistoryAsync(startIndex, pageSize, documentId, sortExpression?.OrderByExpression<Transfer>());
                    //var toremove = itemList.Where(t => t.FromStructureId == t.ToStructureId && t.FromStructure.IsPrivate && !structureIds.Contains(t.FromStructureId.Value));
                    //itemList = itemList.Except(toremove).ToList();

                    List<long> toremove = new List<long>();
                    foreach (var transfer in itemList)
                    {
                        var isTransferBetweenDifferentStructures = transfer.FromStructureId != transfer.ToStructureId;
                        var isUserInStructure = structureIds[0] == transfer.ToStructureId.Value || structureIds[0] == transfer.FromStructureId.Value;

                        var transferPrivateStructure = ManageStructure.GetClosestPrivateStructure(transfer.ToStructureId.Value);
                        var isStructurePublic = transferPrivateStructure == null;
                        isUserInStructure = !isStructurePublic && ManageStructure.IsParentToStructure(transferPrivateStructure.Id, structureIds[0]);

                        if (!(isStructurePublic || isTransferBetweenDifferentStructures ||
                            (!isTransferBetweenDifferentStructures && isUserInStructure)))
                        {
                            toremove.Add(transfer.Id);
                        }
                    }
                    itemList.RemoveAll(t => toremove.Contains(t.Id));

                    retValue = (await countResult, itemList.Select(t =>
                    {
                        var fromStructure = string.Empty;
                        if (t.FromStructureId.HasValue)
                        {
                            fromStructure = t.FromStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                            {
                                fromStructure = t.FromStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr ))
                            {
                                fromStructure = t.FromStructure.NameFr;
                            }
                        }
                        var toStructure = string.Empty;
                        if (t.ToStructureId.HasValue)
                        {
                            toStructure = t.ToStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                            {
                                toStructure = t.ToStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                            {
                                toStructure = t.ToStructure.NameFr;
                            }
                        }
                        var isOverdue = false;
                        if (t.DueDate.HasValue && t.DueDate.Value.Date != t.CreatedDate.Date)
                        {
                            if (t.ClosedDate.HasValue)
                            {
                                isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                            }
                            else
                            {
                                isOverdue = DateTime.Now > t.DueDate.Value;
                            }
                        }
                        var transferListViewModel = new TransferListViewModel
                        {
                            Id = t.Id,
                            DocumentId = t.DocumentId.Value,
                            Subject = t.Document.Subject,
                            CategoryId = t.Document.CategoryId,
                            ReferenceNumber = t.Document.ReferenceNumber,
                            TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                            TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                            CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                            DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Status = t.Document.StatusId,
                            IsOverDue = isOverdue,
                            IsRead = t.OpenedDate != null ? true : false,
                            SentToUser = t.ToUserId != null ? true : false,
                            SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                            FromStructure = fromStructure,
                            ToStructure = toStructure,
                            OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            CreatedByUser = language == Language.EN ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                            Instruction = (userId != t.FromUserId && (t.ToUserId != null && userId != t.ToUserId) && t.PrivateInstruction == true) || (!structureIds.Contains((long)t.ToStructureId) && t.PrivateInstruction == true) ? null : t.Instruction,
                            VoiceNote = t.VoiceNoteStorageId != null ?
                            !t.VoiceNotePrivacy ? true : (t.ToUserId == userId || (!t.ToUserId.HasValue && structureIds.Contains(t.ToStructureId.Value) && isStructureReceiver && t.Document.Privacy.Level <= privacyLevel) || t.FromUserId == userId)
                            : false,
                            FromStructureId = t.FromStructureId,
                            ToStructureId = t.ToStructureId,
                            FromUser = "",
                            ToUser = "",
                        };

                        // Include user fields only if not in private structures or specific conditions met
                        if (!Structures.Any(s => s.Id == t.FromStructureId) ||
                            (Structures.Any(s => s.Id == t.FromStructureId) && (t.FromStructureId == structureIds[0] || t.ToStructureId == structureIds[0])))
                        {
                            transferListViewModel.FromUserId = t.FromUserId;
                            transferListViewModel.FromUser = t.FromUserId.HasValue ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty;
                            if (t.FromDelegatedUserId != null)
                            {
                                var from = $" {IdentityHelperExtension.GetFullName(t.FromDelegatedUserId.Value, language)}";

                                if (t.FromDelegatedUserId.Value == currentUserId)
                                    from = TranslationUtility.Translate("You", language);

                                transferListViewModel.FromUser = from + " " + TranslationUtility.Translate("OnBehalfOf", language) + " " + (t.FromUserId.HasValue ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty);
                            }
                        }
                        if (!Structures.Any(s => s.Id == t.ToStructureId) ||
                            (Structures.Any(s => s.Id == t.ToStructureId) && (t.FromStructureId == structureIds[0] || t.ToStructureId == structureIds[0])))
                        {
                            transferListViewModel.ToUserId = t.ToUserId;
                            transferListViewModel.ToUser = t.ToUserId.HasValue ? (language == Language.EN ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty;
                        }
                        return transferListViewModel;
                    }).ToList());
                    ManageActivityLog.AddFullActivityLog(documentId, transferId, (int)ActivityLogs.ViewTransfersHistory, currentUserId, "", "");
                }
            }
            return retValue;
        }

        /// <summary>
        /// Recall transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static bool Recall(long userId, long id, long? delegationId, string note, Language lang = Language.EN, bool fromExported = false)
        {
            bool retValue = false;
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeToUserToStructure(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
            }
            if ( transfer != null && (transfer.FromUserId == userId||fromExported))
            {
                transfer.RequestStatus = (long)RequestStatuses.Recalled;
                transfer.UpdateRequestStatus();
                retValue = true;
            }
            if (transfer != null && transfer.FromUserId == userId && transfer.OpenedDate == null && transfer.ClosedDate == null)
            {
                
                if (transfer.ParentTransferId != default)
                {
                    var parentTransfer = new Transfer().Find((long)transfer.ParentTransferId);
                    parentTransfer.ClosedDate = null;
                    parentTransfer.StatusId = (short)DocumentStatus.InProgress;
                    parentTransfer.UpdateStatusAndClosedDate();
                    //Logs are deleted on transfer.Delete() due to cascade relation, ParentTransferId was used instead
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);

                }
                else
                {
                    ManageDocument.UpdateDocumentStatusById(transfer.DocumentId.Value, DocumentStatus.Draft);
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);
                }
                new Attachment().ResetAttachmentsTransfer(id);
                if (!fromExported)
                    transfer.Delete();
                
                    

                retValue = true;
            }
            return retValue;
        }


        /// <summary>
        /// Recall transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public async static Task<bool> RecallWithNewTransfer(long userId, long structureId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel ,long id, long? delegationId, string note, IHubContext<CommunicationHub> _hub, Language lang = Language.EN, bool fromExported = false)
        {
            List<TransferedDocumentModel> result = new List<TransferedDocumentModel>();
            List<TransferModel> transfers = new List<TransferModel>();
            bool retValue = false;
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeToUserToStructure(id);
            var transferModel = new TransferModel()
            {
                CCed = transfer.Cced,
                DocumentId = transfer.DocumentId.Value,
                DueDate = transfer.DueDate.ToString(),
                FromStructureId = transfer.FromStructureId,
                Id = transfer.Id,
                Instruction = note,
                ParentTransferId = transfer.ParentTransferId,
                PriorityId = transfer.PriorityId != null ? transfer.PriorityId.Value: default,
                PrivateInstruction = transfer.PrivateInstruction,
                PurposeId = (short)Configuration.RecallPurpose /*new Purpose().FindByName("Recall").Id*/ !=0 ?(short) Configuration.RecallPurpose : transfer.PurposeId.Value,
                ToStructureId = transfer.FromStructureId,
                ToUserId = transfer.FromUserId.Value,
                FromRecall = true
                

            };
            if (transfer != null)
                transfers.Add(transferModel);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
            }

            if (transfer != null && transfer.FromUserId == userId && transfer.OpenedDate == null && transfer.ClosedDate == null && !fromExported)
            {
                //if we recall a transfer replied to myself or recall a transfer sent to myself
                if (transfer.ToUserId == userId)
                {
                    transfer.StatusId = (short)DocumentStatus.Completed;
                    transfer.ClosedDate = DateTime.Now;
                    transfer.UpdateStatusAndClosedDate();
                }
                result = await ManageTransfer.Transfer(userId, structureId, structureIds, isStructureSender, isStructureReceiver, privacyLevel, transfers, false, delegationId,language: lang, fromSendAddCC: true);
                for (int i = 0; i < result.Count; i++)
                {
                    if (result[i].Updated)
                    {
                        if (result[i].DocumentId > 0)
                        {
                            if (result[i].ParentTransferId.HasValue)
                            {
                                _hub.Transfer(result[i].ParentTransferId.Value, lang);
                            }
                            else
                            {
                                _hub.Send(result[i].DocumentId, lang);
                            }
                        }
                    }
                }

                ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);
                if (transfer.ExportedDocumentId.HasValue)
                    ManageActivityLog.AddActivityLog(transfer.ExportedDocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);

                new Attachment().ResetTransferToDelete(id);
                if (!fromExported)
                {
                    transfer.Delete();
                }
                else
                {
                    AddRejectionReason(transfer, note);
                }



                retValue = true;
            }
            else if (transfer != null && fromExported)
            {
                AddRejectionReason(transfer, note);
                retValue = true;
            }

            return retValue;
        }

        /// <summary>
        /// Recall transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static APIResponseViewModel StructureUserRecall(long userId, long id, long? delegationId, string note, Language lang = Language.EN)
        {
            APIResponseViewModel retValue = new APIResponseViewModel() {Success=false };
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeToUserToStructure(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
            }
            if (transfer == null || transfer.OpenedDate != null || transfer.ClosedDate != null)
            {
                retValue.Success = false;
                retValue.Message = "CannotBeRecalled";
                return retValue;
            }
            if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsWithOriginal(transfer.DocumentId.Value))
            {
                retValue.Success = false;
                retValue.Message = "FileInUse";
                return retValue;
            }


            if (transfer != null && transfer.FromUserId == userId && transfer.ClosedDate == null)//transfer.OpenedDate == null
            {
                if (transfer.ParentTransferId != default)
                {
                    var parentTransfer = new Transfer().Find((long)transfer.ParentTransferId);
                    parentTransfer.ClosedDate = null;
                    parentTransfer.StatusId = (short)DocumentStatus.InProgress;
                    parentTransfer.UpdateStatusAndClosedDate();
                    //Logs are deleted on transfer.Delete() due to cascade relation, ParentTransferId was used instead
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);
                }
                else
                {
                    ManageDocument.UpdateDocumentStatusById(transfer.DocumentId.Value, DocumentStatus.Draft);
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);

                }
                transfer.Delete();
                retValue.Success = true;
            }
            return retValue;
        }

        /// <summary>
        /// Complete transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="ids"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static List<CompletedDocumentTransferModel> Complete(long userId, List<long> ids, List<long> structureIds, bool isStructureReceiver, short privacyLevel, string completeReasonNote = null ,long? delegationId = null, bool overrideAccess = false, bool? completeFollowUp = false, bool fromExport = false, Language? language = null, List<long?> documentIds = null)
        {
    // we added the completeReasonNote to be initially null Incase, there was another complete button in the system with no completeReasonNote value
            List<CompletedDocumentTransferModel> retValue = new List<CompletedDocumentTransferModel>();

            if (completeFollowUp.Value && documentIds != null && documentIds[0] != null)
            {
                List<long> documentTransfersIds = new List<long>();

                foreach (long id in documentIds)
                {
                    var document = new Document().FindIncludeAll(id);
                    if (document.CreatedByUserId != userId)
                    {
                        CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();
                        model.UncompletedDocumentReferenceNumber = document.Subject;
                        Language lang = language.HasValue ? language.Value : Language.EN;
                        model.Message = TranslationUtility.Translate("TransferHasDifferentOwnerOrIsCarbonCopy", lang);
                        retValue.Add(model);
                        return retValue;
                    }
                    documentTransfersIds = (List<long>)((new Transfer().ListByDocumentId(id)).Select(a => a.Id)).ToList();

                    foreach (var item in documentTransfersIds)
                    {
                        var transfer = new Transfer().FindIncludeDocumentAndCategory(item);
                        retValue.AddRange(CompleteATransfer(userId, transfer, structureIds, isStructureReceiver, privacyLevel, completeReasonNote, overrideAccess, fromExport, language));
                    }
                }
            }
            else
            {
                if (delegationId.HasValue)
                {
                    var transfers = new Transfer().ListByIds(ids);
                    var delegation = ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfers);
                    if (delegation != null)
                    {
                        userId = delegation.FromUserId;
                        structureIds = delegation.StructureIds;
                        isStructureReceiver = delegation.IsStructureReceiver;
                        privacyLevel = delegation.PrivacyLevel;
                    }
                }
                foreach (long id in ids)
                {
                    var transfer = new Transfer().FindIncludeDocumentAndCategory(id);
                    retValue.AddRange(CompleteATransfer(userId, transfer, structureIds, isStructureReceiver, privacyLevel, completeReasonNote, overrideAccess, fromExport, language));
                }
            }
            return retValue;
        }

        public static List<CompletedDocumentTransferModel> CompleteATransfer(long userId, Transfer transfer, List<long> structureIds, bool isStructureReceiver, short privacyLevel, string completeReasonNote= null, bool overrideAccess = false, bool fromExport = false,Language? language = null )
        {
      // we added the completeReasonNote to be initially null Incase, there was another complete button in the system with no completeReasonNote value

            List<CompletedDocumentTransferModel> retValue = new List<CompletedDocumentTransferModel>();
            var currentUserId = userId;

            if (ManageUserAccess.HaveTransferAccess(transfer.Id, userId, structureIds, isStructureReceiver, privacyLevel) || overrideAccess)
            {
                bool isFollowUp = (transfer.Document.CategoryId == Configuration.FollowUpCategory);

                CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();
                bool isInternalBroadcast = false;
                dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(transfer.Document.Category.BasicAttribute);
                if (!ReferenceEquals(null, categoryBasicAttribute) && !isFollowUp)
                {
                    foreach (var attr in categoryBasicAttribute.Root)
                    {
                        if (attr.Name.Value == "ReceivingEntity")
                        {
                            if (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value == true && attr.Type.Value == "internal")
                            {
                                isInternalBroadcast = true;
                            }
                            break;
                        }
                    }
                }

                if (Configuration.AttachmentEditable)
                {
                    if (new Document().CheckHasLockedAttachmentsByUser(transfer.DocumentId.Value, userId))
                    {
                        model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                        model.TransferId = transfer.Id;
                        Language lang = language.HasValue ? language.Value : Language.EN;
                        model.Message = TranslationUtility.Translate("HasLockedAttachmentsByUser", lang);
                        retValue.Add(model);
                        return retValue;
                    }
                }
                else if (new Document().CheckOriginalDocumentLockedByUser(transfer.DocumentId.Value, userId))
                {
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    model.TransferId = transfer.Id;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    model.Message = TranslationUtility.Translate("OriginalDocumentLockedByUser", lang);
                    retValue.Add(model);
                    return retValue;
                }

                if (string.IsNullOrEmpty(transfer.Document.ReferenceNumber))
                {
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    model.TransferId = transfer.Id;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    model.Message = TranslationUtility.Translate("NoReferenceNumber", lang);
                    retValue.Add(model);
                    return retValue;
                }

                model.DocumentAttachmentIdHasValue = transfer.Document.AttachmentId.HasValue;
                if (((transfer.OwnerUserId.HasValue && transfer.OwnerUserId.Value != userId) || (transfer.Cced && !isInternalBroadcast)) && !isFollowUp && !fromExport)
                {
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    model.TransferId = transfer.Id;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    model.Message = TranslationUtility.Translate("TransferHasDifferentOwnerOrIsCarbonCopy", lang);
                    retValue.Add(model);
                    return retValue;
                }

                if (transfer.WorkflowStepId != null)
                {
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    model.TransferId = transfer.Id;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    model.Message = TranslationUtility.Translate("WorkflowCannotbeCompleted", lang);
                    retValue.Add(model);
                    return retValue;
                }

                transfer.ClosedDate = DateTime.Now;
                transfer.StatusId = (short)DocumentStatus.Completed;
                bool isLastOpenedTransfer = new Transfer().CheckIfLastOpenTransfer(transfer.Id, transfer.DocumentId, isInternalBroadcast);
                if (isLastOpenedTransfer && transfer.Document.AttachmentId.HasValue)
                {
                    if (transfer.Document.StatusId == (short)DocumentStatus.InProgress)
                    {
                        transfer.Document.StatusId = (short)DocumentStatus.Completed;
                        transfer.Document.ClosedDate = DateTime.Now;
                        transfer.Document.UpdateStatusAndClosedDate();
                        transfer.UpdateStatusAndClosedDate();
                        model.Updated = true;
                        model.TransferId = transfer.Id;
                        ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Complete, currentUserId, note: completeReasonNote);
                    }
                    else
                    {
                        model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                        model.TransferId = transfer.Id;
                        Language lang = language.HasValue ? language.Value : Language.EN;
                        model.Message = TranslationUtility.Translate("CantCompleteIsLastOpenedTransferNotInProgress", lang);
                        retValue.Add(model);
                        return retValue;
                    }

                }
                else if (!transfer.Document.AttachmentId.HasValue)
                {
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    model.TransferId = transfer.Id;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    model.Message = TranslationUtility.Translate("CorrespondenceNotCompleteNoOriginalMail", lang);
                    retValue.Add(model);
                    return retValue;
                }
                else if (!isLastOpenedTransfer)
                {
                    transfer.UpdateStatusAndClosedDate();
                    model.Updated = true;
                    model.TransferId = transfer.Id;
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Complete, currentUserId,note: completeReasonNote);
                }
                else
                {
                    model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    model.TransferId = transfer.Id;
                }
                retValue.Add(model);
            }

            return retValue;
        }

        /// <summary>
        /// List in progress transfers. 
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <param name="structureId"></param>
        /// <param name="fromStructure"></param>
        /// <returns></returns>
        public static (int, List<TransferListViewModel>) ListSent(int startIndex, int pageSize, long userId, long structureId, long? delegationId = null, ExpressionBuilderFilters filter = null,
            List<SortExpression> sortExpression = null, Language language = Language.EN, bool fromStructure = false)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", delegation.PrivacyLevel, Operator.LessThanOrEqualTo);


            }
            using (Transfer item = new Transfer())
            {
                if (IsInboxModeWithGrouping() && filter.Count() == 0)
                    filter.Add("Id", 0, Operator.GreaterThan);

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;

                Task<int> countResult;
                List<Transfer> itemList;
                //check if the Inbox Mode parametr is Inbox Default With Grouping so get data with document grouping
                if (IsInboxModeWithGrouping())
                {
                    countResult = item.GetSentCountWithGrouping(structureId, userId, filterExp, fromStructure);
                    itemList = item.ListSentWithGrouping(startIndex, pageSize, userId, structureId, filterExp, sortExpression?.OrderByExpression<Document>(), fromStructure);
                }
                else
                {
                    countResult = item.GetSentCount(structureId, userId, filterExp, fromStructure);
                    itemList = item.ListSent(startIndex, pageSize, userId, structureId, filterExp, sortExpression?.OrderByExpression<Transfer>(), fromStructure);
                }

                return (countResult.Result, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr ))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now > t.DueDate.Value;
                        }
                    }
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }
                    List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.Document.DocumentReceiverEntity)
                    {
                        if (!receiver.EntityGroupId.HasValue && !receiver.StructureId.HasValue)
                            continue;
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr ) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr ) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                        receivers.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            IsEntityGroup = isEntityGroup,
                            Text = text
                        });
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    string linkedCorrespondencesReferenceNo = string.Empty;

                    foreach (var linkedCorrespondence in t.Document.LinkedDocumentDocument)
                    {
                        if (!string.IsNullOrEmpty(linkedCorrespondence.LinkedDocumentNavigation?.ReferenceNumber))
                            linkedCorrespondencesReferenceNo += (linkedCorrespondencesReferenceNo.Length > 0 ? ", " : "") + linkedCorrespondence.LinkedDocumentNavigation.ReferenceNumber;
                    }
                    return new TransferListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId.Value,
                        Subject = t.Document.Subject,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.CategoryId != Configuration.FollowUpCategory ? t.Document.ReferenceNumber : linkedCorrespondencesReferenceNo,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                        CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        Status = t.Document.StatusId,
                        TransferStatusId = t.StatusId.Value,
                        OwnerUserId = t.OwnerUserId,
                        OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                        IsOverDue = isOverdue,
                        IsRead = t.OpenedDate != null ? true : false,
                        IsLocked = t.OwnerUserId != null ? true : false,
                        SentToUser = t.ToUserId != null ? true : false,
                        SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ? (language == Language.EN ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        LockedBy = t.OwnerUser != null ? (language == Language.EN ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                        LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (language == Language.EN ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                        LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        CreatedByUser = language == Language.EN ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                        CreatedByUserId = t.Document.CreatedByUserId,
                        Instruction = t.Instruction,
                        Cced = t.Cced,
                        PurposeId = t.PurposeId,
                        PriorityId = t.Document.PriorityId,
                        PrivacyId = t.Document.PrivacyId,
                        ImportanceId = t.Document.ImportanceId,
                        SendingEntity = sendingEntity,
                        CreatedByStructureId = t.Document.CreatedByStructureId,
                        ReceivingEntityId = receivers,
                        ReceivingEntity = receivingEntity,
                        DocumentForm = t.Document.DocumentForm,
                        Note = t.Note,
                        Body = t.Document.DocumentForm.Body != null ? t.Document.DocumentForm.Body : string.Empty
                        //Body = t.DocumentForm != null ? item.DocumentForm.Body : string.Empty,


                    };
                }).ToList());
            }
        }

        /// <summary>
        /// Get in progress transfers count. 
        /// Must be created by the user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static (int Total, int Today) GetSentCounts(long userId, List<long> structureIds, long? loggedInStructureId, long? delegationId = null, ExpressionBuilderFilters filter = null, bool fromStructure = false)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", delegation.PrivacyLevel, Operator.LessThanOrEqualTo);

            }

            using (var item = new Transfer())
            {
                if (Core.Configuration.EnablePerStructure && loggedInStructureId != null)
                    filter.Add("FromStructureId", loggedInStructureId, Operator.Equals);

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                Task<int> inboxTotal;
                int inboxToday;
                //check if the Inbox Mode parametr is Inbox Default With Grouping so get counts with document grouping
                if (IsInboxModeWithGrouping())
                {

                    inboxTotal = item.GetSentCountWithGrouping(loggedInStructureId, userId, filterExp, fromStructure);
                    inboxToday = item.GetSentTodayCountWithGrouping(loggedInStructureId, userId, filterExp, fromStructure);
                }
                else
                {

                    inboxTotal = item.GetSentCount(loggedInStructureId, userId, filterExp, fromStructure);
                    inboxToday = item.GetSentTodayCount(loggedInStructureId, userId, filterExp, fromStructure);
                }
                return (inboxTotal.Result, inboxToday);
            }
        }

        /// <summary>
        /// Transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="transfers"></param>
        /// <param name="delegationId"></param>
        /// <param name="maintainTransfer"></param>
        /// <param name="StructureId"></param>
        /// <returns></returns>
        public static async Task<List<TransferedDocumentModel>> Transfer(long
            userId, long structureId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel,
            List<TransferModel> transfers, bool maintainTransfer = false, long? delegationId = null, Action<List<TransferModel>>? onAfterAddition = null, Language language = Language.EN, bool fromSendAddCC = false)
        {
            List<TransferedDocumentModel> retValue = new List<TransferedDocumentModel>();
            List<TransferModel> toTransfer = new List<TransferModel>();
            long? FromDelegatedUserId = null;
            DateTime? ParentTranssferDate = null;
            if (transfers[0].ParentTransferId != null)
            {
                var parentTransfer = new Transfer().Find(transfers[0].ParentTransferId.Value);
                ParentTranssferDate = parentTransfer.CreatedDate;
            }


            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, ParentTranssferDate) : null;
            if (delegation != null)
            {
                FromDelegatedUserId = userId;
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureSender = delegation.IsStructureSender;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }

            if (transfers.Count > 0)
            {
                var transferdDoc = new Document().FindIncludeDocumentForm(transfers[0].DocumentId);

                var meetingIds = new List<long>()
                {
                    long.Parse(ManageParameter.FindByKeyWord("SepResolutionId").Content),
                    long.Parse(ManageParameter.FindByKeyWord("MeetingAgendaId").Content),
                    long.Parse(ManageParameter.FindByKeyWord("MeetingResolutionId").Content),
                    long.Parse(ManageParameter.FindByKeyWord("MeetingMinutesId").Content)
                };
                var currentStructreId = Intalio.CTS.Core.Utility.IdentityHelperExtension.getCurrentLoggedInStructure(userId);
                if (meetingIds.Contains(transferdDoc.CategoryId) && !transfers.Any(x => x.ToUserId == userId && x.ToStructureId == currentStructreId))
                {
                    TransferModel currentUserTarnsfer = new TransferModel()
                    {
                        DocumentId = transfers[0].DocumentId,
                        FromStructureId = transfers[0].FromStructureId,
                        ToUserId = userId,
                        ToStructureId = currentStructreId,
                        PurposeId = transfers[0].PurposeId,
                        PriorityId = transfers[0].PriorityId,
                        FromRecall = transfers[0].FromRecall
                    };
                    transfers.Add(currentUserTarnsfer);
                }

                List<TransferModel> transfersData = new List<TransferModel>();
                foreach (var transfer in transfers)
                {
                    var transferData = CheckAutoForwardTransfer(transfer, userId);
                    transfersData.Add(transferData);
                }


                var haveAccess = false;
                if (delegation != null)
                    haveAccess = (ManageUserAccess.CanSend(structureId, userId, structureIds, transfersData.Select(t => t.ToStructureId.Value).Distinct().ToList(), isStructureSender) && DateTime.Now.Date >= delegation.FromDate && DateTime.Now.Date <= delegation.ToDate);
                else
                    haveAccess = ManageUserAccess.CanSend(structureId, userId, structureIds, transfersData.Select(t => t.ToStructureId.Value).Distinct().ToList(), isStructureSender);
                if (!haveAccess)
                {
                    retValue = new List<TransferedDocumentModel>();
                    retValue.Add(new TransferedDocumentModel
                    {
                        Updated = false,
                        Message = string.Empty
                    });
                    return retValue;
                }
                var IsDocumentCompleted = ManageDocument.IsDocumentCompleted(transfers.FirstOrDefault().DocumentId);
                if (IsDocumentCompleted)
                {
                    retValue = new List<TransferedDocumentModel>();
                    retValue.Add(new TransferedDocumentModel
                    {
                        Updated = false,
                        Message = "DocumentIsCompleted"
                    });
                    return retValue;
                }

                bool referenceNumperUpdated = false;
                //looping through transfer to check security
                var groupedTransfers = transfers.GroupBy(t => t.DocumentId);//group by documentId
                long DocumentId = groupedTransfers.FirstOrDefault().Key;
                var item = new Document().FindIncludeDocumentForm(DocumentId);
                long ReferenceNumberGeneratorType = ManageCategory.GetReferenceNumberGeneratorTypeByCategoryId(item.CategoryId);
                foreach (var groupedTransfer in groupedTransfers)
                {
                    long transferDocumentId = groupedTransfer.Key;
                    if (ManageUserAccess.HaveAccess(transferDocumentId, userId, structureIds, isStructureReceiver, privacyLevel))
                    {
                        if (string.IsNullOrEmpty(item.ReferenceNumber))
                        {
                            try
                            {
                                if ((ReferenceNumberGeneratorType == 1) || ((ReferenceNumberGeneratorType == 2) && (item.IsSigned == true)))
                                {
                                    var returnedResult = new CategoryReferenceCounter().Generate(item.CategoryId, userId, item.CreatedByStructureId, item.Id, null, Language.EN);
                                    item.ReferenceNumber = returnedResult.Reference;
                                    item.ReferenceSequence = returnedResult.Sequence;
                                    referenceNumperUpdated = true;
                                }
                            }
                            catch (Exception)
                            {
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = false,
                                    Message = "CantGenerateReferenceNumber"
                                });
                                return retValue;
                            }
                        }

                        dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(new Document().FindIncludeCategory(transferDocumentId).Category.BasicAttribute);
                        if (!ReferenceEquals(null, categoryBasicAttribute))
                        {
                            bool isBroadcast = false;
                            foreach (var attr in categoryBasicAttribute.Root)
                            {
                                if (attr.Name.Value == "ReceivingEntity")
                                {
                                    if (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value == true && attr.Type.Value == "internal")
                                    {
                                        retValue.Add(new TransferedDocumentModel
                                        {
                                            Updated = false,
                                            DocumentId = transferDocumentId,
                                            Message = string.Empty
                                        });
                                        isBroadcast = true;
                                    }
                                    break;
                                }
                            }
                            if (isBroadcast)
                            {
                                continue;
                            }
                        }
                        var groupedTransfersParentId = groupedTransfer.ToList().GroupBy(t => t.ParentTransferId);//group by parent transfer Id
                        foreach (var group in groupedTransfersParentId)
                        {
                            long? parentTransferId = group.Key;


                            if (new Document().CheckOriginalDocumentLockedByUser(transferDocumentId, userId))
                            {
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = false,
                                    ParentTransferId = parentTransferId,
                                    DocumentId = transferDocumentId,
                                    Message = "OriginalFileInUse"
                                });
                            }
                            else if (new Transfer().CheckSameTransferIsExist(transferDocumentId, parentTransferId, groupedTransfer.Select(g => g.ToStructureId).FirstOrDefault(), groupedTransfer.Select(g => g.ToUserId).FirstOrDefault(), userId))
                            {
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = false,
                                    Message = "TransferIsExist"
                                });
                            }
                            else if (Configuration.AttachmentEditable)
                            {
                                if (new Document().CheckHasLockedAttachmentsByUser(transferDocumentId, userId))
                                {
                                    retValue.Add(new TransferedDocumentModel
                                    {
                                        Updated = false,
                                        ParentTransferId = parentTransferId,
                                        DocumentId = transferDocumentId,
                                        Message = "FileInUse"
                                    });
                                }
                                else if (parentTransferId.HasValue)
                                {
                                    if (new Transfer().IsLockedByUserOrNotLocked(parentTransferId.Value, userId))
                                    {
                                        toTransfer.AddRange(group.ToList());
                                        retValue.Add(new TransferedDocumentModel
                                        {
                                            Updated = true,
                                            ParentTransferId = parentTransferId,
                                            DocumentId = transferDocumentId,
                                            ToStructureId = groupedTransfer.Select(g => g.ToStructureId).FirstOrDefault().Value,
                                            Message = string.Empty
                                        });
                                    }
                                    else
                                    {
                                        retValue.Add(new TransferedDocumentModel
                                        {
                                            Updated = false,
                                            ParentTransferId = parentTransferId,
                                            DocumentId = transferDocumentId,
                                            Message = "TransferIsLocked"
                                        });
                                    }
                                }
                                else
                                {
                                    toTransfer.AddRange(group.ToList());
                                    retValue.Add(new TransferedDocumentModel
                                    {
                                        Updated = true,
                                        ParentTransferId = parentTransferId,
                                        DocumentId = transferDocumentId,
                                        Message = string.Empty
                                    });
                                }
                            }
                            else if (parentTransferId.HasValue)
                            {
                                if (new Transfer().IsLockedByUserOrNotLocked(parentTransferId.Value, userId))
                                {
                                    toTransfer.AddRange(group.ToList());
                                    retValue.Add(new TransferedDocumentModel
                                    {
                                        Updated = true,
                                        ParentTransferId = parentTransferId,
                                        DocumentId = transferDocumentId,
                                        Message = string.Empty
                                    });
                                }
                                else
                                {
                                    retValue.Add(new TransferedDocumentModel
                                    {
                                        Updated = false,
                                        ParentTransferId = parentTransferId,
                                        DocumentId = transferDocumentId,
                                        Message = "TransferIsLocked"
                                    });
                                }
                            }
                            else
                            {
                                toTransfer.AddRange(group.ToList());
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = true,
                                    ParentTransferId = parentTransferId,
                                    DocumentId = transferDocumentId,
                                    Message = string.Empty
                                });
                            }
                        }
                    }
                    else
                    {
                        retValue.Add(new TransferedDocumentModel
                        {
                            Updated = false,
                            DocumentId = transferDocumentId,
                            Message = string.Empty
                        });
                    }
                }
                if (toTransfer.Count > 0)
                {
                    bool result;
                    // check  closeParentTransfer keep maintain or close it
                    if (fromSendAddCC)
                    {
                         result = await Create(userId, toTransfer, FromDelegatedUserId, maintainTransfer, true, language);
                    }
                    else
                    {
                        result = await Create(userId, toTransfer, FromDelegatedUserId, !maintainTransfer, true, language);
                    }
                    
                    if (!result)
                    {
                        retValue = new List<TransferedDocumentModel>();
                        retValue.Add(new TransferedDocumentModel
                        {
                            Updated = false,
                            Message = string.Empty
                        });
                    }
                    else
                    {
                        onAfterAddition?.Invoke(toTransfer);
                        if (referenceNumperUpdated && ((ReferenceNumberGeneratorType == 1) || ((ReferenceNumberGeneratorType == 2) && (item.IsSigned == true))))
                        {
                            item.UpdateReferenceNumber();
                            new EventReceivers().OnDocumentReferenceNumberGenerated(item.Id);
                        }
                    }

                    if (result && toTransfer.Any(t => t.followUp))
                    {
                        createFollowUpFromTransfer(toTransfer.Where(t => t.followUp).ToList());
                    }

                }
                else
                {
                    if (retValue.Count == 0)
                    {
                        retValue.Add(new TransferedDocumentModel
                        {
                            Updated = false,
                            Message = string.Empty
                        });
                    }
                    return retValue;
                }
                Document document = new Document().FindIncludeTransfers(DocumentId);
                if (document.AttachmentId != null && document.Category.CategoryReferenceNumberTypeId == 1 && document.Transfer.Count == 1 && Intalio.Core.Helper.IsWord($"{document.AttachmentNavigation.Name}.{document.AttachmentNavigation.Extension}"))
                {
                    await ManageBookmark.FindReplaceBookmarkAndAttachment((long)document.AttachmentId, document.AttachmentNavigation.StorageAttachmentId, DocumentId, userId);

                }
            }            
            return retValue;
        }

        /// <summary>
        /// Dismiss carbon copy(ies) by transfer(s) id(s)
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="ids"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static List<UpdateTransferCheckModel> DismissCarbonCopy(long userId, List<long> ids, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId)
        {
            List<Transfer> transfers = new List<Transfer>();
            List<Transfer> transfersToCheckDelegation = new Transfer().ListByIds(ids);
            List<UpdateTransferCheckModel> retValue = new List<UpdateTransferCheckModel>();
            var currentUserId = userId;
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfersToCheckDelegation) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            foreach (long id in ids)
            {
                UpdateTransferCheckModel model = new UpdateTransferCheckModel();
                if (ManageUserAccess.HaveTransferAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    var transfer = new Transfer().FindIncludeDocumentAndCategory(id);
                    if (transfer != null)
                    {
                        if ((transfer.OwnerUserId.HasValue && transfer.OwnerUserId.Value != userId) || !transfer.Cced)
                        {
                            model.Updated = false;
                            model.TransferId = id;
                            retValue.Add(model);
                            continue;
                        }
                        dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(transfer.Document.Category.BasicAttribute);
                        if (!ReferenceEquals(null, categoryBasicAttribute))
                        {
                            bool isInternalBroadcast = false;
                            foreach (var attr in categoryBasicAttribute.Root)
                            {
                                if (attr.Name.Value == "ReceivingEntity")
                                {
                                    if (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value == true && attr.Type.Value == "internal")
                                    {
                                        model.Updated = false;
                                        model.TransferId = id;
                                        retValue.Add(model);
                                        isInternalBroadcast = true;
                                    }
                                    break;
                                }
                            }
                            if (isInternalBroadcast)
                            {
                                continue;
                            }
                        }

                        if (!transfer.ToUserId.HasValue)
                        {
                            transfer.ToUserId = userId;
                        }
                        transfer.StatusId = (short)DocumentStatus.Completed;
                        model.TransferId = id;
                        model.Updated = true;
                        transfers.Add(transfer);
                        ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.DismissCopy, currentUserId);
                    }
                }
                else
                {
                    model.Updated = false;
                    model.TransferId = id;
                }
                retValue.Add(model);
            }
            if (transfers.Count > 0)
            {
                new Transfer().UpdateClosedDateStatusIdToUserIdByList(transfers);
            }
            return retValue;
        }

        /// <summary>
        /// List in progress transfers for vip mode. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<TransferInboxListViewVipModel> ListInboxVip(int startIndex, long userId, long structureId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
            long? delegationId = null, ExpressionBuilderFilters filter = null, Language language = Language.EN, bool fromStructure = false)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            var categoryIds = new List<short>();
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
            }
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new Transfer().ListInboxVip(startIndex, structureId, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, fromStructure);
            return itemList.Select(t =>
            {
                var fromStructure = string.Empty;
                if (t.FromStructureId.HasValue)
                {
                    fromStructure = t.FromStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                    {
                        fromStructure = t.FromStructure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                    {
                        fromStructure = t.FromStructure.NameFr;
                    }
                }
                var toStructure = string.Empty;
                if (t.ToStructureId.HasValue)
                {
                    toStructure = t.ToStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                    {
                        toStructure = t.ToStructure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                    {
                        toStructure = t.ToStructure.NameFr;
                    }
                }
                var purposeName = string.Empty;
                if (t.PurposeId.HasValue)
                {
                    purposeName = t.Purpose.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.Purpose.NameAr))
                    {
                        purposeName = t.Purpose.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.Purpose.NameFr))
                    {
                        purposeName = t.Purpose.NameFr;
                    }
                }
                var isOverdue = false;
                if (t.DueDate.HasValue)
                {
                    if (t.ClosedDate.HasValue)
                    {
                        isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                    }
                    else
                    {
                        isOverdue = DateTime.Now.Date > t.DueDate.Value;
                    }
                }
                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                foreach (var receiver in t.Document.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr)  ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr)  ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                    }
                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup,
                        Text = text
                    });
                }
                return new TransferInboxListViewVipModel
                {
                    Id = t.Id,
                    DocumentId = t.DocumentId.Value,
                    Subject = t.Document.Subject == null ? "" : t.Document.Subject,
                    CategoryId = t.Document.CategoryId,
                    ReferenceNumber = t.Document.ReferenceNumber,
                    TransferDate = t.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    DocumentDueDate = t.Document.DueDate != null ? t.Document.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    OwnerUserId = t.OwnerUserId,
                    OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                    IsOverDue = isOverdue,
                    IsRead = t.OpenedDate != null ? true : false,
                    IsLocked = t.LockedDate.HasValue && t.OwnerUserId != null ? true : false,
                    SentToUser = t.ToUserId != null ? true : false,
                    SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                    FromUserId = t.FromUserId,
                    FromUser = t.FromUserId.HasValue ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                    FromStructure = fromStructure,
                    LockedBy = t.OwnerUser != null ? (language == Language.EN ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                    LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (language == Language.EN ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                    LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                    OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                    CreatedByUserId = t.Document.CreatedByUserId,
                    Cced = t.Cced,
                    ImportanceId = t.Document.ImportanceId,
                    ToStructureId = t.ToStructureId,
                    ReceivingEntities = receivers,
                    ToStructure = toStructure,
                    PurposeName = purposeName,
                    PriorityId = t.Document.PriorityId,
                    PrivacyId = t.Document.PrivacyId,
                    delegationId = delegationId,
                    RequestStatus = ((RequestStatuses)t.RequestStatus).ToString()
                };
            }).ToList();
        }

        /// <summary>
        /// List closed transfers for vip mode.  
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<TransferCompletedListViewVipModel> ListCompletedVip(int startIndex, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
            long? delegationId = null, ExpressionBuilderFilters filter = null, Language language = Language.EN)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            var categoryIds = new List<short>();
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
            }
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new Transfer().ListCompletedVip(startIndex, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp);
            return itemList.Select(t =>
            {
                var fromStructure = string.Empty;
                if (t.FromStructureId.HasValue)
                {
                    fromStructure = t.FromStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr ))
                    {
                        fromStructure = t.FromStructure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr ))
                    {
                        fromStructure = t.FromStructure.NameFr;
                    }
                }
                var toStructure = string.Empty;
                if (t.ToStructureId.HasValue)
                {
                    toStructure = t.ToStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                    {
                        toStructure = t.ToStructure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr ))
                    {
                        toStructure = t.ToStructure.NameFr;
                    }
                }
                var purposeName = string.Empty;
                if (t.PurposeId.HasValue)
                {
                    purposeName = t.Purpose.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.Purpose.NameAr ))
                    {
                        purposeName = t.Purpose.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.Purpose.NameFr))
                    {
                        purposeName = t.Purpose.NameFr;
                    }
                }
                return new TransferCompletedListViewVipModel
                {
                    Id = t.Id,
                    DocumentId = t.DocumentId.Value,
                    Subject = t.Document.Subject == null ? "" : t.Document.Subject,
                    CategoryId = t.Document.CategoryId,
                    ReferenceNumber = t.Document.ReferenceNumber,
                    TransferDate = t.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    IsRead = t.OpenedDate != null ? true : false,
                    FromUser = t.FromUserId.HasValue ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                    FromStructure = fromStructure,
                    ImportanceId = t.Document.ImportanceId,
                    ToStructure = toStructure,
                    PurposeName = purposeName,
                    PriorityId = t.Document.PriorityId,
                    PrivacyId = t.Document.PrivacyId

                };
            }).ToList();
        }

        /// <summary>
        /// List in progress transfers for vip mode.  
        /// Must be created by the user
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="userId"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static List<TransferSentListViewVipModel> ListSentVip(int startIndex, long userId, long structureId, long? delegationId = null, ExpressionBuilderFilters filter = null, Language language = Language.EN, bool fromStructure = false)
        {
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                if (filter == null)
                    filter = new ExpressionBuilderFilters();

                if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                    filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                else if (!delegation.ShowOldCorespondence)
                    filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                filter.Add("Document.Privacy.Level", delegation.PrivacyLevel, Operator.LessThanOrEqualTo);
            }
            var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
            var itemList = new Transfer().ListSentVip(startIndex, userId, structureId, filterExp, fromStructure);
            return itemList.Select(t =>
            {
                var toStructure = string.Empty;
                if (t.ToStructureId.HasValue)
                {
                    toStructure = t.ToStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr ))
                    {
                        toStructure = t.ToStructure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr ))
                    {
                        toStructure = t.ToStructure.NameFr;
                    }
                }
                var fromStructure = string.Empty;
                if (t.FromStructureId.HasValue)
                {
                    fromStructure = t.FromStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr ))
                    {
                        fromStructure = t.FromStructure.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr ))
                    {
                        fromStructure = t.FromStructure.NameFr;
                    }
                }
                var purposeName = string.Empty;
                if (t.PurposeId.HasValue)
                {
                    purposeName = t.Purpose.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(t.Purpose.NameAr))
                    {
                        purposeName = t.Purpose.NameAr;
                    }
                    else if (language == Language.FR && !string.IsNullOrEmpty(t.Purpose.NameFr))
                    {
                        purposeName = t.Purpose.NameFr;
                    }
                }
                return new TransferSentListViewVipModel
                {
                    Id = t.Id,
                    DocumentId = t.DocumentId.Value,
                    Subject = t.Document.Subject,
                    CategoryId = t.Document.CategoryId,
                    ReferenceNumber = t.Document.ReferenceNumber,
                    TransferDate = t.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    IsRead = t.OpenedDate != null ? true : false,
                    ToUser = t.ToUserId.HasValue ? (language == Language.EN ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUser.Id, language)}") : string.Empty,
                    ToStructure = toStructure,
                    ImportanceId = t.Document.ImportanceId,
                    FromStructure = fromStructure,
                    PurposeName = purposeName,
                    PriorityId = t.Document.PriorityId,
                    PrivacyId = t.Document.PrivacyId,
                };
            }).ToList();
        }

        /// <summary>
        /// Broadcast Send
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="transferId"></param>
        /// <param name="dueDate"></param>
        /// <param name="instruction"></param>
        /// <param name="structureId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<(bool Sent, string Message)> BroadcastSend(long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long id, long? transferId, DateTime? dueDate, string instruction, long structureId, long? delegationId = null)
        {
            bool retValue = false;
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureSender = delegation.IsStructureSender;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (ManageUserAccess.HaveAccess(id, transferId, userId, structureIds, isStructureReceiver, privacyLevel))
            {
                if (Configuration.AttachmentEditable)
                {
                    if (ManageAttachment.DocumentHasLockedAttachments(id))
                    {
                        return (false, "FileInUse");
                    }
                }
                Document item = new Document().FindIncludeReceiversAndCarbonCopyAndCategory(id);
                if (item != null)
                {
                    dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(item.Category.BasicAttribute);
                    if (!ReferenceEquals(null, categoryBasicAttribute))
                    {
                        foreach (var attr in categoryBasicAttribute.Root)
                        {
                            if (attr.Name.Value == "ReceivingEntity")
                            {
                                if (ReferenceEquals(null, attr.BroadcastReceivingEntity) || (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value != true))
                                {
                                    return (false, string.Empty);
                                }
                            }
                        }

                    }
                    short? purposeId = ManagePurpose.GetCCedPurpose();
                    if (!purposeId.HasValue)
                    {
                        return (false, "CheckAtleastOnePurposeCCed");
                    }
                    bool isBroadcast = true;
                    if (await SendToReceiverEntity(userId, structureId, structureIds, isStructureSender, transferId, item, purposeId.Value, dueDate, instruction, isBroadcast: isBroadcast))
                    {
                        if (transferId.HasValue)
                        {
                            Close(transferId.Value);
                        }
                        retValue = true;
                    }
                }
            }
            return (retValue, string.Empty);
        }

        /// <summary>
        /// Broadcast Complete
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="documentId"></param>
        /// <param name="ids"></param>
        /// <param name="dueDate"></param>
        /// <param name="instruction"></param>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public static List<CompletedDocumentTransferModel> BroadcastComplete(long userId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, long? documentId, List<long> ids, DateTime? dueDate, string instruction, long structureId, long? delegationId = null)
        {
            List<CompletedDocumentTransferModel> retValue = new List<CompletedDocumentTransferModel>();
            short? purposeId = ManagePurpose.GetCCedPurpose();
            if (!purposeId.HasValue)
            {
                CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();
                model.Message = "CheckAtleastOnePurposeCCed";
                retValue.Add(model);
                return retValue;
            }
            var currentUserId = userId;
            var transfers = new Transfer().ListByIds(ids);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfers) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureSender = delegation.IsStructureSender;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (ids != null && ids.Any())
            {
                foreach (long id in ids)
                {
                    if (ManageUserAccess.HaveTransferAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
                    {
                        var transfer = new Transfer().FindIncludeDocumentAndReceiversAndCategory(id);
                        if (transfer != null)
                        {
                            CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();
                            dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(transfer.Document.Category.BasicAttribute);
                            if (!ReferenceEquals(null, categoryBasicAttribute))
                            {
                                bool isBroadcast = true;
                                foreach (var attr in categoryBasicAttribute.Root)
                                {
                                    if (attr.Name.Value == "ReceivingEntity")
                                    {
                                        if (ReferenceEquals(null, attr.BroadcastReceivingEntity) || (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value != true))
                                        {
                                            model.UncompletedDocumentReferenceNumber = transfer.Document.ReferenceNumber;
                                            retValue.Add(model);
                                            isBroadcast = false;
                                        }
                                        break;
                                    }
                                }
                                if (!isBroadcast)
                                {
                                    continue;
                                }
                            }
                            if (Configuration.AttachmentEditable)
                            {
                                if (new Document().CheckHasLockedAttachmentsByUser(transfer.DocumentId.Value, userId))
                                {
                                    model.UncompletedDocumentReferenceNumber = transfer.Document.ReferenceNumber;
                                    model.Message = "HasLockedAttachmentsByUser";
                                    retValue.Add(model);
                                    continue;
                                }
                            }
                            else if (new Document().CheckOriginalDocumentLockedByUser(transfer.DocumentId.Value, userId))
                            {
                                model.UncompletedDocumentReferenceNumber = transfer.Document.ReferenceNumber;
                                model.Message = "OriginalDocumentLockedByUser";
                                retValue.Add(model);
                                continue;
                            }

                            model.DocumentAttachmentIdHasValue = transfer.Document.AttachmentId.HasValue;
                            if (transfer.OwnerUserId.HasValue && transfer.OwnerUserId.Value != userId)
                            {
                                model.UncompletedDocumentReferenceNumber = transfer.Document.ReferenceNumber;
                                model.Message = "TransferHasDifferentOwner";
                                retValue.Add(model);
                                continue;
                            }
                            transfer.ClosedDate = DateTime.Now;
                            transfer.StatusId = (short)DocumentStatus.Completed;
                            bool isLastOpenedTransfer = new Transfer().CheckIfLastOpenTransfer(transfer.Id, transfer.DocumentId, true);
                            if (isLastOpenedTransfer && transfer.Document.AttachmentId.HasValue)
                            {
                                if (CompleteAndSendToReceiverEntity(userId, structureId, structureIds, isStructureSender, id, purposeId.Value, transfer.Document, dueDate, instruction))
                                {
                                    transfer.Document.StatusId = (short)DocumentStatus.Completed;
                                    transfer.Document.ClosedDate = DateTime.Now;
                                    transfer.Document.UpdateStatusAndClosedDate();
                                    transfer.UpdateStatusAndClosedDate();
                                    model.Updated = true;
                                    model.TransferId = id;
                                    model.DocumentId = transfer.Document.Id;
                                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Complete, currentUserId);
                                }
                            }
                            else if (!isLastOpenedTransfer)
                            {
                                if (CompleteAndSendToReceiverEntity(userId, structureId, structureIds, isStructureSender, id, purposeId.Value, transfer.Document, dueDate, instruction))
                                {
                                    transfer.UpdateStatusAndClosedDate();
                                    model.Updated = true;
                                    model.TransferId = id;
                                    model.UncompletedDocumentReferenceNumber = transfer.Document.ReferenceNumber;
                                    model.DocumentId = transfer.Document.Id;
                                    model.Message = "TransferWasCompletedNotLastOpenTransfer";
                                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.Id, (int)ActivityLogs.Complete, currentUserId);
                                }
                            }
                            else
                            {
                                model.UncompletedDocumentReferenceNumber = transfer.Document.ReferenceNumber;
                                model.Message = "CorrespondenceNotCompleteNoOriginalMail";
                            }
                            retValue.Add(model);
                        }
                    }
                }
            }
            else if (documentId.HasValue)
            {
                Document item = new Document().FindIncludeReceiversAndCarbonCopy(documentId.Value);
                if (item.AttachmentId.HasValue)
                {
                    if (CompleteAndSendToReceiverEntity(userId, structureId, structureIds, isStructureSender, null, purposeId.Value, item, dueDate, instruction))
                    {
                        CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();
                        model.Updated = true;
                        model.DocumentId = item.Id;
                        retValue.Add(model);
                    }
                }
                else
                {
                    CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();
                    model.Message = "CorrespondenceNotCompleteNoOriginalMail";
                    retValue.Add(model);
                }
            }
            return retValue;
        }

        /// <summary>
        /// Get transfer and document metadata
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<TransferInboxListViewVipModel> GetTransferDetailsById(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel,
            long id, long? delegationId = null, Language language = Language.EN)
        {
            var currentUserId = userId;
            var item = await new Transfer().GetTransferInfoByIdAsync(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, item.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            var retValue = new TransferInboxListViewVipModel();
            if (await ManageUserAccess.HaveTransferAccessAsync(id, userId, structureIds, isStructureReceiver, privacyLevel))
            {

                var fromStructure = string.Empty;
                if (item.FromStructureId.HasValue)
                {
                    fromStructure = item.FromStructure.Name;
                    if (language == Language.AR && !string.IsNullOrEmpty(item.FromStructure.NameAr))
                    {
                        fromStructure = item.FromStructure.NameAr;
                    }
                    else if (language == Language.FR && item.FromStructure.NameFr != null)
                    {
                        fromStructure = item.FromStructure.NameFr;
                    }
                }
                var isOverdue = false;
                if (item.DueDate.HasValue)
                {
                    if (item.ClosedDate.HasValue)
                    {
                        isOverdue = item.ClosedDate.Value > item.DueDate.Value;
                    }
                    else
                    {
                        isOverdue = DateTime.Now.Date > item.DueDate.Value;
                    }
                }
                List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                foreach (var receiver in item.Document.DocumentReceiverEntity)
                {
                    long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                    bool isEntityGroup = receiver.EntityGroupId.HasValue;
                    string text = string.Empty;
                    if (isEntityGroup)
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                        : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                        : receiver.EntityGroup.Name;
                    }
                    else
                    {
                        text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                       : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                       : receiver.Structure.Name;
                    }
                    receivers.Add(new ReceivingEntityModel
                    {
                        Id = targetId,
                        IsEntityGroup = isEntityGroup,
                        Text = text
                    });
                }
                var latestAttachmentVersion = (item.Document.AttachmentId != null && item.Document.AttachmentNavigation != null) ? await ManageAttachment.GetCurrentVersionNumber(item.Document.AttachmentNavigation.StorageAttachmentId) : "";
                retValue = new TransferInboxListViewVipModel
                {
                    Id = item.Id,
                    DocumentId = item.DocumentId.Value,
                    Subject = item.Document.Subject,
                    CategoryId = item.Document.CategoryId,
                    ReferenceNumber = item.Document.ReferenceNumber,
                    TransferDate = item.CreatedDate.ToString(Constants.DATETIME_FORMAT24),
                    DueDate = item.DueDate != null ? item.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                    OwnerUserId = item.OwnerUserId,
                    OwnerDelegatedUserId = item.OwnerDelegatedUserId,
                    IsOverDue = isOverdue,
                    IsRead = item.OpenedDate != null ? true : false,
                    IsLocked = !item.ToUserId.HasValue && item.OwnerUserId != null ? true : false,
                    SentToUser = item.ToUserId != null ? true : false,
                    SentToStructure = !item.ToUserId.HasValue && item.ToStructureId != null ? true : false,
                    FromUserId = item.FromUserId,
                    FromUser = item.FromUserId.HasValue ? (language == Language.EN ? $"{item.FromUser.Firstname} {item.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.FromUser.Id, language)}") : string.Empty,
                    FromStructure = fromStructure,
                    LockedBy = item.OwnerUser != null ? (language == Language.EN ? $"{item.OwnerUser.Firstname} {item.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.OwnerUser.Id, language)}") : string.Empty,
                    LockedByDelegatedUser = item.OwnerDelegatedUser != null ? (language == Language.EN ? $"{item.OwnerDelegatedUser.Firstname} {item.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(item.OwnerDelegatedUser.Id, language)}") : string.Empty,
                    LockedDate = item.LockedDate != null ? item.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                    OpenedDate = item.OpenedDate != null ? item.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                    CreatedByUserId = item.Document.CreatedByUserId,
                    Cced = item.Cced,
                    ImportanceId = item.Document.ImportanceId,
                    ToStructureId = item.ToStructureId,
                    ReceivingEntities = receivers,
                    IsSigned = item.IsSigned,
                    attachmentId = item.Document.AttachmentId,
                    AttachmentVersion = latestAttachmentVersion
                };
                ManageActivityLog.AddFullActivityLog(item.DocumentId, item.Id, (int)ActivityLogs.ViewTransfer, currentUserId, "", "");
            }
            return retValue;
        }

        /// <summary>
        /// Get transfer owner id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<long?> GetTransferOwnerId(long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long id, long? delegationId = null)
        {
            if (await ManageUserAccess.HaveTransferAccessAsync(id, userId, structureIds, isStructureReceiver, privacyLevel, delegationId))
            {
                return await new Transfer().GetOwnerId(id);
            }
            return null;
        }

        /// <summary>
        /// Cancel transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static bool Cancel(long userId, long id, int roleId, long? delegationId, Language lang = Language.EN)
        {
            bool retValue = false;
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeToUserToStructure(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
            }

            var allTransfers = new Transfer().ListByDocumentIdIncludeDocument(transfer.DocumentId.Value);
            var inprogressStatusId = Convert.ToInt32(DocumentStatus.InProgress);
            if (transfer != null && (transfer.FromUserId == userId || roleId == Convert.ToInt32(Role.Administrator)) && transfer.ClosedDate == null && transfer.StatusId == Convert.ToInt32(DocumentStatus.InProgress))
            {
                var transfersWithTheSameParent = allTransfers.Where(t => t.ParentTransferId == transfer.ParentTransferId).ToList();
                if (transfersWithTheSameParent.Count == 1)
                {
                    if (transfer.ParentTransferId != default)
                    {
                        var parentTransfer = new Transfer().Find((long)transfer.ParentTransferId);
                        parentTransfer.ClosedDate = null;
                        parentTransfer.StatusId = (short)DocumentStatus.InProgress;
                        parentTransfer.UpdateStatusAndClosedDate();
                        //Logs are deleted on transfer.Delete() due to cascade relation, ParentTransferId was used instead
                        ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: transfer.ToUserId != default(long?) ? $"{IdentityHelperExtension.GetFullName(transfer.ToUserId.Value, lang)}" : transfer.ToStructure.Name);
                    }
                    else
                    {
                        ManageDocument.UpdateDocumentStatusById(transfer.DocumentId.Value, DocumentStatus.Draft);
                    }
                }

                transfer.Delete();
                retValue = true;
            }
            return retValue;
        }

        /// <summary>
        /// Function to add user signature using viewer
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="userId"></param>
        /// <param name="StructureIds"></param>
        /// <param name="isStructureReciever"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="templateId"></param>
        /// <param name="lang"></param>
        /// <returns></returns>
        public static async Task<(bool updated, string message,long? signedTransferId)> SignDocument(long documentId, long? transferId, long userId, List<long> StructureIds, bool isStructureReciever, bool isStructureSender, short privacyLevel, long signatureTemplateId, long? delegationId = null, Language lang = Language.EN, string usertoken = null)
        {
            long? signedTransferId = null;
            try
            {
                if (string.IsNullOrEmpty(usertoken))
                {
                    Log.Information("SignDocument: usertoken is null or empty, using Core.Configuration.IdentityAccessToken");
                    usertoken = Core.Configuration.IdentityAccessToken;
                }
                Document document = new Document().FindIncludeTransfers(documentId);


                Transfer transfer = null;
                if (transferId != null)
                    transfer = new Transfer().Find(transferId.Value);
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer == null ? null : transfer.CreatedDate) : null;
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    StructureIds = delegation.StructureIds;
                    isStructureReciever = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                }
                if (!ManageUserAccess.HaveAccess(documentId, userId, StructureIds, isStructureReciever, privacyLevel, delegationId))
                    return (false, "HasNoAccess", signedTransferId);

                if (Configuration.AttachmentEditable)
                {
                    if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                    {
                        return (false, "FileInUse", signedTransferId);
                    }
                }
                else
                {
                    if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                    {
                        return (false, "OriginalFileInUse", signedTransferId);
                    }
                }
                var attachments = document.Attachment;

                if (attachments == null || attachments.Count == 0)
                {
                    return (false, "NoAttachmentToSign", signedTransferId);
                }
                var templateImage = await GetSignature(signatureTemplateId, usertoken);
                bool signaturesFlag = false;

                foreach (var item in attachments)
                {
                    List<ViewerSignModel> signModels = new List<ViewerSignModel>();

                    if (/*document.ByTemplate != null && */document.TemplateHasSignature && document.AttachmentNavigation.Extension == "docx" && item.Id == document.AttachmentId)
                    {
                        StorageAttachmentModel storageAttachmentModelOriginal = new StorageAttachmentModel();
                        storageAttachmentModelOriginal = await ManageAttachment.GetStorageAttachmentModel(document.AttachmentNavigation.StorageAttachmentId);
                        var output = GetSignPlaceholderDiminsion(storageAttachmentModelOriginal.Data, "signature");
                        output.language = lang.ToString();
                        signModels.Add(output);

                        ManageDocument.GenerateReferenceNumberFirstTime(userId, documentId, transferId, lang);
                        var file = ManageBookmark.ReplaceBookmark(storageAttachmentModelOriginal.Data, documentId, false);
                        var fileAfterRemoveSignShape = RemoveSignShape(file);

                        FileViewModel fileAttachment = new FileViewModel();
                        fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModelOriginal.Name);
                        fileAttachment.FileSize = Convert.ToInt64(storageAttachmentModelOriginal.FileSize);
                        fileAttachment.ContentType = "application/pdf";
                        fileAttachment.Extension = "pdf";
                        fileAttachment.Data = fileAfterRemoveSignShape;
                        await ManageAttachment.Replace(document.AttachmentId.Value, fileAttachment, documentId, transferId, userId);
                    }

                    var signatureAreas = await new SignatureRegion().FindByUserAndAttachmentasync(userId, item.Id);
                    //if(signatureAreas == null || signatureAreas.Count == 0)
                    //{
                    //    return (false, "NoSignatureConfigured");
                    //}

                    foreach (var area in signatureAreas)
                    {
                        ViewerSignModel signLocation = new ViewerSignModel();
                        signLocation.height = area.Height.Value;
                        signLocation.width = area.Width.Value;
                        signLocation.posX = area.PositionX.Value;
                        signLocation.posY = area.PositionY.Value;
                        signLocation.pageNumber = area.PageNumber.Value;
                        signLocation.language = lang.ToString();
                        signModels.Add(signLocation);
                    }

                    if (signModels == null || signModels.Count == 0)
                        continue;

                    StorageAttachmentModel storageAttachmentModel = new StorageAttachmentModel();
                    storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(item.StorageAttachmentId);
                    var currentVersion = await ManageAttachment.GetCurrentVersionNumber(item.StorageAttachmentId);


                    bool isDraft = true;
                    if (transferId != null)
                        isDraft = false;

                    var documentOpened = await OpenFileInViewer(item.Id, documentId, transferId, currentVersion, delegationId, isDraft, usertoken);
                    if (!documentOpened)
                        return (false, "HasNoAccess", signedTransferId);

                    var ischeckedOut = await CheckOutFile(item.Id, documentId, transferId, currentVersion, delegationId, isDraft, usertoken);
                    if (!ischeckedOut)
                    {
                        return (false, "CheckoutFaild", signedTransferId);   
                    }

                    var isSignedAdded = await SaveMultiSignatures(templateImage, signModels, signatureTemplateId, currentVersion, item.Id, StructureIds, usertoken);
                    if (!isSignedAdded)
                    {
                        await DiscardCheckOutFile(item.Id, currentVersion, usertoken);
                        return (false, "SignFailed", signedTransferId);
                    }

                    var isCheckedIn = await CheckInFile(item.Id, documentId, transferId, currentVersion, delegationId, isDraft, usertoken);
                    if (!isCheckedIn)
                    {
                        await DiscardCheckOutFile(item.Id, currentVersion, usertoken);
                        return (false, "CheckinFailed", signedTransferId);
                    }

                    ManageSignatureRegion.MakeRegionsIsSigned(signatureAreas);
                    signaturesFlag = true;
                    if (transferId != null)
                        transfer.UpdateTransferIsSigned(true, userId);
                    else if (transferId == null)
                    {
                        var transferRes = await Transfer(userId, StructureIds.FirstOrDefault(), StructureIds, isStructureSender, isStructureReciever, privacyLevel,
                            new List<TransferModel>()
                            { new TransferModel{
                                                DocumentId = documentId,
                                                PurposeId = (short)Configuration.ForInfoPurpose /*new Intalio.CTS.Core.DAL.Purpose().List().Where(x=>x.Name=="For Info").FirstOrDefault().Id*/,
                                                FromStructureId = StructureIds.FirstOrDefault(),
                                                ToStructureId = StructureIds.FirstOrDefault(),
                                            }},language: lang);
                        document = new Document().FindIncludeTransfers(documentId);
                        transfer = document.Transfer.LastOrDefault();
                        transfer.UpdateTransferIsSigned(true, userId);
                        signedTransferId = transfer.Id;
                    }


                    //ManageAttachment.ConvertToPdf(document.AttachmentId.Value, userId, StructureIds, isStructureReciever, privacyLevel, delegationId);
                    //await ManageDocumentLock.LockDocumentAsync(documentId, userId);
                }
                if (!signaturesFlag)
                {
                    return (false, "NoSignatureConfigured", signedTransferId);
                }
            }
            catch (Exception ex)
            {
                return (false, ex.Message, signedTransferId);
            }

            return (true, "", signedTransferId);

        }
        /// <summary>
        /// Add rejected image to document
        /// </summary>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="userId"></param>
        /// <param name="StructureIds"></param>
        /// <param name="isStructureReciever"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="lang"></param>
        /// <returns></returns>
        public static async Task<bool> RejectDocument(long documentId, long transferId, long userId, List<long> StructureIds, bool isStructureReciever, short privacyLevel, Language lang = Language.EN)
        {
            try
            {
                Document document = new Document().FindIncludeTransfers(documentId);
                if (document.AttachmentId != 0)
                {
                    FileViewModel fileViewModel = new FileViewModel();
                    StorageAttachmentModel storageAttachmentModel = new StorageAttachmentModel();
                    var attachment = new Attachment().Find(document.AttachmentId.Value);
                    storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);

                    if (storageAttachmentModel != null)
                    {
                        byte[] newFile = ManageBookmark.ReplaceBookmark(storageAttachmentModel.Data, documentId, true);
                        (double signBookmarkX, double signBookmarkY, int signPageNumber, float pageWidth, float pageHeight) signBookmark = GetSignBookmarkCoordinates(newFile);
                        var currentVersion = await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId);
                        byte[] rejectedFile = await AddRejectedImage(newFile);

                        FileViewModel fileAttachment = new FileViewModel();
                        fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModel.Name);
                        fileAttachment.FileSize = Convert.ToInt64(storageAttachmentModel.FileSize);
                        fileAttachment.ContentType = "application/pdf";
                        fileAttachment.Extension = "pdf";
                        fileAttachment.Data = rejectedFile;
                        await ManageAttachment.Replace(document.AttachmentId.Value, fileAttachment, documentId, transferId, userId);

                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }

            catch (Exception ex) { return false; }

            return true;
        }

        public static APIResponseViewModel ReAssign(long transferId, long newUserId, long userId, List<long> StructureIds, bool isStructureReciever, short privacyLevel, Language lang = Language.EN)
        {

            APIResponseViewModel apiResponseViewModel = new APIResponseViewModel();

            try
            {
                var transfer = new Transfer().Find(transferId);
                //var ifHaveManageCorrespondenceAccess = Configuration.EnablePerStructure ? ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(roleId, userId, structureId).Any(x => x.Name == "ManageCorrespondence") : ManageMenu.ListMenuesByRoleIdUserId(roleId, userId).Any(x => x.Name == "ManageCorrespondence");



                if (transfer != null && transfer.ToUserId.HasValue)
                {
                    if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsWithOriginal(transfer.DocumentId.Value))

                    {
                        apiResponseViewModel.Success = false;
                        apiResponseViewModel.Message = "FileInUse";
                        return apiResponseViewModel;
                    }
                    else
                    {
                        transfer.ToUserId = newUserId;
                        transfer.Update();
                    }

                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Transfer, userId, note: transfer.ToUserId != default(long?) ? $"{IdentityHelperExtension.GetFullName(transfer.ToUserId.Value, lang)}" : transfer.ToStructure.Name);
                }
                else
                {
                    apiResponseViewModel.Success = false;
                    apiResponseViewModel.Message = "";
                    return apiResponseViewModel;
                }

            }

            catch (Exception ex)
            {
                apiResponseViewModel.Success = false;
                apiResponseViewModel.Message = "";
                return apiResponseViewModel;
            }

            apiResponseViewModel.Success = true;
            apiResponseViewModel.Message = "";
            return apiResponseViewModel;
        }

        public static APIResponseViewModel ReAssignWithoutTransfer(long transferId, long newUserId, long userId, List<long> StructureIds, bool isStructureReciever, short privacyLevel, string note, Language lang = Language.EN )
        {

            APIResponseViewModel apiResponseViewModel = new APIResponseViewModel();

            try
            {
                var transfer = new Transfer().Find(transferId);
               
                if (transfer != null && transfer.ToUserId.HasValue)
                {
                    if (transfer.DocumentId.HasValue && new Document().CheckHasLockedAttachmentsWithOriginal(transfer.DocumentId.Value))
                    {
                        apiResponseViewModel.Success = false;
                        apiResponseViewModel.Message = "FileInUse";
                        return apiResponseViewModel;
                    }
                    else
                    {
                        transfer.ToUserId = newUserId;
                        transfer.OwnerDelegatedUserId = null;
                        transfer.OwnerUserId = null;
                        transfer.LockedDate = null;
                        transfer.OpenedDate = null;
                        transfer.CreatedDate = DateTime.Now;
                        transfer.Update();
                    }

                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.ReAssign, userId, note: note);
                }
                else
                {
                    apiResponseViewModel.Success = false;
                    apiResponseViewModel.Message = "";
                    return apiResponseViewModel;
                }

            }

            catch (Exception ex)
            {
                apiResponseViewModel.Success = false;
                apiResponseViewModel.Message = "";
                return apiResponseViewModel;
            }

            apiResponseViewModel.Success = true;
            apiResponseViewModel.Message = "";
            return apiResponseViewModel;
        }

        /// <summary>
        /// Preview Transfers For Moving. 
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public static async Task<(int, List<MoveTransferListViewModel>)> PreviewTransfersForMoving(MoveTransferModel model, int startIndex, int pageSize, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {

                var countResult = item.GetPreviewTransfersCount(model.FromUser, model.ToUser, model.Structure);
                var itemList = await item.GetPreviewTransfersForMoving(startIndex, pageSize, model.FromUser, model.ToUser, model.Structure, sortExpression?.OrderByExpression<Transfer>());
                return (countResult.Result, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && t.ToStructure.NameFr != null)
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }

                    List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.Document.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr)? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr)? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                        receivers.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            IsEntityGroup = isEntityGroup,
                            Text = text
                        });
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    var isEnLang = language == Language.EN;
                    return new MoveTransferListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId.Value,
                        Subject = t.Document.Subject,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                        CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Status = t.Document.StatusId,
                        StatusId = t.Document.StatusId,
                        OwnerUserId = t.OwnerUserId,
                        OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                        IsOverDue = isOverdue,
                        IsRead = t.OpenedDate != null ? true : false,
                        IsLocked = t.OwnerUserId != null ? true : false,
                        SentToUser = t.ToUserId != null ? true : false,
                        SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ? (isEnLang ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUserId.Value, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ? (isEnLang ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUserId.Value, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        LockedBy = t.OwnerUser != null ? (isEnLang ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                        LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (isEnLang ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                        LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        CreatedByUser = (isEnLang ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUserId, language)}"),
                        CreatedByUserId = t.Document.CreatedByUserId,
                        Instruction = t.Instruction,
                        Cced = t.Cced,
                        PurposeId = t.PurposeId,
                        PriorityId = t.Document.PriorityId,
                        PrivacyId = t.Document.PrivacyId,
                        ImportanceId = t.Document.ImportanceId,
                        SendingEntity = sendingEntity,
                        CreatedByStructureId = t.Document.CreatedByStructureId,
                        ReceivingEntityId = receivers,
                        ReceivingEntity = receivingEntity,
                        HasAttachment = t.Document.Attachment.Any(),
                        AttachmentCount = t.Document.Attachment.Count()
                    };
                }).ToList());
            }
        }

        /// <summary>
        /// Move Transfers. 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static async Task<MoveTransferViewModel> MoveTransfers(MoveTransferModel model, Language language = Language.EN)
        {
            MoveTransferViewModel result = new MoveTransferViewModel();
            try
            {
                using (Transfer TransferEntity = new Transfer())
                {

                    if (model.FromUser != default && model.ToUser != default && model.Structure != default)
                    {

                        var transfersFromUserIds = await TransferEntity.GetTransfersIdsForMovingFromUser(model.FromUser, model.Structure);
                        var transfersToUserIds = await TransferEntity.GetTransfersIdsForMovingToUser(model.FromUser, model.Structure);

                        var allTransfersIds = transfersToUserIds.Union(transfersFromUserIds).ToList();


                        if (transfersToUserIds.Count() > 0 || transfersFromUserIds.Count() > 0)
                        {
                            var attachmentLockedByUser = await TransferEntity.GetAttachmentsToDiscardCheckOutForMoving(allTransfersIds, model.FromUser);


                            foreach (var attachment in attachmentLockedByUser)
                            {
                                try
                                {
                                    var currentVersion = await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId);
                                    var res = await attachment.DiscardCheckOutAttachment(attachment.Id, currentVersion);
                                    if (!res)
                                    {
                                        throw new Exception();
                                    }
                                }
                                catch (Exception e)
                                {

                                    result.Status = false;
                                    result.Message = "ErrorInDiscardCheckOut";
                                    return result;
                                }

                            }

                            if (transfersFromUserIds.Count() > 0)
                                await TransferEntity.UpdateFromUserIdForMoving(transfersFromUserIds, model.FromUser, model.ToUser);



                            if (transfersToUserIds.Count() > 0)
                                await TransferEntity.UpdateToUserIdForMoving(transfersToUserIds, model.FromUser, model.ToUser);




                            await TransferEntity.UpdateNotesForMoving(allTransfersIds, model.FromUser, model.ToUser);
                            await TransferEntity.UpdateAttachmentsForMoving(allTransfersIds, model.FromUser, model.ToUser);
                            await TransferEntity.UpdateNonArchivedAttachmentsForMoving(allTransfersIds, model.FromUser, model.ToUser);
                            await TransferEntity.UpdateLinkedDocumentForMoving(allTransfersIds, model.FromUser, model.ToUser);

                        }

                        result.Status = true;
                        result.Message = "Success";
                    }
                    else
                    {
                        result.Status = false;
                        result.Message = "FillSearchCritirea";
                    }
                    return result;
                }


            }
            catch (Exception e)
            {
                result.Status = false;
                result.Message = e.Message;
                return result;
            }
        }

        public static async Task<MoveTransferViewModel> CheckTransfersForMoving(MoveTransferModel model, int viwerRoleId, Language language = Language.EN)
        {
            MoveTransferViewModel result = new MoveTransferViewModel();
            try
            {
                using (Transfer TransferEntity = new Transfer())
                {
                    if (model.FromUser != default && model.ToUser != default && model.Structure != default)
                    {
                        var usersNotExistsIds = ManageUser.FindNotExistent(new List<long> { model.ToUser, model.FromUser });
                        if (usersNotExistsIds.Count > 0)
                        {
                            result.Status = false;
                            result.Message = TranslationUtility.Translate("UserNotSynchronized", language);
                            return result;
                        }

                        var transfersFromUserIds = await TransferEntity.GetTransfersIdsForMovingFromUser(model.FromUser, model.Structure);
                        var transfersToUserIds = await TransferEntity.GetTransfersIdsForMovingToUser(model.FromUser, model.Structure);

                        var allTransfersIds = transfersToUserIds.Union(transfersFromUserIds).ToList();


                        if (transfersToUserIds.Count() > 0 || transfersFromUserIds.Count() > 0)
                        {
                            var attachmentLockedByUser = await TransferEntity.GetAttachmentsToDiscardCheckOutForMoving(allTransfersIds, model.FromUser);

                            if (attachmentLockedByUser.Count() > 0)
                            {

                                if (viwerRoleId == (int)Role.Administrator)
                                {
                                    result.Status = true;
                                    result.Message = "FilesNeededToDiscardCheckOutByViewerAdmin";
                                }
                                else
                                {
                                    result.Status = false;
                                    result.Message = TranslationUtility.Translate("FilesNeededToDiscardCheckOut", language);
                                }

                                return result;

                            }
                            else
                            {
                                result.Status = true;
                                result.Message = "NoAttachmentsFound";
                            }

                        }
                        else
                        {
                            result.Status = false;
                            result.Message = TranslationUtility.Translate("MigrateTransfersEmpty", language);

                        }


                    }
                    else
                    {
                        result.Status = false;
                        result.Message = TranslationUtility.Translate("FillSearchCritirea", language);

                    }
                }
                return result;
            }
            catch (Exception e)
            {
                result.Status = false;
                result.Message = e.Message;
                return result;
            }

        }
        public static bool UnArchive(long transferId, long userId, List<long> StructureIds, bool isStructureReciever, short privacyLevel, Language lang = Language.EN)
        {
            try
            {

                var transfer = new Transfer().FindIncludeDocumnet(transferId);
                if (transfer.Document.StatusId == (short)DocumentStatus.Completed)
                {

                    transfer.UpdateStatusIdAndClosedDateInCorrespondence(transfer.DocumentId.Value);
                    ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Unarchive, userId);
                }
                else
                    return false;
            }

            catch (Exception ex) { return false; }

            return true;
        }

        public static async Task<bool> SendCorrespondenceAsEmail(TransferEmailViewModel model)
        {
            //string templateName = NotificationTemplateName.OnMailDocument.ToString();
            var retValue = false;

            var attachments = new Attachment().ListAttachmentsById(model.AttachmentIds, model.DocumentId);
            var storageAttachments = new List<AttachmentViewModel>();

            if (attachments.Any())
            {
                foreach (var attachment in attachments)
                {
                    var storageAttachment = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);
                    if (storageAttachment != null)
                    {
                        var attachmentViewModel = new AttachmentViewModel();
                        attachmentViewModel.Name = storageAttachment.Name;
                        attachmentViewModel.Extension = storageAttachment.Extension;
                        attachmentViewModel.ContentType = storageAttachment.ContentType;
                        attachmentViewModel.Data = storageAttachment.Data;
                        storageAttachments.Add(attachmentViewModel);
                    }
                }
            }

            //NotificationTemplateListViewModel notificationTemplate = Configuration.EnableCaching ? new NotificationTemplate().ListWithCache()
            //    .Select(t => (NotificationTemplateListViewModel)t).Where(t => t.Name.ToLower() == templateName.ToLower()).First() : new NotificationTemplate().FindByName(templateName);

            var receivers = model.To.Split(";").ToList();

            //if (notificationTemplate != null)
            //{
            //    model.Body = notificationTemplate.Body;
            //}
            retValue = new Intalio.Core.Email().SendEmail(receivers, model.Subject, model.Body, storageAttachments);
            return retValue;
        }

        /// <summary>
        /// Archive document
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="id"></param>
        /// <param name="roleId"></param>
        /// <param name="isStructureReciver"></param>
        /// <param name="structureId"></param>
        /// <param name="StructureIds"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="rootFolderId"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static async Task<(bool Success, string Message)> Archive(long userId, long id, int roleId, long? structureId, List<long> StructureIds, bool isStructureReciver, short privacyLevel, long? rootFolderId, long? delegationId)
        {
            var transfer = new Transfer().FindIncludeDocument(id);
            if (transfer?.DocumentId == null)
            {
                return (false, "Invalid transfer or DocumentId");
            }

            var attachments = await ManageAttachment.List(transfer.DocumentId.Value, null, userId, roleId, structureId, StructureIds, isStructureReciver, privacyLevel);

            if (attachments == null || !attachments.Any())
            {
                return (false, "No Attachments to archive");
            }
            var RefrenceNumerFolderId = AddFolder(transfer.Document.ReferenceNumber ?? transfer.Document.Id.ToString(), rootFolderId ?? 0);
            bool success = RefrenceNumerFolderId != 0 ? await AddAllAttachmentsAsync(attachments, RefrenceNumerFolderId, id, delegationId) : false;
            return success ? (true, "Archived Successfully") : (false, "ArchivingFailed_" + RefrenceNumerFolderId);
        }

        private static long AddFolder(string name, long parentId)
        {
            try
            {
                string url = Configuration.DMSURL + (parentId == 0 ? $"/Cabinets/Save?Name={name}" : $"/Folders/Save?Name={name}&ParentId={parentId}");
                var response = Intalio.Core.Helper.HttpPost(url, Configuration.IdentityAccessToken);
                var responseMsg = JsonConvert.DeserializeObject<Dictionary<string, string>>(response.message);
                if (!response.Success)
                {
                    if (responseMsg["state"] == "AlreadyExists")
                    {
                        string getUrl = Configuration.DMSURL + "/Folders/GetByName?parentFolderId=" + parentId + "&name=" + name;
                        var getResponse = Intalio.Core.Helper.HttpGet<dynamic>(getUrl, Configuration.IdentityAccessToken);
                        return getResponse.id;
                    }
                    return 0;
                }
                //var responseMsg = JsonConvert.DeserializeObject<Dictionary<string, string>>(response.message);
                return Convert.ToInt64(responseMsg["id"]);

            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        private static async Task<bool> AddAllAttachmentsAsync(List<Model.TreeNode> list, long rootFolderId, long transferId, long? delegationId)
        {
            var tasks = new List<Task<bool>>();

            foreach (var x in list)
            {
                if (x.Type == ((int)NodeType.Folder).ToString())
                {
                    var parentFolderId = AddFolder(x.Text, rootFolderId);
                    var children = x.Children as IEnumerable<Model.TreeNode>;

                    if (children != null && children.Any())
                    {
                        tasks.Add(AddAllAttachmentsAsync(children.ToList(), parentFolderId, transferId, delegationId));
                    }
                }
                else if (x.Type == ((int)NodeType.File).ToString())
                {
                    tasks.Add(UploadDmsAttachmentAsync(Convert.ToInt64(x.Id.Split("_")[1]), rootFolderId, transferId, delegationId));
                }
            }

            var results = await Task.WhenAll(tasks);
            return results.All(r => r); // Ensures all tasks succeeded
        }

        private static async Task<bool> UploadDmsAttachmentAsync(long attachmentId, long rootFolderId, long transferId, long? delegationId)
        {
            try
            {
                var attachment = new DAL.Attachment().Find(attachmentId);
                if (attachment == null) return false;
                var attachmentCurrentVersion = await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId);
                var viewResponse = await OpenFileInViewer(attachmentId, attachment.DocumentId ?? 0, transferId, attachmentCurrentVersion, delegationId, false, Core.Configuration.IdentityAccessToken);
                if (!viewResponse) return false;
                var file = await ManageAttachment.DownloadFromViewertoUploadOnDMS(attachmentId);
                if (file == null) return false;
                string contentDisposition = file.GetType().GetProperty("ContentDisposition").GetValue(file).ToString();
                var contentType = file.GetType().GetProperty("ContentType").GetValue(file).ToString();
                byte[] data = (byte[])file.GetType().GetProperty("Data").GetValue(file);
                var filename = Uri.UnescapeDataString(new System.Net.Mime.ContentDisposition(contentDisposition).FileName);
                //var fileContent = await ManageAttachment.GetStorageAttachmentModel(attachment.StorageAttachmentId);

                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Configuration.IdentityAccessToken);

                    using (var request = new MultipartFormDataContent())
                    {
                        request.Add(new StringContent(rootFolderId.ToString()), "folderId");
                        request.Add(new StringContent("true"), "overwriteIfCheckOutByMe");
                        request.Add(new StringContent("true"), "overwrite");

                        var fileData = new ByteArrayContent(data);
                        fileData.Headers.ContentType = new MediaTypeHeaderValue(contentType);
                        request.Add(fileData, "file", $"{filename}");

                        var response = await client.PostAsync(Configuration.DMSURL + "/Files/Upload", request);
                        return response.IsSuccessStatusCode;
                    }
                }

            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public static async void DeleteDMSFolder(long folderId)
        {
            if (folderId != 0)
            {
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Configuration.IdentityAccessToken);
                    var response = await client.DeleteAsync(Configuration.DMSURL + "/Folders/Delete?id=" + folderId);
                }
            }
        }

        public static bool CheckIfLastOpenTransfer(List<long> ids)
        {
            var result = true;
            foreach (var id in ids)
            {
                var transfer = new Transfer().Find(id);
                result = result && new Transfer().CheckIfLastOpenTransfer(id, transfer.DocumentId);
            }
            return result;
        }
        public static async Task<(bool result, List<string> bookmarks)> CheckBookMarksAreExist(long documentId, long? transferId, List<string> BookMarks)
        {
            var result = (result: true, bookmarks: new List<string>());
            var document = new Document().Find(documentId);
            if (document.AttachmentId != null && (document.ByTemplate ?? false))
            {
                var attachment = new Attachment().Find(document.AttachmentId ?? 0);
                var currentversion = await ManageAttachment.GetCurrentVersionNumber(attachment.StorageAttachmentId);
                var attachmentdata = await ManageAttachment.GetFileData(attachment.StorageAttachmentId, currentversion);
                using (MemoryStream ms = new MemoryStream(attachmentdata))
                {
                    Aspose.Words.Document doc = new Aspose.Words.Document(ms);
                    foreach (var bookmark in BookMarks)
                    {
                        if (bookmark.ToLower() == "Signature".ToLower())
                        {
                            Aspose.Words.NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);
                            bool shapeExist = false;
                            foreach (Aspose.Words.Drawing.Shape shape in shapes)
                            {
                                if (shape.HasImage && shape.AlternativeText.ToLower() == "Signature".ToLower())
                                {
                                    shapeExist = true;
                                }
                            }
                            if (!shapeExist)
                            {
                                result.result = false;
                                result.bookmarks.Add(bookmark);
                            }
                        }
                        else if (bookmark.ToLower() == "Barcode".ToLower())
                        {
                            Aspose.Words.NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);
                            bool shapeExist = false;
                            foreach (Aspose.Words.Drawing.Shape shape in shapes)
                            {
                                if (shape.HasImage && shape.AlternativeText.ToLower() == "Barcode".ToLower())
                                {
                                    shapeExist = true;
                                }
                            }
                            if (!shapeExist)
                            {
                                result.result = false;
                                result.bookmarks.Add(bookmark);
                            }
                        }
                        else
                        {
                            if (doc.Range.Bookmarks[bookmark.ToLower()] == null)
                            {
                                result.result = false;
                                result.bookmarks.Add(bookmark);
                            }
                        }
                    }
                }
            }
            return result;
        }

        public static async Task<(bool result, string message, string details)> CheckUserCanSign(long documentId, long userId, List<long> StructureIds, bool isStructureReciever, bool isStructureSender, short privacyLevel, long? transferId, long? delegationId, List<string> BookMarks)
        {
            Transfer transfer = null;
            if (transferId != null)
                transfer = new Transfer().Find(transferId.Value);
            Document document = new Document().FindIncludeTransfers(documentId);

            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer == null ? null : transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                StructureIds = delegation.StructureIds;
                isStructureReciever = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }
            if (!ManageUserAccess.HaveAccess(documentId, userId, StructureIds, isStructureReciever, privacyLevel, delegationId))
                return (false, "HasNoAccess", "");

            if (Configuration.AttachmentEditable && new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                return (false, "FileInUse", "");

            if (!Configuration.AttachmentEditable && new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                    return (false, "OriginalFileInUse", "");

            if (document.Attachment == null || document.Attachment.Count == 0)
                return (false, "NoAttachmentToSign", "");

            var bookmarkRetValue = await CheckBookMarksAreExist(documentId, transferId, BookMarks);
            if (!bookmarkRetValue.result)
            {
                return (false, "BookmarksDosenotExist", string.Join(",", bookmarkRetValue.bookmarks));
            }
            return (true, "", "");
        }
        
        #endregion

        #region Private Methods
        /// <summary>
        /// Function to get signature position in file and return page width asnd height
        /// </summary>
        /// <param name="document"></param>
        /// <returns></returns>
        private static (double signBookmarkX, double signBookmarkY, int pageNumber, float pageWidth, float pageHeight) GetSignBookmarkCoordinates(byte[] document)
        {
            (double signBookmarkX, double signBookmarkY, int signPageNumber, float pageWidth, float pageHeight) result = (0, 0, 1, 0, 0);
            try
            {
                AsposeLicense asposeLicense = new AsposeLicense();
                Stream licenseStream = asposeLicense.Get();
                if (licenseStream != null)
                {
                    Aspose.Words.License license = new Aspose.Words.License();
                    license.SetLicense(licenseStream);
                }

                double x = 0, y = 0;
                int pageNumber = 1;

                MemoryStream fileStream = new MemoryStream(document);

                Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(fileStream);
                TextFragmentAbsorber textFragmentAbsorber = new TextFragmentAbsorber("Signature");
                pdfDocument.Pages.Accept(textFragmentAbsorber);

                Aspose.Pdf.Facades.PdfPageEditor editor = new Aspose.Pdf.Facades.PdfPageEditor();
                editor.BindPdf(pdfDocument);
                Aspose.Pdf.PageSize size = editor.GetPageSize(1);


                TextFragmentCollection textFragmentCollection = textFragmentAbsorber.TextFragments;
                foreach (TextFragment textFragment in textFragmentCollection)
                {
                    x = textFragment.Position.XIndent / size.Width;
                    y = (textFragment.Position.YIndent / size.Height) + 0.05;
                    pageNumber = textFragment.Page.Number;
                }

                result = (x, y, pageNumber, size.Width, size.Height);
            }
            catch (Exception ex)
            {
                //result = (0, 0);
            }

            return result;

        }

        /// <summary>
        /// Checkout file using viewer
        /// </summary>
        /// <param name="storageAttachmentId"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="version"></param>
        /// <param name="delgationId"></param>
        /// <param name="isDraft"></param>
        /// <param name="usertoken"></param>
        /// <returns></returns>
        private static async Task<bool> CheckOutFile(long storageAttachmentId, long documentId, long? transferId, string version, long? delgationId, bool isDraft, string usertoken)
        {
            //viewer API document/{documentId}/version/{version}/checkout
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{storageAttachmentId}/version/{version}/checkout?ctsDocumentId={documentId}&ctsTransferId={transferId}&delegationId={delgationId}&isDraft={isDraft}";
            var result = await Intalio.Core.Helper.HttpGetAsync<object>(viewerUrl, usertoken);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;
        }

        /// <summary>
        /// Checkout file using viewer
        /// </summary>
        /// <param name="storageAttachmentId"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        private static async Task<bool> DiscardCheckOutFile(long storageAttachmentId, string version, string usertoken)
        {

            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Delete, Configuration.ViewerUrl + "/api/document/" + storageAttachmentId.ToString() + "/version/T_" + version + "/checkout");
            request.Headers.Add("Authorization", "Bearer " + usertoken);
            var result = await client.SendAsync(request);

            return result.IsSuccessStatusCode;
        }

        /// <summary>
        /// get temmplate signatue from DS
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        private static async Task<byte[]> GetSignature(long templateId, string usertoken)
        {
            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, Configuration.DSURL + "/api/signature/template/" + templateId + "/image?token=" + usertoken);
            request.Headers.Add("Authorization", "Bearer " + usertoken);
            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsByteArrayAsync();
        }

        /// <summary>
        /// Add signature to document using viewer
        /// </summary>
        /// <param name="signature"></param>
        /// <param name="pageNumber"></param>
        /// <param name="language"></param>
        /// <param name="positionX"></param>
        /// <param name="positionY"></param>
        /// <param name="pageWidth"></param>
        /// <param name="pageHeight"></param>
        /// <param name="templateId"></param>
        /// <param name="version"></param>
        /// <param name="attachmentId"></param>
        /// <returns></returns>
        private static async Task<bool> SaveSignature(byte[] signature, int pageNumber, string language, double positionX, double positionY, float pageWidth, float pageHeight, long templateId, string version, long attachmentId)
        {
            var signatureBase64 = Convert.ToBase64String(signature);
            HttpClient httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Configuration.IdentityAccessToken);
            MultipartFormDataContent form = new MultipartFormDataContent();
            HttpResponseMessage response = new HttpResponseMessage();

            System.Drawing.Image image = System.Drawing.Image.FromStream(new System.IO.MemoryStream(signature));
            var height = image.Height.ToString();
            var width = image.Width.ToString();

            signatureBase64 = "data:image/png;base64," + signatureBase64;

            List<ViewerSignModel> signmodels = new List<ViewerSignModel>();
            ViewerSignModel signatureData = new ViewerSignModel();
            signatureData.imageSource = signatureBase64;
            signatureData.language = language;
            signatureData.pageNumber = pageNumber;
            signatureData.posX = positionX;
            signatureData.posY = 1 - positionY;
            signatureData.width = width.ToDouble() / pageWidth;
            signatureData.height = height.ToDouble() / pageHeight;
            signatureData.templateId = templateId;

            signmodels.Add(signatureData);
            var json = JsonConvert.SerializeObject(signmodels);
            var location_content = new StringContent(json, Encoding.UTF8, "application/json");

            //api/document/8/version/T_2.0/signature/save
            response = await httpClient.PostAsync(Configuration.ViewerUrl + "/api/document/" + attachmentId + "/version/T_" + version + "/signature/save", location_content);

            return true;
        }

        private static async Task<bool> SaveMultiSignatures(byte[] signature, List<ViewerSignModel> signmodels, long templateId, string version, long attachmentId, List<long> structureIds, string usertoken)
        {
            var retValue = false;
            var signatureBase64 = Convert.ToBase64String(signature);
            HttpClient httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", usertoken);
            MultipartFormDataContent form = new MultipartFormDataContent();
            HttpResponseMessage response = new HttpResponseMessage();
            signatureBase64 = "data:image/png;base64," + signatureBase64;

            foreach (ViewerSignModel signmodel in signmodels)
            {
                signmodel.templateId = templateId;
                signmodel.imageSource = signatureBase64;
            }
            var json = JsonConvert.SerializeObject(signmodels);
            //comment this when try local when viewer not latest
            json = "{" + "\"signatures\": " + json + ",\"pinCode\":\"-1\"}";
            var location_content = new StringContent(json, Encoding.UTF8, "application/json");

            if (Configuration.EnablePerStructure)
            {
                var structureId = structureIds[0];
                response = await httpClient.PostAsync(Configuration.ViewerUrl + "/api/document/" + attachmentId + "/version/T_" + version + "/signature/save?structId=" + structureId, location_content);
            }
            else
                response = await httpClient.PostAsync(Configuration.ViewerUrl + "/api/document/" + attachmentId + "/version/T_" + version + "/signature/save", location_content);
            if (response != null)
            {
                retValue = response.IsSuccessStatusCode;
            }
            return retValue;
        }
        /// <summary>
        /// Check in file using viewer
        /// </summary>
        /// <param name="attachmentId"></param>
        /// <param name="documentId"></param>
        /// <param name="transferId"></param>
        /// <param name="version"></param>
        /// <param name="delgationId"></param>
        /// <param name="isDraft"></param>
        /// <param name="usertoken"></param>
        /// <returns></returns>
        private static async Task<bool> CheckInFile(long attachmentId, long documentId, long? transferId, string version, long? delgationId, bool isDraft, string usertoken)
        {
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{attachmentId}/version/T_{version}/checkin?ctsDocumentId={documentId}&ctsTransferId={transferId}&delegationId={delgationId}&isDraft={isDraft}";
            var result = await Intalio.Core.Helper.HttpPostAsync<object>(viewerUrl, usertoken, null);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;
        }

        /// <summary>
        /// Draw rejected Image to file
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        private static async Task<byte[]> AddRejectedImage(byte[] file)
        {
            string documentPath = Directory.GetCurrentDirectory();
            string imagePath = Path.Combine(documentPath, "wwwroot/images/rejected.png");

            AsposeLicense asposeLicense = new AsposeLicense();
            Stream licenseStream = asposeLicense.Get();
            if (licenseStream != null)
            {
                Aspose.Words.License license = new Aspose.Words.License();
                license.SetLicense(licenseStream);
            }

            double x = 0, y = 0;
            int pageNumber = 1;

            MemoryStream fileStream = new MemoryStream(file);

            Aspose.Pdf.Document pdfDocument = new Aspose.Pdf.Document(fileStream);
            TextFragmentAbsorber textFragmentAbsorber = new TextFragmentAbsorber("Signature");
            pdfDocument.Pages.Accept(textFragmentAbsorber);

            Aspose.Pdf.Facades.PdfPageEditor editor = new Aspose.Pdf.Facades.PdfPageEditor();
            editor.BindPdf(pdfDocument);

            TextFragmentCollection textFragmentCollection = textFragmentAbsorber.TextFragments;
            foreach (TextFragment textFragment in textFragmentCollection)
            {
                x = textFragment.Position.XIndent;
                y = textFragment.Position.YIndent;
                pageNumber = textFragment.Page.Number;
            }

            Aspose.Pdf.Page page = pdfDocument.Pages[pageNumber];

            FileStream imageStream = new FileStream(imagePath, FileMode.Open);
            System.Drawing.Image image = System.Drawing.Image.FromStream(imageStream);
            var height = image.Height;
            var width = image.Width;

            page.Resources.Images.Add(imageStream);
            page.Contents.Add(new Aspose.Pdf.Operators.GSave());

            Aspose.Pdf.Rectangle rectangle = new Aspose.Pdf.Rectangle(x, y, x + 100, y + 50);
            Aspose.Pdf.Matrix matrix = new Aspose.Pdf.Matrix(new double[] { rectangle.URX - rectangle.LLX, 0, 0, rectangle.URY - rectangle.LLY, rectangle.LLX, rectangle.LLY });


            page.Contents.Add(new Aspose.Pdf.Operators.ConcatenateMatrix(matrix));
            Aspose.Pdf.XImage ximage = page.Resources.Images[page.Resources.Images.Count];

            page.Contents.Add(new Aspose.Pdf.Operators.Do(ximage.Name));

            page.Contents.Add(new Aspose.Pdf.Operators.GRestore());

            MemoryStream outPutFile = new MemoryStream();
            pdfDocument.Save(outPutFile);

            imageStream.Close();

            var fileArray = outPutFile.ToArray();

            return fileArray;
        }

        private static async Task<bool> OpenFileInViewer(long fileId, long documentId, long? transferId, string version, long? delgationId, bool isDraft, string usertoken)
        {
            string viewerUrl = $"{Configuration.ViewerUrl}/api/document/{fileId}/version/{version}/details?ctsDocumentId={documentId}&ctsTransferId={transferId}&delegationId={delgationId}&isDraft={isDraft}";
            var result = await Intalio.Core.Helper.HttpGetAsync<object>(viewerUrl, usertoken);
            if (result != null && ((Newtonsoft.Json.Linq.JContainer)result)?.Count > 0)
                return true;
            return false;
        }

        //check autoforward transfer 
        public static TransferModel CheckAutoForwardTransfer(TransferModel transfer, long UserId)
        {
            if (transfer.ToUserId != null)
            {
                AutoForward autoForward = new AutoForward().FindByUserOrStructurId(transfer.ToStructureId.Value, transfer.ToUserId);

                if (autoForward != null)
                {

                    List<ExceptionRules> ExceptionUser = new ExceptionRules().FindByAutoForwardId(autoForward.Id);

                    var ExceptionUserList = ExceptionUser.Any(X => X.ExceptionUserId.HasValue ?
                    (X.ExceptionStructureId == transfer.FromStructureId && X.ExceptionUserId == UserId) : X.ExceptionStructureId == transfer.ToStructureId);
                    if (ExceptionUserList)
                    {
                        return transfer;
                    }
                    else
                    {
                        transfer.ToStructureId = autoForward.ToStructureId;
                        transfer.ToUserId = autoForward.ToUserId;
                        return transfer;
                    }
                }
                else
                {
                    return transfer;
                }
            }
            else
            {
                return transfer;
            }
        }

        private static ViewerSignModel GetSignPlaceholderDiminsion(byte[] wordFile, string imageAlt)
        {
            ViewerSignModel retValue = null;
            double imageWidth = 0.0;
            double imageHeight = 0.0;
            MemoryStream pdfms = new MemoryStream();
            using (MemoryStream ms = new MemoryStream(wordFile))
            {
                Aspose.Words.Document doc = new Aspose.Words.Document(ms);

                LayoutCollector layoutCollector = new LayoutCollector(doc);
                LayoutEnumerator layoutEnumerator = new LayoutEnumerator(doc);

                Aspose.Words.NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);
                // Iterate over all shapes in the document
                foreach (Aspose.Words.Drawing.Shape shape in doc.GetChildNodes(Aspose.Words.NodeType.Shape, true))
                {
                    // Check if the shape is an image and has alt text "signature"
                    if (shape.HasImage && shape.AlternativeText.ToLower() == imageAlt.ToLower())
                    {
                        // Get the dimensions of the image
                        imageWidth = shape.SizeInPoints.Width;
                        imageHeight = shape.SizeInPoints.Height;

                        var renderObject = layoutCollector.GetEntity(shape);
                        layoutEnumerator.Current = renderObject;
                        System.Drawing.RectangleF rec = layoutEnumerator.Rectangle;
                                     

                        Aspose.Words.Section section = doc.Sections[0];
                        Aspose.Words.PageSetup pageSetup = section.PageSetup;

                        double pageWidth = pageSetup.PageWidth;
                        double pageHeight = pageSetup.PageHeight;

                        double imageX = rec.X; // X position (horizontal)
                        double imageY = rec.Y;    // Y position (vertical)                   
                        retValue = new ViewerSignModel();
                        retValue.pageNumber = GetNodePageNumber(shape);
                        retValue.posX = imageX / pageWidth;
                        retValue.posY = (imageY / pageHeight);
                        retValue.width = imageWidth / pageWidth;
                        retValue.height = imageHeight / pageHeight;
                    }
                }
            }



            return retValue;
        }
        private static int GetNodePageNumber(Aspose.Words.Node node)
        {

            Aspose.Words.DocumentBuilder builder = new Aspose.Words.DocumentBuilder((Aspose.Words.Document)node.Document);
            builder.MoveTo(node);
            Aspose.Words.Fields.Field page = builder.InsertField("PAGE");
            builder.Document.UpdatePageLayout();
            page.Update();
            int pageNumber = Int32.Parse(page.Result);
            page.Remove();
            return pageNumber;
        }

        private static byte[] RemoveSignShape(byte[] file)
        {
            MemoryStream newms = new MemoryStream();
            byte[] newFile;
            using (MemoryStream ms = new MemoryStream(file))
            {
                Aspose.Words.Document doc = new Aspose.Words.Document(ms);
                Aspose.Words.NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);
                foreach (Aspose.Words.Drawing.Shape shape in doc.GetChildNodes(Aspose.Words.NodeType.Shape, true))
                {
                    // Check if the shape is an image and has alt text "signature"
                    if (shape.HasImage && shape.AlternativeText.ToLower() == "signature")
                    {
                        string plankImagePath = Path.GetFullPath(@".\wwwroot\images\blank.png"); // Adjust as needed
                        shape.ImageData.SetImage(plankImagePath);
                    }
                }
                doc.Save(newms, Aspose.Words.SaveFormat.Docx);

                 newFile = newms.ToArray();
                newFile = new WordUtility().ConvertToPDF(newFile);
            }
            return newFile;
        }


        public static List<TransferModel> FilterWithoutCarbonCopy (List<TransferModel> transfers)
        {
            var documentId = transfers[0].DocumentId;
            var carbonCopies = new DocumentCarbonCopy().List(documentId);
            return transfers.Where(e => !carbonCopies.Any(c => c.StructureId == e.ToStructureId)).ToList();
        }

        public static void AddCarbonCopy(List<TransferModel> transfers)
        {
            foreach (var CarbonCopy in transfers)
            {
                DocumentCarbonCopy documentCarbonCopy = new DocumentCarbonCopy()
                {
                    DocumentId = CarbonCopy.DocumentId,
                    StructureId = CarbonCopy.ToStructureId.Value!,
                };
                documentCarbonCopy.Insert();
            }
        }

        private static void AddRejectionReason(Transfer transfer, string note) 
        {
            transfer.RequestStatus = (long)RequestStatuses.Recalled;
            transfer.RejectedDate = DateTime.Now;
            transfer.RejectionReason = note;
            Document document = new Document().FindIncludeAll(transfer.DocumentId.Value);
            document.ModifiedDate = DateTime.Now;
            document.Update();
            //transfer.Document.ModifiedDate = DateTime.Now;
            transfer.UpdateRequestStatus();
        }
        #endregion

        #region Folllow up

        public static bool Delete(long createdByUserId, long userId, long documentId, Language lang = Language.EN)
        {
            bool retValue = false;
            var currentUserId = userId;
            var transferData = new Transfer().FindByDocumentIdToUserId(documentId, userId);
            var transfer = new Transfer().FindIncludeDocumentAndCategory(transferData.Id);
            if (transfer != null && transfer.CreatedByUserId == createdByUserId && transfer.Document.CategoryId == CTS.Core.Configuration.FollowUpCategory)
            {
                transfer.Delete();
                retValue = true;
            }
            return retValue;
        }

        /// <summary>
        /// TransferFollowUpToAssignee
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="structureId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureSender"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="transfer"></param>
        /// <returns></returns>
        public static async Task<List<TransferedDocumentModel>> TransferFollowUpToAssigneeAsync(long userId, long structureId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, AssigneeTransferModel model, Language language = Language.EN)
        {
            List<TransferedDocumentModel> retValue = new List<TransferedDocumentModel>();
            List<long> documentIds = new List<long>();
            List<TransferModel> transfersData = new List<TransferModel>();

            if (!model.FromGrid || model.fromMyRequestsNode)
                documentIds = model.Ids;
            else
            {
                foreach (var transferId in model.Ids)
                {
                    //long documentId = ManageDocument.GetDocumentIdByTransferId(userId, structureIds, isStructureReceiver, privacyLevel, transferId);
                    var document = await ManageDocument.FindDocumentBasicInfoByTransferId(userId, structureIds, isStructureReceiver, privacyLevel, transferId, null, language);

                    if (document != null && document.Id.Value > 0)
                    {
                        documentIds.Add(document.Id.Value);
                    }
                }
            }
            foreach (var documentId in documentIds)
            {
                //DocumentDetailsModel document = ManageDocument.GetDocument(userId, documentId, null, language, true);
                DocumentDetailsModel document = await ManageDocument.GetSearchDocument(userId, structureIds, isStructureReceiver, privacyLevel, documentId);
                //TODO stop the redundant document queries

                //model.DocumentId = transferDocumentId;
                if (document != null)
                {
                    var IsDocumentCompleted = ManageDocument.IsDocumentCompleted(documentId);
                    if (IsDocumentCompleted)
                    {
                        retValue.Add(new TransferedDocumentModel
                        {
                            Updated = false,
                            Message = "DocumentIsCompleted"
                        });
                    }
                    else
                    {
                        var item = new Assignee();

                        if (item.FindSpecific(documentId, model.ToUserId) != null)
                        {
                            retValue.Add(new TransferedDocumentModel
                            {
                                Updated = false,
                                DocumentId = documentId,
                                Message = TranslationUtility.Translate("DuplicatedAssignee", language) + " : " + document.Subject
                            });
                        }
                        else
                        {
                            if (new Document().CheckOriginalDocumentLockedByUser(documentId, userId))
                            {
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = false,
                                    DocumentId = documentId,
                                    Message = "OriginalFileInUse"
                                });
                            }
                            else if (Configuration.AttachmentEditable)
                            {
                                if (new Document().CheckHasLockedAttachmentsByUser(documentId, userId))
                                {
                                    retValue.Add(new TransferedDocumentModel
                                    {
                                        Updated = false,
                                        DocumentId = documentId,
                                        Message = "FileInUse"
                                    });
                                }
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = true,
                                    DocumentId = documentId,
                                    Message = string.Empty
                                });
                            }
                            //long transferId = CreateFollowUpTask(userId, documentId, model);
                            //if (transferId <= 0)
                            //{
                            //    if (transferId == -1)
                            //    {
                            //        retValue.Add(new TransferedDocumentModel
                            //        {
                            //            Updated = false,
                            //            Message = TranslationUtility.Translate("DuplicatedAssignee", language) + " : " + document.Subject
                            //        });
                            //    }
                            //    else
                            //    {
                            //        retValue.Add(new TransferedDocumentModel
                            //        {
                            //            Updated = false,
                            //            Message = string.Empty
                            //        });
                            //    }
                            //}
                            //else
                            //{
                            int addAssigneeResult = await ManageAssignee.CreateAsync(model, documentId, userId, structureIds, isStructureReceiver, privacyLevel, language);
                            if (addAssigneeResult <= 0)
                            {
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = false,
                                    Message = TranslationUtility.Translate("DuplicatedAssignee", language) + " : " + document.Subject
                                });
                            }
                            else
                            {
                                retValue.Add(new TransferedDocumentModel
                                {
                                    Updated = true,
                                    DocumentId = documentId,
                                    Message = string.Empty
                                });
                            }
                            //}
                        }
                    }
                }
                else
                {
                    retValue.Add(new TransferedDocumentModel
                    {
                        Updated = false,
                        DocumentId = documentId,
                        Message = string.Empty
                    });
                }
            }

            return retValue;
        }

        internal static long CreateFollowUpTask(long userId, long documentId, AssigneeTransferModel transfer)
        {
            long transferId = 0;

            var item = new Transfer();
            if (item.FindByDocumentIdToUserId(documentId, transfer.ToUserId) == null)
            {
                List<long> documentsIds = new List<long>();

                documentsIds.Add(documentId);

                item.DocumentId = documentId;
                item.StatusId = (short)DocumentStatus.InProgress;
                item.FromUserId = userId;
                item.ToUserId = transfer.ToUserId;
                item.ToStructureId = transfer.ToStructureId;
                item.FromStructureId = transfer.FromStructureId;
                item.CreatedByUserId = userId;
                item.PurposeId = (short)CTS.Core.Configuration.FollowUpPurpose;
                item.CreatedDate = DateTime.Now;
                item.Insert();

                ManageActivityLog.AddActivityLog(documentId, item.Id, (int)ActivityLogs.FollowUpTask, userId);
                ManageDocument.UpdateDocumentStatusByIds(documentsIds, DocumentStatus.InProgress);
                transferId = item.Id;
            }
            else
            {
                transferId = -1;
            }

            return transferId;
        }

        /// <summary>
        /// RequestToComplete transfer
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="ids"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <returns></returns>
        public static List<CompletedDocumentTransferModel> RequestToComplete(long userId, List<long> ids, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId = null, Language language = Language.EN)
        {
            List<CompletedDocumentTransferModel> retValue = new List<CompletedDocumentTransferModel>();

            var transfers = new Transfer().ListByIds(ids);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfers) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
                structureIds = delegation.StructureIds;
                isStructureReceiver = delegation.IsStructureReceiver;
                privacyLevel = delegation.PrivacyLevel;
            }

            foreach (long id in ids)
            {
                if (ManageUserAccess.HaveTransferAccess(id, userId, structureIds, isStructureReceiver, privacyLevel))
                {
                    var transfer = new Transfer().FindIncludeDocumentAndCategory(id);
                    if (transfer != null)
                    {
                        if (transfer.Document.StatusId != (short)DocumentStatus.Completed)
                        {
                            CompletedDocumentTransferModel model = new CompletedDocumentTransferModel();

                            transfer.StatusId = (short)DocumentStatus.RequestToComplete;
                            transfer.ClosedDate = DateTime.Now;
                            transfer.ModifiedDate = DateTime.Now;
                            transfer.UpdateStatusAndClosedDate();

                            model.Updated = true;
                            model.TransferId = id;

                            retValue.Add(new CompletedDocumentTransferModel
                            {
                                Updated = true,
                            });
                            //Send the email here
                            var followUpDocument = new Document().Find(transfer.DocumentId.Value);
                            if (followUpDocument?.CategoryId == Configuration.FollowUpCategory)
                            {
                                var assigneeToMe = new Assignee().FindSpecific(transfer.DocumentId.Value, userId);
                                if (assigneeToMe != null)
                                {
                                    var requestedBy = IdentityHelper.GetUser(userId, Configuration.IdentityAccessToken);
                                    var sendToUser = IdentityHelper.GetUser(assigneeToMe.CreatedByUserId, Configuration.IdentityAccessToken);
                                    var receiverName = requestedBy.FirstName + " " + requestedBy.LastName;
                                    Dictionary<string, string> bookmark = new Dictionary<string, string>();
                                    bookmark.Add("[CreatedByUser]", receiverName);
                                    bookmark.Add("[FollowUpSubject]", followUpDocument?.Subject);

                                    ManageNotificationTemplate.Send(sendToUser.Email, NotificationTemplateName.OnFollowUpRequestCompletion.ToString(), bookmark);
                                }
                            }

                        }
                        else
                        {
                            retValue.Add(new CompletedDocumentTransferModel

                            {
                                Updated = false,
                                Message = TranslationUtility.Translate("FollowUpCompleted", language)
                            });
                        }
                    }
                    else
                    {
                        retValue.Add(new CompletedDocumentTransferModel
                        {
                            Updated = false,
                            Message = TranslationUtility.Translate("RecordNotFound", language)
                        });
                    }
                }
                else
                {
                    retValue.Add(new CompletedDocumentTransferModel
                    {
                        Updated = false,
                        Message = TranslationUtility.Translate("NoPermission", language)
                    });
                }
            }
            return retValue;
        }

        public static async Task<bool> PostponeTask(long userId, long structureId, List<long> structureIds, bool isStructureSender, bool isStructureReceiver, short privacyLevel, PostponeTaskModel request)
        {
            try
            {
                //if (ManageUserAccess.HaveAccess(request.DocumentId, userId, structureIds, isStructureReceiver, privacyLevel))
                Document document = new Document().FindIncludeDocumentForm(request.DocumentId.Value);
                if (document != null)
                {
                    var DocumentForm = JObject.Parse(document.DocumentForm.Form);

                    DocumentForm["followUpFromDate"] = DateTime.Parse(request.FromDate);
                    DocumentForm["followUpToDate"] = DateTime.Parse(request.ToDate);
                    DocumentForm["status"] = "Postponed";

                    document.DocumentForm = new DocumentForm
                    {
                        Id = document.Id,
                        Form = DocumentForm.ToString(),

                    };
                    document.DocumentDate = DateTime.Parse(request.FromDate);
                    document.DueDate = DateTime.Parse(request.ToDate);
                    document.StatusId = (short)DocumentStatus.Postponed;

                    document.UpdateIncludeDocumentForm();
                }
                else
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public static async Task<bool> PostponeTransfer(PostponeTransferModel request, long userId , Language language = Language.EN)
        {
            try
            {
                Transfer transfer = await new Transfer().FindAsync(request.TransferId.Value);
                //DateTime? oldTransfer = transfer.DueDate;
                if (transfer != null)
                {
                    transfer.DueDate= DateTime.Parse(request.ToDate);
                    //DateTime? newTransfer = DateTime.Parse(request.ToDate);

                    transfer.Update();
                    ManageActivityLog.AddActivityLog(request.DocumentId.HasValue?request.DocumentId.Value:transfer.DocumentId, transfer.Id, (int)ActivityLogs.PostponeTransfer, userId, note: $"{TranslationUtility.Translate("TransferPostponed", language)}");
                    //ManageActivityLog.AddActivityLog(request.DocumentId.HasValue?request.DocumentId.Value:transfer.DocumentId, transfer.Id, (int)ActivityLogs.PostponeTransfer, userId,
                    //   originalValue: "{ \"DueDate\":\"" + (oldTransfer.HasValue ? oldTransfer.Value.ToString(Constants.DATE_FORMAT) : string.Empty) + "\"}",
                    //   newOriginalValue: "{\"DueDate\":\"" + (newTransfer.HasValue ? newTransfer.Value.ToString(Constants.DATE_FORMAT) : string.Empty)+"\"}" , note: "Transfer Postponement");
                }
                else
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// List followups. 
        /// Must be sent to the user or the user structures 
        /// </summary>
        /// <param name="followUpFilters"></param>
        /// <param name="startIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="userId"></param>
        /// <param name="structureIds"></param>
        /// <param name="isStructureReceiver"></param>
        /// <param name="privacyLevel"></param>
        /// <param name="delegationId"></param>
        /// <param name="filter"></param>
        /// <param name="sortExpression"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static (int, List<TransferListViewModel>) ListFollowUps(int startIndex, int pageSize, long userId, List<long> structureIds,
            bool isStructureReceiver, short privacyLevel, long? delegationId = null, ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null,
            Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value) : null;
                var categoryIds = new List<short>();
                if (delegation != null)
                {
                    userId = delegation.FromUserId;
                    structureIds = delegation.StructureIds;
                    categoryIds = delegation.CategoryIds?.Select(t => (short)t).ToList();
                    isStructureReceiver = delegation.IsStructureReceiver;
                    privacyLevel = delegation.PrivacyLevel;
                    if (filter == null)
                        filter = new ExpressionBuilderFilters();

                    if (delegation.ShowOldCorespondence && delegation.StartDate != null && delegation.StartDate != DateTime.MinValue)
                        filter.Add("CreatedDate", delegation.StartDate, Operator.GreaterThanOrEqual);
                    else if (!delegation.ShowOldCorespondence)
                        filter.Add("CreatedDate", delegation.FromDate, Operator.GreaterThanOrEqual);

                    filter.Add("CreatedDate", delegation.ToDate, Operator.LessThanOrEqualTo);
                    filter.Add("Document.Privacy.Level", privacyLevel, Operator.LessThanOrEqualTo);
                }

                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = item.GetFollowUpsCount(userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp);
                var itemList = item.ListFollowUps(startIndex, pageSize, userId, structureIds, categoryIds, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Transfer>());
                return (countResult.Result, itemList.Select(t =>
                {
                    var fromStructure = string.Empty;
                    if (t.FromStructureId.HasValue)
                    {
                        fromStructure = t.FromStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.FromStructure.NameAr))
                        {
                            fromStructure = t.FromStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.FromStructure.NameFr ))
                        {
                            fromStructure = t.FromStructure.NameFr;
                        }
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr ))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr ))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var isOverdue = false;
                    if (t.DueDate.HasValue)
                    {
                        if (t.ClosedDate.HasValue)
                        {
                            isOverdue = t.ClosedDate.Value > t.DueDate.Value;
                        }
                        else
                        {
                            isOverdue = DateTime.Now.Date > t.DueDate.Value;
                        }
                    }
                    var sendingEntity = string.Empty;
                    if (t.Document.SendingEntity != null)
                    {
                        sendingEntity = t.Document.SendingEntity.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameAr ))
                        {
                            sendingEntity = t.Document.SendingEntity.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.Document.SendingEntity.NameFr))
                        {
                            sendingEntity = t.Document.SendingEntity.NameFr;
                        }
                    }

                    List<ReceivingEntityModel> receivers = new List<ReceivingEntityModel>();
                    string receivingEntity = string.Empty;
                    string structureText = string.Empty;
                    string entityGroupText = string.Empty;
                    foreach (var receiver in t.Document.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr) ? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                            entityGroupText += text + Constants.SEPARATOR;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr) ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                            structureText += text + Constants.SEPARATOR;
                        }
                        receivers.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            IsEntityGroup = isEntityGroup,
                            Text = text
                        });
                    }
                    receivingEntity = $"{structureText}{entityGroupText}";
                    if (!string.IsNullOrEmpty(receivingEntity) && receivingEntity.Substring(receivingEntity.Length - 1) == Constants.SEPARATOR)
                    {
                        receivingEntity = receivingEntity.Remove(receivingEntity.Length - 1);
                    }
                    var isEnLang = language == Language.EN;

                    return new TransferListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId.Value,
                        Subject = t.Document.Subject,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.Document.ReferenceNumber,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                        CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Status = t.Document.StatusId,
                        OwnerUserId = t.OwnerUserId,
                        OwnerDelegatedUserId = t.OwnerDelegatedUserId,
                        IsOverDue = isOverdue,
                        IsRead = t.OpenedDate != null ? true : false,
                        IsLocked = t.OwnerUserId != null ? true : false,
                        SentToUser = t.ToUserId != null ? true : false,
                        SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                        FromUserId = t.FromUserId,
                        ToUserId = t.ToUserId,
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId.HasValue ? (isEnLang ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUserId.Value, language)}") : string.Empty,
                        ToUser = t.ToUserId.HasValue ? (isEnLang ? $"{t.ToUser.Firstname} {t.ToUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.ToUserId.Value, language)}") : string.Empty,
                        FromStructure = fromStructure,
                        ToStructure = toStructure,
                        LockedBy = t.OwnerUser != null ? (isEnLang ? $"{t.OwnerUser.Firstname} {t.OwnerUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerUser.Id, language)}") : string.Empty,
                        LockedByDelegatedUser = t.OwnerDelegatedUser != null ? (isEnLang ? $"{t.OwnerDelegatedUser.Firstname} {t.OwnerDelegatedUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.OwnerDelegatedUser.Id, language)}") : string.Empty,
                        LockedDate = t.LockedDate != null ? t.LockedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATETIME_FORMAT24) : string.Empty,
                        CreatedByUser = (isEnLang ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUserId, language)}"),
                        CreatedByUserId = t.Document.CreatedByUserId,
                        Instruction = t.Instruction,
                        Cced = t.Cced,
                        PurposeId = t.PurposeId,
                        PriorityId = t.Document.PriorityId,
                        PrivacyId = t.Document.PrivacyId,
                        ImportanceId = t.Document.ImportanceId,
                        SendingEntity = sendingEntity,
                        CreatedByStructureId = t.Document.CreatedByStructureId,
                        ReceivingEntityId = receivers,
                        ReceivingEntity = receivingEntity,
                        HasAttachment = t.Document.Attachment.Any(),
                        AttachmentCount = t.Document.Attachment.Count(),
                        TransferStatusId = t.StatusId.Value
                    };
                }).ToList());
            }
        }


        /// <summary>
        /// Send email reminder for task
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static async Task<bool> SendEmailReminder(EmailReminderModel request, long userId, List<long> structureIds, bool isStructureReceiver, short privacyLevel, long? delegationId)
        {
            bool result = false;
            try
            {
                if (!string.IsNullOrEmpty(Intalio.Core.Configuration.SmtpSettings.SmtpServer) && !string.IsNullOrEmpty(Intalio.Core.Configuration.SmtpSettings.SystemEmail))
                {
                    System.Net.Mail.SmtpClient smtpClient = new System.Net.Mail.SmtpClient(Intalio.Core.Configuration.SmtpSettings.SmtpServer, Intalio.Core.Configuration.SmtpSettings.Port);
                    if (!string.IsNullOrEmpty(Intalio.Core.Configuration.SmtpSettings.UserName) && !string.IsNullOrEmpty(Intalio.Core.Configuration.SmtpSettings.Password))
                    {
                        smtpClient.Credentials = new NetworkCredential(Intalio.Core.Configuration.SmtpSettings.UserName, Intalio.Core.Configuration.SmtpSettings.Password);
                    }
                    else
                    {
                        smtpClient.UseDefaultCredentials = true;
                    }

                    if (Intalio.Core.Configuration.SmtpSettings.EnableSSL)
                    {
                        smtpClient.EnableSsl = true;
                    }
                    System.Net.Mail.MailMessage mailMessage = new System.Net.Mail.MailMessage();
                    mailMessage.From = new System.Net.Mail.MailAddress(Intalio.Core.Configuration.SmtpSettings.SystemEmail);
                    mailMessage.Subject = request.Subject;
                    mailMessage.Body = request.Body;

                    foreach (var CC in request.CC)
                    {
                        if (!string.IsNullOrEmpty(CC))
                            mailMessage.CC.Add(CC);
                    }

                    foreach (var to in request.To)
                    {
                        if (!string.IsNullOrEmpty(to))
                        {
                            mailMessage.To.Add(to);
                            if (request.AttachmentsIds != null && request.AttachmentsIds.Count > 0)
                            {
                                if (request.AttachmentsIds.Any())
                                {
                                    foreach (var id in request.AttachmentsIds)
                                    {
                                        var attachment = await ManageAttachment.Download(id, null, userId, structureIds, isStructureReceiver, privacyLevel, delegationId, true);
                                        if (attachment != null)
                                        {
                                            Stream stream = new MemoryStream(attachment.Data);
                                            switch (attachment.ContentType.ToLower())
                                            {
                                                case "application/pdf":
                                                    mailMessage.Attachments.Add(new System.Net.Mail.Attachment(stream, string.Format("{0}.pdf", attachment.Name)));
                                                    break;
                                                case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                                                    mailMessage.Attachments.Add(new System.Net.Mail.Attachment(stream, string.Format("{0}.docx", attachment.Name)));
                                                    break;
                                                default:
                                                    mailMessage.Attachments.Add(new System.Net.Mail.Attachment(stream, string.Format("{0}.eml", attachment.Name)));
                                                    break;
                                            }
                                        }
                                    }
                                }
                            }
                            mailMessage.IsBodyHtml = true;
                            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

                            smtpClient.Send(mailMessage);
                        }
                    }
                    result = true;
                }
            }
            catch (Exception ex)
            {
                ExceptionLogger.LogException(ex);
            }

            return result;
        }

        /// <summary>
        /// Create FollowUp and Assginee from transfer
        /// </summary>
        /// <param name="transfers"></param>
        public static void createFollowUpFromTransfer(List<TransferModel> transfers)
        {
            var followUpTransfer = transfers.FirstOrDefault();
            Document getDocumentData = new Document().FindIncludeAll(followUpTransfer.DocumentId);

            Document item = new Document
            {
                CategoryId = (short)Configuration.FollowUpCategory,
                StatusId = (short)DocumentStatus.InProgress,
                CreatedByUserId = getDocumentData.CreatedByUserId,
                CreatedByStructureId = getDocumentData.CreatedByStructureId,
                SenderPerson = getDocumentData.SenderPerson == null ? 0 : getDocumentData.SenderPerson,
                ReceiverPerson = string.IsNullOrEmpty(getDocumentData.ReceiverPerson) ? string.Empty : getDocumentData.ReceiverPerson,
                ByTemplate = getDocumentData.ByTemplate,
                Subject = getDocumentData.Subject,
                Classification = getDocumentData.Classification,
                DocumentType = getDocumentData.DocumentType,
                DueDate = getDocumentData.DueDate,
                Importance = getDocumentData.Importance,
                Priority = getDocumentData.Priority,
                Privacy = getDocumentData.Privacy,
                SendingEntityId = getDocumentData.SendingEntityId,
                ExternalReferenceNumber = getDocumentData.ExternalReferenceNumber,
            };

            item.DocumentForm = new DocumentForm()
            {
                Body = getDocumentData.DocumentForm.Body,
                Form = JsonConvert.SerializeObject(new FollowUpForm()
                {
                    followUpFromDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    followUpToDate = getDocumentData.DueDate.HasValue ? getDocumentData.DueDate.Value.ToString("yyyy-MM-dd") : string.Empty,
                    status = (short)DocumentStatus.InProgress,
                    remind = false,
                    reminderDate = string.Empty,
                    reminderTime = string.Empty,
                    instructions = getDocumentData.DocumentForm.Body,
                }),
                Keyword = getDocumentData.DocumentForm.Keyword
            };

            item.Insert();

            var attachmentFolders = ManageAttachmentFolder.ListByCategoryId((short)Configuration.FollowUpCategory);
            foreach (var attachmentFolder in attachmentFolders)
            {
                var folder = new Folder
                {
                    Name = attachmentFolder.Name,
                    PhysicalName = attachmentFolder.Name,
                    ParentId = attachmentFolder.ParentId,
                    DocumentId = item.Id,
                };
                folder.Insert();
                var childrenFolders = attachmentFolders.Where(x => x.ParentId == attachmentFolder.Id);
                if (childrenFolders.Any())
                {
                    foreach (var childrenFolder in childrenFolders)
                    {
                        childrenFolder.ParentId = (int)folder.Id;
                    }
                }
            }

            foreach (var ReceiverEntity in getDocumentData.DocumentReceiverEntity)
            {
                DocumentReceiverEntity documentReceiver = new DocumentReceiverEntity()
                {
                    DocumentId = item.Id,
                    StructureId = ReceiverEntity.StructureId,
                };
                documentReceiver.Insert();
            }

            foreach (var CarbonCopy in getDocumentData.DocumentCarbonCopy)
            {
                DocumentCarbonCopy documentCarbonCopy = new DocumentCarbonCopy()
                {
                    DocumentId = item.Id,
                    StructureId = CarbonCopy.StructureId,
                };
                documentCarbonCopy.Insert();
            }

            LinkedDocument linkedDocument = new LinkedDocument()
            {
                CreatedByUserId = getDocumentData.CreatedByUserId,
                CreatedDate = DateTime.Now,
                LinkedDocumentId = getDocumentData.Id,
                DocumentId = item.Id,
                TransferId = null,
            };
            linkedDocument.Insert();

            foreach (var assignee in transfers)
            {
                AssigneeTransferModel assigneeTransferModel = null;
                if (assignee.ToUserId == null && assignee.ToStructureId != null)
                {
                    var users = IdentityHelper.GetStructureAllUsers((long)assignee.ToStructureId, Configuration.IdentityAccessToken);
                    foreach (var user in users)
                    {
                        if (user.Id != getDocumentData.CreatedByUserId)
                        {
                            assigneeTransferModel = new AssigneeTransferModel()
                            {
                                FollowUpSecurityId = 2,
                                FromStructureId = assignee.FromStructureId.Value,
                                ToStructureId = assignee.ToStructureId.Value,
                                ToUserId = user.Id,
                                Instruction = assignee.Instruction,
                            };
                            ManageAssignee.AddAssignee(getDocumentData.CreatedByUserId, item.Id, assigneeTransferModel);
                        }

                    }
                }
                else
                {
                    assigneeTransferModel = new AssigneeTransferModel()
                    {
                        FollowUpSecurityId = 2,
                        FromStructureId = assignee.FromStructureId.Value,
                        ToStructureId = assignee.ToStructureId.Value,
                        ToUserId = assignee.ToUserId.Value,
                        Instruction = assignee.Instruction,
                    };
                    int manageAssignee = ManageAssignee.AddAssignee(getDocumentData.CreatedByUserId, item.Id, assigneeTransferModel);
                }
            }
        }

        public static  List<TransferListViewModel> ListExportedRequests(long documentId, Language language = Language.EN)
        {
            List<TransferListViewModel> retValue =  new List<TransferListViewModel>();
            
                using (Transfer item = new Transfer())
                {
                    var itemList =  item.ListExportedRequests( documentId);
                
                    retValue = ( itemList.Select(t =>
                    {
                        
                        var toStructure = string.Empty;
                        if (t.ToStructureId.HasValue)
                        {
                            toStructure = t.ToStructure.Name;
                            if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                            {
                                toStructure = t.ToStructure.NameAr;
                            }
                            else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                            {
                                toStructure = t.ToStructure.NameFr;
                            }
                        }                        
                        var transferListViewModel = new TransferListViewModel
                        {
                            Id = t.Id,
                            DocumentId = t.DocumentId.Value,
                            Subject = t.Document.Subject,
                            CategoryId = t.Document.CategoryId,
                            ReferenceNumber = t.Document.ReferenceNumber,
                            TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                            TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                            CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                            DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            Status = t.Document.StatusId,
                            IsRead = t.OpenedDate != null ? true : false,
                            SentToUser = t.ToUserId != null ? true : false,
                            SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                            ToStructure = toStructure,
                            OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                            CreatedByUser = language == Language.EN ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                            FromStructureId = t.FromStructureId,
                            ToStructureId = t.ToStructureId,
                            FromUser = "",
                            ToUser = "",
                            RequestStatus = ((RequestStatuses)t.RequestStatus).ToString(),
                            RejectionReason = t.RejectionReason,
                            ExportedDocumentId = t.ExportedDocumentId
                        };


                        return transferListViewModel;
                    }).ToList());
                }
            
            return retValue;
        }


        public static async Task<(int, List<TransferListViewModel>)> ListExportedDocuments(int startIndex, int pageSize, long userId, long structureId, bool isStructureReceiver, short privacyLevel,
        ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = await item.GetExportedDocumentsTotalCountAsync(userId, structureId, isStructureReceiver, privacyLevel, filterExp);
                var itemList = await item.ListExportedDocumentsAsync(startIndex, pageSize, userId, structureId, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Transfer>());
                return (countResult, itemList.Select(t =>
                {
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr ))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr ))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var exportedDate = t.Document.CreatedDate; /*(t.ExportedDocument.Transfer.Any() ? t.ExportedDocument.Transfer.FirstOrDefault(s => s.IsSigned == true)?.ExportedDate : null)*/
                    var transferListViewModel = new TransferListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId.Value,
                        Subject = t.Document.Subject,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.ExportedDocument.ReferenceNumber,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                        CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Status = t.Document.StatusId,
                        IsRead = t.OpenedDate != null ? true : false,
                        SentToUser = t.ToUserId != null ? true : false,
                        SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                        ToStructure = toStructure,
                        OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedByUser = language == Language.EN ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                        FromStructureId = t.FromStructureId,
                        FromUserId = t.FromUserId,
                        ToStructureId = t.ToStructureId,
                        FromUser = t.FromUserId != null ? (language == Language.EN ? $"{t.FromUser.Firstname} {t.FromUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.FromUser.Id, language)}") : string.Empty,
                        ToUser = "",
                        RequestStatus = ((RequestStatuses)t.RequestStatus).ToString(),
                        RejectionReason = t.RejectionReason,
                        ExportedDocumentId = t.ExportedDocumentId,
                        //ExportedDate = t.ExportedDate?.ToString(Constants.DATE_FORMAT),
                        ExportedDate = /*exportedDate.HasValue ? */exportedDate.ToString(Constants.DATE_FORMAT) /*: ""*/,
                        //ExportedNumberOfDays = t.ExportedDate.HasValue ? (DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(t.ExportedDate.Value).DayNumber) : null)
                        ExportedNumberOfDays = /*exportedDate.HasValue ?  */DateOnly.FromDateTime(DateTime.Now).DayNumber - DateOnly.FromDateTime(exportedDate).DayNumber /*: null*/,
                        Document = t.Document
                    };


                    return transferListViewModel;
                }).ToList());
            }
        }
        
        public static async Task<(int, List<TransferListViewModel>)> ListRejectedDocuments(int startIndex, int pageSize, long userId, long structureId, bool isStructureReceiver, short privacyLevel,
        ExpressionBuilderFilters filter = null, List<SortExpression> sortExpression = null, Language language = Language.EN)
        {
            using (Transfer item = new Transfer())
            {
                var filterExp = filter != null ? ExpressionBuilder.GetExpression<Transfer>(filter, ExpressionBuilderOperator.And) : null;
                var countResult = await item.GetRejectedDocumentsTotalCountAsync(userId, structureId, isStructureReceiver, privacyLevel, filterExp);
                var itemList = await item.ListRejectedDocumentsAsync(startIndex, pageSize, userId, structureId, isStructureReceiver, privacyLevel, filterExp, sortExpression?.OrderByExpression<Transfer>());
                return (countResult, itemList.Select(t =>
                {
                    var receivingEntities = new List<ReceivingEntityModel>();
                    foreach (var receiver in t.Document.DocumentReceiverEntity)
                    {
                        long targetId = receiver.StructureId.HasValue ? receiver.StructureId.Value : receiver.EntityGroupId.Value;
                        bool isEntityGroup = receiver.EntityGroupId.HasValue;
                        string text = string.Empty;
                        if (isEntityGroup)
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameAr)? receiver.EntityGroup.NameAr : receiver.EntityGroup.Name
                            : language == Language.FR ? !string.IsNullOrEmpty(receiver.EntityGroup.NameFr) ? receiver.EntityGroup.NameFr : receiver.EntityGroup.Name
                            : receiver.EntityGroup.Name;
                        }
                        else
                        {
                            text = language == Language.AR ? !string.IsNullOrEmpty(receiver.Structure.NameAr) ? receiver.Structure.NameAr : receiver.Structure.Name
                           : language == Language.FR ? !string.IsNullOrEmpty(receiver.Structure.NameFr)  ? receiver.Structure.NameFr : receiver.Structure.Name
                           : receiver.Structure.Name;
                        }

                        receivingEntities.Add(new ReceivingEntityModel
                        {
                            Id = targetId,
                            Text = text,
                            IsEntityGroup = isEntityGroup,
                            IsExternal = receiver.Structure.IsExternal,
                        });
                    }
                    var toStructure = string.Empty;
                    if (t.ToStructureId.HasValue)
                    {
                        toStructure = t.ToStructure.Name;
                        if (language == Language.AR && !string.IsNullOrEmpty(t.ToStructure.NameAr))
                        {
                            toStructure = t.ToStructure.NameAr;
                        }
                        else if (language == Language.FR && !string.IsNullOrEmpty(t.ToStructure.NameFr))
                        {
                            toStructure = t.ToStructure.NameFr;
                        }
                    }
                    var transferListViewModel = new TransferListViewModel
                    {
                        Id = t.Id,
                        DocumentId = t.DocumentId.Value,
                        Subject = t.Document.Subject,
                        CategoryId = t.Document.CategoryId,
                        ReferenceNumber = t.ExportedDocument.ReferenceNumber,
                        TransferDate = t.CreatedDate.ToString(Constants.DATE_FORMAT),
                        TransferTime = t.CreatedDate.ToString(Constants.TIME_FORMAT),
                        CreatedDate = t.Document.CreatedDate.ToString(Constants.DATE_FORMAT),
                        DueDate = t.DueDate != null ? t.DueDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        Status = t.Document.StatusId,
                        IsRead = t.OpenedDate != null ? true : false,
                        SentToUser = t.ToUserId != null ? true : false,
                        SentToStructure = !t.ToUserId.HasValue && t.ToStructureId != null ? true : false,
                        ToStructure = toStructure,
                        OpenedDate = t.OpenedDate != null ? t.OpenedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        ClosedDate = t.ClosedDate != null ? t.ClosedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty,
                        CreatedByUser = language == Language.EN ? $"{t.Document.CreatedByUser.Firstname} {t.Document.CreatedByUser.Lastname}" : $"{IdentityHelperExtension.GetFullName(t.Document.CreatedByUser.Id, language)}",
                        FromStructureId = t.FromStructureId,
                        ToStructureId = t.ToStructureId,
                        FromUser = "",
                        ToUser = "",
                        RequestStatus = ((RequestStatuses)t.RequestStatus).ToString(),
                        RejectionReason = t.RejectionReason,
                        ExportedDocumentId = t.ExportedDocumentId,
                        IsDraft = t.ExportedDocument?.StatusId == (int)DocumentStatus.Draft,
                        IsLocked = t.ExportedDocument?.Transfer?.LastOrDefault()?.OwnerUserId != null ? true : false,
                        OwnerUserId = t.ExportedDocument?.Transfer?.LastOrDefault()?.OwnerUserId,
                        OwnerDelegatedUserId = t.ExportedDocument?.Transfer?.LastOrDefault()?.OwnerDelegatedUserId,
                        isInProgress = t.ExportedDocument?.Transfer?.LastOrDefault()?.StatusId == (short)DocumentStatus.InProgress,
                        ExportedDocument = t.ExportedDocument,
                        SignedTransferId = t.ExportedDocument.Transfer.Any() ? t.ExportedDocument.Transfer.FirstOrDefault(s => s.IsSigned == true).Id : null,
                        ReceivingEntityId = receivingEntities,
                        Document = t.Document,
                        RejectedDate = t.RejectedDate != null ? t.RejectedDate.Value.ToString(Constants.DATE_FORMAT) : string.Empty
                    };


                    return transferListViewModel;
                }).ToList());
            }
        }

        public static async Task<int> GetExportedDocumentsTotalCount(long? userId, long? loggedInStructureId,bool isStructureReceiver, short privacyLevel)
        {
            using (Transfer item = new Transfer())
            {
                return await item.GetExportedDocumentsTotalCountAsync((long)userId, (long)loggedInStructureId, isStructureReceiver, privacyLevel, null);
            }
        }

        public static async Task<int> GetExportedDocumentsTodayCount(long? userId, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel)
        {
            using (Transfer item = new Transfer())
            {
                return await item.GetExportedDocumentsTodayCountAsync((long)userId, (long)loggedInStructureId, isStructureReceiver, privacyLevel);
            }
        }

        public static async Task<int> GetExportedDocumentsUnreadCount(long? userId, long? loggedInStructureId,bool isStructureReceiver, short privacyLevel)
        {
            using (Transfer item = new Transfer())
            {
                return await item.GetExportedDocumentsUnreadCountAsync((long)userId, (long)loggedInStructureId, isStructureReceiver, privacyLevel);
            }
        }

        public static async Task<int> GetRejectedDocumentsTotalCount(long? userId, long? loggedInStructureId,bool isStructureReceiver, short privacyLevel)
        {
            using (Transfer item = new Transfer())
            {
                return await item.GetRejectedDocumentsTotalCountAsync((long)userId, (long)loggedInStructureId, isStructureReceiver, privacyLevel, null);
            }
        }

        public static async Task<int> GetRejectedDocumentsTodayCount(long? userId, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel)
        {
            using (Transfer item = new Transfer())
            {
                return await item.GetRejectedDocumentsTodayCountAsync((long)userId, (long)loggedInStructureId, isStructureReceiver, privacyLevel);
            }
        }

        public static async Task<int> GetRejectedDocumentsUnreadCount(long? userId, long? loggedInStructureId, bool isStructureReceiver, short privacyLevel)
        {
            using (Transfer item = new Transfer())
            {
                return await item.GetRejectedDocumentsUnreadCountAsync((long)userId, (long)loggedInStructureId, isStructureReceiver, privacyLevel);
            }
        }

        public static bool Dismiss(long userId, long id, long? delegationId, string note, long structureId, Language lang = Language.EN, bool fromExported = false)
        {
            bool retValue = false;
            var currentUserId = userId;
            var transfer = new Transfer().FindIncludeToUserToStructure(id);
            var delegation = delegationId.HasValue ? ManageDelegation.GetByDelegationId(userId, delegationId.Value, transfer.CreatedDate) : null;
            if (delegation != null)
            {
                userId = delegation.FromUserId;
            }
            if (fromExported && transfer != null && transfer.FromStructureId == structureId)
            {
                transfer.RequestStatus = (long)RequestStatuses.Dismissed;
                transfer.UpdateRequestStatus();
                retValue = true;
            }
            //if (transfer != null && transfer.FromUserId == userId && transfer.OpenedDate == null && transfer.ClosedDate == null)
            //{

            //    if (transfer.ParentTransferId != default)
            //    {
            //        var parentTransfer = new Transfer().Find((long)transfer.ParentTransferId);
            //        parentTransfer.ClosedDate = null;
            //        parentTransfer.StatusId = (short)DocumentStatus.InProgress;
            //        parentTransfer.UpdateStatusAndClosedDate();
            //        //Logs are deleted on transfer.Delete() due to cascade relation, ParentTransferId was used instead
            //        ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);

            //    }
            //    else
            //    {
            //        ManageDocument.UpdateDocumentStatusById(transfer.DocumentId.Value, DocumentStatus.Draft);
            //        ManageActivityLog.AddActivityLog(transfer.DocumentId.Value, transfer.ParentTransferId, (int)ActivityLogs.Recall, currentUserId, note: note);
            //    }
            //    new Attachment().ResetAttachmentsTransfer(id);
            //    if (!fromExported)
            //        transfer.Delete();



            //    retValue = true;
            //}
            return retValue;
        }

        public static (bool Valid , CompletedDocumentTransferModel model) CheckCanExportOrComplete(long userId, Transfer transfer, List<long> structureIds, bool isStructureReceiver, short privacyLevel,bool fromExport, bool overrideAccess = false, Language? language = null)
        {
            (bool Valid, CompletedDocumentTransferModel model) retValue = (true, new CompletedDocumentTransferModel());
            var currentUserId = userId;
            if (ManageUserAccess.HaveTransferAccess(transfer.Id, userId, structureIds, isStructureReceiver, privacyLevel) || overrideAccess)
            {
                retValue.model.DocumentAttachmentIdHasValue = transfer.Document.AttachmentId.HasValue;
                bool isFollowUp = (transfer.Document.CategoryId == Configuration.FollowUpCategory);

                bool isInternalBroadcast = false;
                dynamic categoryBasicAttribute = JsonConvert.DeserializeObject<dynamic>(transfer.Document.Category.BasicAttribute);
                if (!ReferenceEquals(null, categoryBasicAttribute) && !isFollowUp)
                {
                    foreach (var attr in categoryBasicAttribute.Root)
                    {
                        if (attr.Name.Value == "ReceivingEntity")
                        {
                            if (!ReferenceEquals(null, attr.BroadcastReceivingEntity) && attr.BroadcastReceivingEntity.Value == true && attr.Type.Value == "internal")
                            {
                                isInternalBroadcast = true;
                            }
                            break;
                        }
                    }
                }

                if (Configuration.AttachmentEditable)
                {
                    if (new Document().CheckHasLockedAttachmentsByUser(transfer.DocumentId.Value, userId))
                    {
                        retValue.Valid = false;
                        retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                        Language lang = language.HasValue ? language.Value : Language.EN;
                        retValue.model.Message = fromExport? "HasLockedAttachmentsByUser" : TranslationUtility.Translate("HasLockedAttachmentsByUser", lang);
                        return retValue;
                    }
                }
                else if (new Document().CheckOriginalDocumentLockedByUser(transfer.DocumentId.Value, userId))
                {
                    retValue.Valid = false;
                    retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    retValue.model.Message = fromExport ? "OriginalDocumentLockedByUser" : TranslationUtility.Translate("OriginalDocumentLockedByUser", lang);
                    return retValue;
                }

                if (string.IsNullOrEmpty(transfer.Document.ReferenceNumber))
                {
                    retValue.Valid = false;
                    retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    retValue.model.Message = fromExport ? "NoReferenceNumber" : TranslationUtility.Translate("NoReferenceNumber", lang);
                    return retValue;
                }

                if (((transfer.OwnerUserId.HasValue && transfer.OwnerUserId.Value != userId) || (transfer.Cced && !isInternalBroadcast)) && !isFollowUp && !fromExport)
                {
                    retValue.Valid = false;
                    retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    retValue.model.Message = fromExport ? "TransferHasDifferentOwnerOrIsCarbonCopy" : TranslationUtility.Translate("TransferHasDifferentOwnerOrIsCarbonCopy", lang);
                    return retValue;
                }

                if (transfer.WorkflowStepId != null)
                {
                    retValue.Valid = false;
                    retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    retValue.model.Message = fromExport ? "WorkflowCannotbeCompleted" : TranslationUtility.Translate("WorkflowCannotbeCompleted", lang);
                    return retValue;
                }

                if (!transfer.Document.AttachmentId.HasValue)
                {
                    retValue.Valid = false;
                    retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                    Language lang = language.HasValue ? language.Value : Language.EN;
                    retValue.model.Message = fromExport ? "CorrespondenceNotCompleteNoOriginalMail" : TranslationUtility.Translate("CorrespondenceNotCompleteNoOriginalMail", lang);
                    return retValue;
                }

                return retValue;

            }
            else
            {
                retValue.Valid = false;
                retValue.model.UncompletedDocumentReferenceNumber = transfer.Document.Subject;
                Language lang = language.HasValue ? language.Value : Language.EN;
                retValue.model.Message = fromExport ? "HasNoAccess" : TranslationUtility.Translate("HasNoAccess", lang);
                return retValue;
            }
        }

        public static Transfer FindIncludeDocumentAndCategory(long id)
        {
            return new Transfer().FindIncludeDocumentAndCategory(id);
        }
        public static void UpdateExportedDate(Transfer item,bool isExoported, DateTime? exportedDate)
        {
            item.UpdateExportedDate(isExoported, exportedDate);
        }

        #endregion
    }

}
