import Intalio from './common.js'
import { IdentityService } from './lookup.js'

class AddBasketFromNodeIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.itemsNames = [];
        this.selectedDocumentIds = [];
    }
}
var itemsNames;
var selectedDocumentIds = [];

class AddBasketFromNodeIndexView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "basketFromNode", model);
    }
    render()
    {
        var self = this;
        itemsNames = self.model.itemsNames;
        selectedDocumentIds = self.model.selectedDocumentIds;
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#btnCloseAddDocumentToBasket').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnSubmitAddDocumentToBasket').focus();
                }
               
            }
        });
        $('#txtNameonly').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnCloseAddDocumentToBasket').focus();
                }
                else {
                    $('#txtNameAronly').focus();
                }
            }
        });
        $('#txtNameAronly').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtNameonly').focus();
                }
                else {
                    $('#txtNameFronly').focus();
                }
            }
        });
        $('#txtNameFronly').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#txtNameAronly').focus();
                }
                else {
                    $('#btnSubmitAddDocumentToBasket').focus();
                }
            }
        });
       
        $('#formAddDocumentToBasketPost').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
                $('#btnSubmitAddDocumentToBasket').trigger("click");
            }
        });
        Common.gridCommon();

        $("#basketNames").select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $("#indexAddToBasketContainer"),
            ajax: {
                type: 'GET',
                url: '/Basket/ListWithEditRole',
                headers: {
                    "Authorization": 'Bearer ' + window.IdentityAccessToken
                },
                dataType: 'json',
                delay: 400,
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var item = {};
                        item.id = val.id;
                        if (window.language == "ar")
                            item.text = val.nameAr ?? val.name;
                        else if (window.language == "fr")
                            item.text = val.nameFr ?? val.name;
                        else
                            item.text = val.name;

                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            }
        }).on('change', function () {
            $(this).trigger('input');
        });
        $('#radioExistBasket').on('change', function () {
            if ($(this).is(':checked')) {
                $('#existBasketContainer').show();
                $('#newBasketContainer').hide();
            }
        });

        $('#radioNewBasket').on('change', function () {
            if ($(this).is(':checked')) {
                $('#existBasketContainer').hide();
                $('#newBasketContainer').show();
            }
        });
        $('#btnSubmitAddDocumentToBasket').on('click', function () {
            var $form = $('#formAddDocumentToBasketPost');
            $form.parsley().reset();
            var isValid = true;

            if ($('#radioExistBasket').is(':checked')) {
                isValid = $('#basketNames').parsley().validate();
            } else if ($('#radioNewBasket').is(':checked')) {
                isValid = $('#txtNameonly').parsley().validate() &&
                    $('#txtNameAronly').parsley().validate() &&
                    $('#txtNameFronly').parsley().validate();
            }
            if (isValid == true) {
                let params = {
                    "basketId": $('#radioExistBasket').is(':checked') ? $('#basketNames').val() : null,
                    "documentIds": selectedDocumentIds,
                    "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                };
                
                var btn = $('#btnSubmitAddDocumentToBasket');
                btn.button('loading');
                var btnClose = $('#btnCloseAddDocumentToBasket');
                var btnCloseX = $('#BasketClose');
                btnClose.attr('disabled', 'disabled');
                btnCloseX.attr('disabled', 'disabled');
                if ($('#radioExistBasket').is(':checked')) {
                    Common.ajaxPost('/Basket/AddBasketDocuments', params, function (data) {
                        Common.showScreenSuccessMsg();
                        $("#modalAddToBasket").modal("hide");
                    }, function () { gIsLocked = false; btn.button('reset'); btnClose.removeAttr('disabled'); btnCloseX.removeAttr('disabled'); Common.showScreenErrorMsg(); }, false);

                }
                else if ($('#radioNewBasket').is(':checked')) {
                    const cleanupAndCloseModal = () => {
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        $("#modalAddToBasket").modal("hide");
                    };

                    let params = {
                        'Name': $('#txtNameonly').val(),
                        'NameAr': $('#txtNameAronly').val(),
                        'NameFr': $('#txtNameFronly').val(),
                        'BasketUsersModel': [],
                        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    };
                    var Basketname = $('#txtNameonly').val();
                    if (window.language == 'ar') {
                        Basketname = $('#txtNameAronly').val();
                    } else if (window.language == 'fr') {
                        Basketname = $('#txtNameFronly').val();
                    }

                    Common.ajaxPost('/Basket/Index', params, function (data) {
                        if (data.message !== null && data.message !== "") {
                            Common.alertMsg(data.message);
                            cleanupAndCloseModal();
                        } else {
                            window.userMenus.baskets.push({
                                'basketDocuments': null,
                                'basketPermissions': null,
                                'createdByUser': null,
                                'createdByUserId': $('#hdUserId').val(),
                                'id': data.id,
                                'name': params.Name,
                                'nameAr': params.NameAr,
                                'nameFr': params.NameFr
                            });
                            let Addparams = {
                                "basketId": data.id,
                                "documentIds": selectedDocumentIds,
                                "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                            }
                            $('#BasketName').text(Basketname);
                            var liId = "#liBasket" + data.id;
                            var liElement = $(liId);
                            if (liElement.length) {
                                var spanElement = liElement.find('span');
                                spanElement.text(Basketname);
                            }
                            else {
                                $('#MyBaskets').removeClass('hidden');
                                var liHtml = '<li id="liBasket' + data.id + '"><a href="#basket/' + data.id + '" title="' + Basketname + '"><span style="white-space: normal; word-break: break-word;">' + Basketname + '</span></a></li>';
                                $('#nav-MyBaskets').append(liHtml);
                            }

                            Common.ajaxPost('/Basket/AddBasketDocuments', Addparams, function (data) {
                                Common.showScreenSuccessMsg();
                                cleanupAndCloseModal();
                            }, function () {
                                gIsLocked = false;
                                Common.showScreenErrorMsg();
                                cleanupAndCloseModal();
                            }, false);
                        }
                    },
                        function () {
                            Common.alertMsg(Resources.BasketNameAlreadyExist);
                            btn.button('reset');
                            btnClose.removeAttr('disabled');
                            btnCloseX.removeAttr('disabled');
                        }, false);
                }
            }
        });
    };
    setData(data)
    {
        itemsNames = data;
    }
}
export default { AddBasketFromNodeIndex, AddBasketFromNodeIndexView };
