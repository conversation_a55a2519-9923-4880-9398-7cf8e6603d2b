﻿import Intalio from './common.js'
import { IdentityService } from './lookup.js'


class SendToReceivingEntityIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.purposes = [];
        this.transferToType = TransferType.Send;
        this.transferToUser = null;
        this.transferToStructure = null;
        this.customAttributeDueDate = null;
        this.documentId = null;
        this.transferId = null;
        this.isBroadcast = false;
        this.broadcastIds = [];
        this.fromVip = false;
        this.isStructureSender = false;
        this.structureIds = [];
        this.enableSendingRules = false;
        this.enableTransferToUsers = false;
        this.delegationId;
        this.fromStructureId = null;
        this.signatureTemplate = null;
        this.withSign = false;
        this.externalReferenceNumber = null;
        this.fromStructureInbox = false;
    }
}
var gLocked = false;
var purposeId = 0;
function sendToReceivingEntitySubmit() {
    var $form = $('#formSendPost');
    $form.parsley().reset();
    var isValid = $form.parsley().validate();
    if (isValid) {
        if (self.model.purposeIdForSignature != 0) {
            sendPurposeId = self.model.purposeIdForSignature;
        }
        else
            sendPurposeId = $("#cmbSendPurpose").val();

        //let sendPurposeId = $("#cmbSendPurpose").val();
        let sendDueDate = $("#sendDueDate").val();
        let id = self.model.documentId;
        let transferId = self.model.transferId;

        if (sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") == null) {
            sessionStorage.setItem($("#hdUserId").val() + "loggedInStructure", $('#hdLoggedInStructureId').val());
        }
        let userStructureId = window.EnablePerStructure ? sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") : sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
        let sendDelegationId = $("#hdSendDeligationId").val();
        let instruction = $('#txtAreaInstruction').val();
        if ($(CKEDITOR.instances["txtAreaInstruction"].getData()).text().replace(/\r?\n|\r/gm, " ").replace(/\s\s+/g, " ").trim() !== "") {
            instruction = CKEDITOR.instances["txtAreaInstruction"].getData();
        }
        let structureReceiverIds = $('.cmbCustomAttributeReceiver').val();
        if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || !structureReceiverIds) {
            structureReceiverIds = [];
            for (var i = 0; i < self.model.receivingEntities.length; i++) {
                structureReceiverIds.push(self.model.receivingEntities[i].id);
            }
        }
        let params = {
            'id': id,
            'transferId': transferId === "" ? null : transferId,
            'purposeId': sendPurposeId,
            'dueDate': sendDueDate,
            'instruction': instruction,
            'structureId': userStructureId,
            'delegationId': sendDelegationId,
            'structureReceivers': structureReceiverIds,
            'transferToType': self.model.transferToType,
            'withSign': self.model.withSign,
            'SignatureTemplateId': self.model.signatureTemplate,
            'documentId': self.model.documentId === "" ? null : self.model.documentId,
            'fromStructure': self.model.fromStructureInbox,

            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };
        let url = '/Document/Send';
        let isComplete = false;
        let purposeIsEmpty = false;
        let isDraft = self.model.isDraft;
        let transferList = [];
        if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || self.model.transferToType == TransferType.ReplyToInitiator) {
            url = '/Transfer/Reply';
        } else if (self.model.transferToType == TransferType.BroadcastComplete) {
            if ($('#cmbSendPurpose').val() == null) {
                Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                purposeIsEmpty = true;
            }
            params.TransferIds = self.model.broadcastIds;
            url = '/Transfer/BroadcastComplete';
            isComplete = true;
        } else if (self.model.transferToType == TransferType.BroadcastSend) {
            if ($('#cmbSendPurpose').val() == null) {
                Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                purposeIsEmpty = true;
            }
            url = '/Transfer/BroadcastSend';
        }

        else if (self.model.transferToType == TransferType.SignAndSend) {
            var transferToObj = $(self.refs['cmbCustomAttributeReceiver']).select2('data');
            if (typeof transferToObj !== typeof undefined && transferToObj.length > 0) {

                transferToObj = transferToObj[0];
                var selectedOption = $(transferToObj.element);
                var isStructure = typeof transferToObj.isStructure === 'undefined' ? selectedOption.data('isStructure') : transferToObj.isStructure;
                var toUserStructureId = typeof transferToObj.structureId === 'undefined' ? selectedOption.data('structureId') : transferToObj.structureId;
                var toStructureId = isStructure ? parseInt(transferToObj.id.split("Structure")[1]) : parseInt(toUserStructureId);
                var toUserId = isStructure ? null : parseInt(transferToObj.id.split("_")[2]);
                var transferItem = {
                    documentId: self.model.documentId,
                    parentTransferId: self.model.transferId,
                    toStructureId: toStructureId,
                    toUserId: toUserId,
                    name: transferToObj.text,
                    dueDate: sendDueDate,
                    purposeId: sendPurposeId,
                    instruction: instruction,
                    cced: false,
                    fromStructureId: self.model.fromStructureId,
                    'fromStructure': self.model.fromStructureInbox,
                }

                transferList.push(transferItem);
            }

        }
        if (!purposeIsEmpty) {
            if (window.EnableConfirmationMessage === "True") {
                var messageSend = Resources.SendCorrespondenceConfirmation;
                var receiversArray = [];
                if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToInitiator) {
                    receiversArray.push({ 'text': self.model.transferToUser });
                } else if (self.model.transferToType == TransferType.ReplyToStructure) {
                    receiversArray = self.model.receivingEntities;
                }
                else {
                    receiversArray = $('.cmbCustomAttributeReceiver').select2("data");
                    if (!receiversArray) {
                        receiversArray = self.model.receivingEntities;
                    }
                }
                for (var i = 0; i < receiversArray.length; i++) {
                    messageSend += ' \n ○ ' + receiversArray[i].text;
                }
                Common.showConfirmMsg(messageSend, function () {
                    if (!gLocked) {
                        gLocked = true;
                        try {
                            if (self.model.transferToType == TransferType.SignAndSend) {
                                Transfer(transferList, self.model.delegationId, false, false);
                            }
                            else {
                                sendToReceivingEntity(url, params, transferId, isComplete, isDraft, self.model.isBroadcast, self.model.fromVip, self.callback,self);
                            }
                        } catch (e) {
                            gLocked = false;
                        }
                    }
                });
            } else {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        if (self.model.transferToType == TransferType.SignAndSend) {
                            Transfer(transferList, self.model.delegationId, false, false);
                        }
                        else {
                            sendToReceivingEntity(url, params, transferId, isComplete, isDraft, self.model.isBroadcast, self.model.fromVip, self.callback,self);
                        }
                    } catch (e) {
                        gLocked = false;
                    }
                }
            }
        }


    }
}
function sendToReceivingEntity(url, params, transferId, isComplete, isDraft, isBroadcast, fromvip, callback,self, callback2 = null)
{
    var btn = $('#btnSendToReceivingEntity');
    btn.button('loading');
    var btnClose = $('#btnCloseSendToReceivingEntity');
    var btnCloseX = $('#sendToRecEntityClose');
    btnClose.attr('disabled', 'disabled');
    btnCloseX.attr('disabled', 'disabled');
    if ((url.includes('Export') || url.includes('ResendRejectedDocument')) && typeof callback === 'function') {
        Common.showScreenSuccessMsg(Resources.ExportingDocument);
        callback();
        Common.unmask("body-mask");
    }
    Common.ajaxPost(url, params, function (data)
    {
        gLocked = false;
        btn.button('reset');
        btnClose.removeAttr('disabled');
        btnCloseX.removeAttr('disabled');
        if (typeof data != 'string' && data.length > 0)
        {
            let msg = "";
            let refresh = false;
            let transferIds = [];
            if (data.length == 1)
            {
                if (!data[0].uncompletedDocumentReferenceNumber && data[0].message) {
                    msg = Resources[data[0].message];
                } else if (data[0].updated && data[0].uncompletedDocumentReferenceNumber && !data[0].message) {
                    msg = Resources[data[0].message];
                } else if (!data[0].updated && data[0].uncompletedDocumentReferenceNumber && data[0].message) {
                    msg = Resources[data[0].message];
                }
                else if (!data[0].updated) {
                    msg = Resources.CannotCompleteWarning + "\n ○ " + data[0].uncompletedDocumentReferenceNumber;
                }
                else {
                    msg = data[0].uncompletedDocumentReferenceNumber;
                }
                refresh = data[0].updated;
                if (data[0].updated)
                {
                    if (data[0].transferId === 0)
                    {
                        transferIds.push(data[0].documentId);
                    } else
                    {
                        transferIds.push(data[0].transferId);
                    }
                }
            }
            else
            {
                for (var i = 0; i < data.length; i++)
                {
                    if (!data[i].updated && data[i].uncompletedDocumentReferenceNumber)
                    {
                        msg += "\n ○ " + data[i].uncompletedDocumentReferenceNumber;
                    }
                    if (data[i].updated)
                    {
                        refresh = true;
                        if (data[i].transferId === 0)
                        {
                            transferIds.push(data[i].documentId);
                        } else
                        {
                            transferIds.push(data[i].transferId);
                        }
                    }
                }
                if (msg !== "")
                {
                    msg = Resources.CannotCompleteWarning + msg;
                }
            }
            if (msg !== "")
            {
                setTimeout(function ()
                {
                    Common.alertConfirmation(msg, function ()
                    {
                        $("#btnCloseSendToReceivingEntity").click();
                        if (refresh)
                        {
                            $("#gridContainerDiv").show();
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                            if (!fromvip) {
                                GridCommon.Refresh("grdInboxItems");

                                if ($('li .active').attr("data-id") == undefined) {
                                    window.location.href = "/";
                                }
                                else {
                                    window.location.reload();
                                }
                            } else {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#inboxDocumentDetailsContainer").empty();
                                $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                                
                            }
                        }
                        if (typeof callback === 'function') {
                            callback();
                        }
                        return;
                    });
                }, 500);

            }
            else
            {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                Common.showScreenSuccessMsg(Resources.SentSuccessfully);

                if (isComplete)
                {
                    if (isDraft) {
                        var nodeId = $('[data-inherit="' + TreeNode.Draft + '"]').first().data("id");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        if (nodeId !== undefined && $.isNumeric(nodeId)) {
                            $(".close-correspondence").trigger("click");
                            $("[ref=btnCloseTransfer]").trigger("click");
                            if ($('li .active').attr("data-id") == undefined) {
                                window.location.href = "/";
                            }
                            else {
                                window.location.reload();
                            }

                            //window.location.href = '#draft/' + nodeId;
                        }

                        if (fromvip) {
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#draftDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.RefreshCurrentPage("grdDraftItems", false);
                        }

                    } else {
                       
                        if (fromvip) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.Refresh("grdInboxItems");
                        }
                    }

                }
                else
                {
                    var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                    if (!transferId)
                    {
                        TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        GridCommon.RefreshCurrentPage("grdDraftItems", true);

                        if (fromvip) {
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#draftDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.RefreshCurrentPage("grdDraftItems", true);
                        }

                    } else
                    {
                        TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                        if (fromvip) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#myRequestsDocumentDetailsContainer").empty();
                            $("#inboxDocumentDetailsContainer").empty();
                            if (!self.model.isSigned)
                                $($("input[data-id='" + self.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.Refresh("grdMyRequestsItems");
                        }
                    }
                    if (nodeId !== undefined && $.isNumeric(nodeId))
                    {
                        //window.location.href = '#myrequests/' + nodeId;
                    } else
                    {
                        //window.location.href = '/';
                    }
                }
                $("#btnCloseSendToReceivingEntity").click();

                if (typeof callback === 'function') {
                    callback();
                }
            }
        }
        else
        {
            if (data === "NotStructureSender")
            {
                setTimeout(function ()
                {
                    Common.alertMsg(Resources.NotStructureSender);
                }, 500);
            }
            else if (data === "CantGenerateReferenceNumber")
            {
                setTimeout(function ()
                {
                    Common.alertMsg(Resources.CantGenerateReferenceNumber);
                }, 500);
            }
            else if (data === "OriginalFileInUse")
            {
                let msg = Resources.OriginalFileInUse;
                if (isBroadcast)
                {
                    setTimeout(function ()
                    {
                        Common.alertConfirmation(msg, function ()
                        {
                            $("#btnCloseSendToReceivingEntity").click();
                        });
                    }, 500);
                } else
                {
                    setTimeout(function ()
                    {
                        Common.alertMsg(msg);
                    }, 500);
                }
            }
            else if (data === "FileInUse")
            {
                let msg = Resources.FileInUse;
                if (isBroadcast)
                {
                    setTimeout(function ()
                    {
                        Common.alertConfirmation(msg, function ()
                        {
                            $("#btnCloseSendToReceivingEntity").click();
                        });
                    }, 500);
                } else
                {
                    setTimeout(function ()
                    {
                        Common.alertMsg(msg);
                    }, 500);
                }
            }
            else if (data === "CheckAtleastOnePurposeCCed")
            {
                setTimeout(function ()
                {
                    Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                }, 500);

            }
            else if (data === "CorrespondenceNotCompleteNoOriginalMail")
            {
                let msg = Resources.CorrespondenceNotCompleteNoOriginalMail;
                setTimeout(function ()
                {
                    Common.alertConfirmation(msg, function ()
                    {
                        $("#btnCloseSendToReceivingEntity").click();
                    });
                }, 500);
            }
            else if (data === "TransferWasCompletedNotLastOpenTransfer")
            {
                let msg = Resources.TransferWasCompletedNotLastOpenTransfer;
                setTimeout(function ()
                {
                    Common.alertConfirmation(msg, function ()
                    {
                        $("#btnCloseSendToReceivingEntity").click();
                        $("#gridContainerDiv").show();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        
                        if (fromvip) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        }
                    });
                }, 500);
            }
            else if (data === "TransferWasCompletedNoOriginalMail")
            {
                let msg = Resources.TransferWasCompletedNoOriginalMail;
                setTimeout(function ()
                {
                    Common.alertConfirmation(msg, function ()
                    {
                        $("#btnCloseSendToReceivingEntity").click();
                        $("#gridContainerDiv").show();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        if (fromvip) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        }
                    });
                }, 500);
               
            }
            else if (data === "TransferIsOwnedByAnotherUser")
            {
                let msg = Resources.TransferIsOwnedByAnotherUser;
                setTimeout(function ()
                {
                    Common.alertConfirmation(msg, function ()
                    {
                        $("#btnCloseSendToReceivingEntity").click();
                        $("#gridContainerDiv").show();
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        if (fromvip) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        }
                    });
                }, 500);
            }
            else if (data === "TransferHasDifferentOwnerOrIsCarbonCopy")
            {
                let msg = Resources.TransferHasDifferentOwnerOrIsCarbonCopy;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data === "CheckoutFaild") {
                let msg = Resources.CheckoutFaild;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data === "SignFailed") {
                let msg = Resources.SignFailed;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data === "HasNoAccess") {
                let msg = Resources.HasNoAccess;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data === "CheckinFailed") {
                let msg = Resources.CheckinFailed;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data === "CannotExportUnsignedDocument") {
                let msg = Resources.CannotExportUnsignedDocument;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data === "HasLockedAttachmentsByUser") {
                let msg = Resources.HasLockedAttachmentsByUser;
                setTimeout(function () {
                    Common.alertMsg(msg);
                }, 500);
            }
            else if (data != "") {
                setTimeout(function () {
                    Common.alertMsg(data);
                }, 500);
            }
            else
            {
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                Common.showScreenSuccessMsg(Resources.SentSuccessfully);
                //$("button[id*='_btnComplete']").click();
                if (isComplete)
                {
                    if (isDraft) {
                        var nodeId = $('[data-inherit="' + TreeNode.Draft + '"]').first().data("id");
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        if (nodeId !== undefined && $.isNumeric(nodeId)) {
                            //window.location.href = '#draft/' + nodeId;
                        } else {
                            
                            if (fromvip) {
                                $(".withBorders-o").addClass("waitingBackground");
                                $("#draftDocumentDetailsContainer").empty();
                                $($("input[data-id='" + self.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                            } else {
                                GridCommon.RefreshCurrentPage("grdDraftItems", true);
                            }
                        }
                    } else {
                        
                        if (fromvip) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#inboxDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                           GridCommon.Refresh("grdInboxItems");
                        }
                    }
                }
                else {
                    var nodeId = $('[data-inherit="' + TreeNode.Sent + '"]').first().data("id");
                    if (!transferId)
                    {
                        TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);

                        if (fromvip) {
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#draftDocumentDetailsContainer").empty();
                            $($("input[data-id='" + self.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.RefreshCurrentPage("grdDraftItems", false);
                        }

                    } else
                    {
                        TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                        if (fromvip || (window.InboxMode == "InboxVIPView" || window.InboxMode === 'LocalVIPView')) {
                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                            $(".withBorders-o").addClass("waitingBackground");
                            $("#myRequestsDocumentDetailsContainer").empty();
                            $("#inboxDocumentDetailsContainer").empty();
                            if (!self.model.isSigned)
                                $($("input[data-id='" + transferId + "']").parents("li")[0]).fadeOut().remove();
                        } else {
                            GridCommon.Refresh("grdMyRequestsItems");
                        }

                    }
                    if (nodeId !== undefined && $.isNumeric(nodeId))
                    {
                        if (window.location.href.includes('sent')) {
                            window.location.reload();
                        }
                        //window.location.href = '#sent/' + nodeId;
                    } else
                    {
                        //window.location.href = '/';
                    }
                }
                if (typeof callback === 'function') {
                    callback();
                }
                $("button[id*='SignatureTemplatesClose']").click();
                $("#btnCloseSendToReceivingEntity").click(); 
                $("button[id*='_btnClose']").click();
            }
        }
        if (typeof callback2 === 'function') {
            callback2();
        }
        Common.unmask("body-mask");

    }, function () { gLocked = false; btn.button('reset'); btnClose.removeAttr('disabled'); btnCloseX.removeAttr('disabled'); Common.unmask("body-mask"); Common.showScreenErrorMsg(); GridCommon.Refresh("grdInboxItems"); }, false);
}

function CreateSendToSelect(self) {
    var headers = {};
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    var structureSendingRulesIds = [];

    if (!self.model.isStructureSender) {
        structureSendingRulesIds = self.model.structureIds;
    } else if (self.model.enableSendingRules) {
        structureSendingRulesIds = new CoreComponents.Lookup.SendingRules().get(Number(self.model.structureId), self.model.delegationId);
    }

    var url = window.IdentityUrl + '/api/GetUsersAndStructuresWithSearchAttributes';
    var selectComponent = $(self.refs['cmbCustomAttributeReceiver']);
    var selectComponentParent = $('#customAttributeSenderContainer');

    if (self.model.enableTransferToUsers) {
        if (self.model.enableSendingRules && structureSendingRulesIds.length === 0) {
            selectComponent.select2({
                allowClear: false,
                placeholder: "",
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                dropdownParent: selectComponentParent,
                width: "100%",
                data: []
            });
        }
        else {
            selectComponent.select2({
                minimumInputLength: 0,
                allowClear: false,
                placeholder: "",
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                dropdownParent: selectComponentParent,
                width: "100%",
                templateResult: function (option) {
                    var $option;
                    if (typeof option.id !== "undefined") {
                        $option = $(
                            '<div><i style="font-size:12px;" class="' + option.icon + '"></i> ' + option.text + '</div>'
                        );
                    } else {
                        $option = option;
                    }
                    return $option;
                },
                ajax: {
                    delay: 400,
                    url: url,
                    type: "POST",
                    dataType: 'json',
                    headers: typeof headers !== "undefined" ? headers : "",
                    data: function (term) {
                        return {
                            "text": term.term ? term.term : "", "ids": structureSendingRulesIds, "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr], "showOnlyActiveUsers": true
                        };
                    },
                    processResults: function (data) {
                        var listitemsMultiList = [];
                        $.each(data.users, function (key, val) {
                            var delegatedUser = typeof self.model.delegationId !== 'undefined' && self.model.delegationId !== null ? new CoreComponents.Lookup.Delegations().get(Number(self.model.delegationId)) : null;
                            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                            if (val.id !== Number(self.model.userId) && val.id !== delegatedUserId) {
                                var item = {};
                                item.id = "User_" + val.structureIds[0] + "_" + val.id;
                                var currentStructure = new CoreComponents.Lookup.Structure().getWithCaching(val.structureIds[0], window.language);
                                var structureName = currentStructure;
                                if (currentStructure.attributes != null && currentStructure.attributes.length > 0) {
                                    var attributeLang = $.grep(currentStructure.attributes, function (e) {
                                        return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                                    });
                                    if (attributeLang.length > 0) {
                                        structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                                    }
                                }
                                item.text = structureName + " / " + val.fullName;
                                item.icon = "fa fa-user-o";
                                item.isStructure = false;
                                item.dataId = val.id;
                                item.structureId = val.structureIds.length > 0 ? val.structureIds[0] : val.defaultStructureId;
                                item.structureIds = val.structureIds;
                                item.defaultStructureId = val.defaultStructureId;
                                listitemsMultiList.push(item);
                            }
                        });
                        $.each(data.structures, function (key, val) {
                            var item = {};
                            item.id = "Structure" + val.id;
                            var structureName = val.name;
                            if (val.attributes != null && val.attributes.length > 0) {
                                var attributeLang = $.grep(val.attributes, function (e) {
                                    return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                                });
                                if (attributeLang.length > 0) {
                                    structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                                }
                            }
                            item.text = structureName;
                            item.icon = "fa fa-building-o";
                            item.isStructure = true;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        });
                        return {
                            results: listitemsMultiList
                        };
                    }
                },
                sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
            })
        }
    }
    else {
        url = structureSendingRulesIds.length > 0 ? window.IdentityUrl + '/api/ListStructuresByIds' : window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
        selectComponent.select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: selectComponentParent,
            width: "100%",
            ajax: {
                delay: 400,
                url: url,
                type: "POST",
                dataType: 'json',
                headers: typeof headers !== "undefined" ? headers : "",
                data: function (term) {
                    return {
                        "text": term.term ? term.term : "", "ids": structureSendingRulesIds, "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data) {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0) {
                            var attributeLang = $.grep(val.attributes, function (e) {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0) {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = "Structure" + val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        })
    }
}


function Transfer(transferArray, delegationId, allCced, allCcedClose, callback = null) {
    Common.ajaxPostJSON('/Transfer/Transfer?delegationId=' + delegationId, JSON.stringify(transferArray), function (data) {
        if (data[0]) {
            if (data[0].message === "FileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.FileInUse);
                }, 500);
            } else if (data[0].message === "OriginalFileInUse") {
                setTimeout(function () {
                    Common.alertMsg(Resources.OriginalFileInUse);
                }, 500);
            } else if (!data[0].updated) {
                Common.showScreenErrorMsg();
            }
            else {
                if (!allCced && !allCcedClose) {
                    TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                }
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                Common.showScreenSuccessMsg(Resources.SentSuccessfully);
                if (allCced) {
                    transferComponent.clear();
                } else {
                    if (allCcedClose) {
                        var nodeId = $('[data-inherit="' + TreeNode.Draft + '"]').first().data("id");
                        if (nodeId !== undefined && $.isNumeric(nodeId)) {
                            $(".close-correspondence").trigger("click");
                            $("[ref=btnCloseTransfer]").trigger("click");

                            if ($('li .active').attr("data-id") == undefined) {
                                window.location.href = "/";
                            }
                            else {
                                window.location.reload();
                            }
                        } else {
                            $(".close-correspondence").trigger("click");
                            $("[ref=btnCloseTransfer]").trigger("click");

                            if ($('li .active').attr("data-id") == undefined) {
                                window.location.href = "/";
                            }
                            else {
                                window.location.reload();
                            }

                            //window.location.href = '/';
                        }
                    } else {
                        var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                        var redirectTo = '#sent/' + nodeId;

                        if (nodeId !== undefined && $.isNumeric(nodeId)) {
                            $(".close-correspondence").trigger("click");
                            $("[ref=btnCloseTransfer]").trigger("click");

                            if ($('li .active').attr("data-id") == undefined) {
                                window.location.href = "/";
                            }
                            else {
                                window.location.reload();
                            }

                            //window.location.href = redirectTo;
                        } else {
                            $(".close-correspondence").trigger("click");
                            $("[ref=btnCloseTransfer]").trigger("click");
                            if ($('li .active').attr("data-id") == undefined) {
                                window.location.href = "/";
                            }
                            else {
                                window.location.reload();
                            }

                            //window.location.href = '/';
                        }
                    }
                }
                if (typeof callback === 'function') {
                    callback();
                }
            }
            $("#btnCloseSendToReceivingEntity").click();
        }
        else {
            Common.showScreenErrorMsg();
        }

    },
        function () { gLocked = false;  Common.showScreenErrorMsg(); }, false);
}

class SendToReceivingEntityIndexView extends Intalio.View
{
    constructor(element, model, callback)
    {
        super(element, "sendtoreceivingentity", model);
        this.callback = callback;
    }

    render()
    {
       


        var self = this;
        if (self.model.purposeIdForSignature != 0 && self.model.purposeIdForSignature != undefined) {
            $('#btnSendToReceivingEntity').click();

        }
        if (typeof CKEDITOR.instances['txtAreaInstruction'] !== 'undefined')
        {
            CKEDITOR.instances['txtAreaInstruction'].destroy(true);
        }

        if (self.model.transferToType == TransferType.SignAndSend) {

            CreateSendToSelect(self);

            //$(self.refs['cmbCustomAttributeReceiver']).select2({
            //    minimumInputLength: 0,
            //    multiple: false,
            //    allowClear: false,
            //    placeholder: "",
            //    dir: window.language === "ar" ? "rtl" : "ltr",
            //    language: window.language,
            //    dropdownParent: $('#customAttributeSenderContainer'),
            //    width: "100%",
            //    data: structureDataForSelect2(new IdentityService().getUserStructures(window.language)),
            //    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
            //})
        }


        $('#cmbSendPurpose').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnCloseSendToReceivingEntity').focus();
                }
                else
                {
                    $('#sendDueDate').focus();
                }
            }
        });
        $('#btnCloseSendToReceivingEntity').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnSendToReceivingEntity').focus();
                }
                else
                {
                    $('#cmbSendPurpose').focus();
                }
            }
        });
        if (document.getElementById('txtAreaInstruction') !== null)
        {
            var textarea = document.getElementById('txtAreaInstruction');
            CKEDITOR.replace(textarea, {
                customConfig: "/lib/ckeditor/custom-config.js", language: window.languageVal, resize_enabled: false, wordcount: {
                    showWordCount: false,
                    showCharCount: true,
                    maxWordCount: 4,
                    maxCharCount: 220
                },
            });
        }
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#cmbSendPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            disabled: self.model.isBroadcast,
            dropdownParent: $('#sendPurposeContainer')
        });
        var sendDueDate = $('#sendDueDate').flatpickr({
            noCalendar: false,
            static: true,
            enableTime: false,
            dateFormat: 'd/m/Y',
            minDate: moment().format('DD/MM/YYYY'),
            maxDate: $("#customAttributeDueDate").val() != undefined ? $("#customAttributeDueDate").val() : self.model.customAttributeDueDate
        });
        $("#sendDueDate_img").click(function ()
        {
            sendDueDate.toggle();
        });
        $('#formSendPost').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
                $('#btnSendToReceivingEntity').trigger("click");
            }
        });
        $('#btnExportToReceivingEntity').on('click', function () {
            ;
            $('#btnSendToReceivingEntity').trigger("click");
        });
        $('#btnSendToReceivingEntity').on('click', function ()
        {

            var $form = $('#formSendPost');
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            if (isValid)
            {
                //if (self.model.purposeIdForSignature != 0) {
                //    sendPurposeId = self.model.purposeIdForSignature;
                //}
                //else
                //    sendPurposeId = $("#cmbSendPurpose").val();

                let sendPurposeId = $("#cmbSendPurpose").val();
                let sendDueDate = $("#sendDueDate").val();
                let id = self.model.documentId;
                let transferId = self.model.transferId;

                if (sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") == null)
                {
                    sessionStorage.setItem($("#hdUserId").val() + "loggedInStructure", $('#hdLoggedInStructureId').val());
                }
                let userStructureId = window.EnablePerStructure ? sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") : sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
                let sendDelegationId = $("#hdSendDeligationId").val();
                let instruction = $('#txtAreaInstruction').val();
                if ($(CKEDITOR.instances["txtAreaInstruction"].getData()).text().replace(/\r?\n|\r/gm, " ").replace(/\s\s+/g, " ").trim() !== "")
                {
                    instruction = CKEDITOR.instances["txtAreaInstruction"].getData();
                }
                let structureReceiverIds = $('.cmbCustomAttributeReceiver').val();
                if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || !structureReceiverIds)
                {
                    structureReceiverIds = [];
                    for (var i = 0; i < self.model.receivingEntities.length; i++)
                    {
                        structureReceiverIds.push(self.model.receivingEntities[i].id);
                    }
                }
                

                let params = {
                    'id': id,
                    'transferId': transferId === "" ? null : transferId,
                    'purposeId': sendPurposeId,
                    'dueDate': sendDueDate,
                    'instruction': instruction,
                    'structureId': userStructureId,
                    'delegationId': sendDelegationId,
                    'structureReceivers': structureReceiverIds,
                    'transferToType': self.model.transferToType,
                    'withSign': self.model.withSign,
                    'dueDate': sendDueDate,
                    'SignatureTemplateId': self.model.signatureTemplate,
                    'documentId': self.model.documentId === "" ? null : self.model.documentId,
                    'carbonCopy': self.model.carbonCopy,
                    'maintainTransfer': self.model.isSigned ? true : false,
                    'fromStructure': self.model.fromStructureInbox,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };
                let url = '/Document/Send';
                if (self.model.action == "Attribute.Export")
                    url = '/Document/Export';
               
                    
                
                let isComplete = false;
                let purposeIsEmpty = false;
                let isDraft = self.model.isDraft;
                let transferList = [];
                if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || self.model.transferToType == TransferType.ReplyToInitiator) {
                    url = '/Transfer/Reply';
                } else if (self.model.transferToType == TransferType.BroadcastComplete) {
                    if ($('#cmbSendPurpose').val() == null) {
                        Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                        purposeIsEmpty = true;
                    }
                    params.TransferIds = self.model.broadcastIds;
                    url = '/Transfer/BroadcastComplete';
                    isComplete = true;
                } else if (self.model.transferToType == TransferType.BroadcastSend) {
                    if ($('#cmbSendPurpose').val() == null) {
                        Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                        purposeIsEmpty = true;
                    }
                    url = '/Transfer/BroadcastSend';
                }

                else if (self.model.transferToType == TransferType.SignAndSend) {
                    var transferToObj = $(self.refs['cmbCustomAttributeReceiver']).select2('data');
                    if (typeof transferToObj !== typeof undefined && transferToObj.length > 0) {

                        transferToObj = transferToObj[0];
                        var selectedOption = $(transferToObj.element);
                        var isStructure = typeof transferToObj.isStructure === 'undefined' ? selectedOption.data('isStructure') : transferToObj.isStructure;
                        var toUserStructureId = typeof transferToObj.structureId === 'undefined' ? selectedOption.data('structureId') : transferToObj.structureId;
                        var toStructureId = isStructure ? parseInt(transferToObj.id.split("Structure")[1]) : parseInt(toUserStructureId);
                        var toUserId = isStructure ? null : parseInt(transferToObj.id.split("_")[2]);
                        var transferItem = {
                            documentId: self.model.documentId,
                            parentTransferId: self.model.transferId,
                            toStructureId : toStructureId,
                            toUserId : toUserId,
                            name : transferToObj.text,
                            dueDate : sendDueDate,
                            purposeId : sendPurposeId,
                            instruction : instruction,
                            cced: false,
                            fromStructureId: self.model.fromStructureId,
                            'fromStructure': self.model.fromStructureInbox,
                            }

                        transferList.push(transferItem);
                    }

                }
                if (!purposeIsEmpty)
                {
                    if (window.EnableConfirmationMessage === "True")
                    {
                        var messageSend = Resources.SendCorrespondenceConfirmation;
                        var receiversArray = [];
                        if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToInitiator)
                        {
                            receiversArray.push({ 'text': self.model.transferToUser });
                        } else if (self.model.transferToType == TransferType.ReplyToStructure)
                        {
                            receiversArray = self.model.receivingEntities;
                        }
                        else
                        {
                            receiversArray = $('.cmbCustomAttributeReceiver').select2("data");
                            if (!receiversArray)
                            {
                                receiversArray = self.model.receivingEntities;
                            }
                        }
                        for (var i = 0; i < receiversArray.length; i++)
                        {
                            messageSend += ' \n ○ ' + receiversArray[i].text;
                        }
                        Common.showConfirmMsg(messageSend, function ()
                        {
                            if (!gLocked)
                            {
                                gLocked = true;
                                try
                                {
                                    if (self.model.transferToType == TransferType.SignAndSend) {
                                        Transfer(transferList, self.model.delegationId, false, false);
                                    }
                                    else {
                                        sendToReceivingEntity(url, params, transferId, isComplete, isDraft, self.model.isBroadcast, self.model.fromVip, self.callback,self);
                                    }
                                } catch (e)
                                {
                                    gLocked = false;
                                    Common.unmask("body-mask");
                                }
                            }
                        });
                    } else
                    {
                        if (!gLocked)
                        {
                            gLocked = true;
                            try
                            {
                                if (self.model.transferToType == TransferType.SignAndSend) {
                                    Transfer(transferList, self.model.delegationId, false, false);
                                }
                                else {
                                    sendToReceivingEntity(url, params, transferId, isComplete, isDraft, self.model.isBroadcast, self.model.fromVip, self.callback, self);
                                }
                            } catch (e)
                            {
                                gLocked = false;
                                Common.unmask("body-mask");
                            }
                        }
                    }
                }


            }
            //sendToReceivingEntitySubmit();
        });
        $('#modalSendToReceivingEntity').modal('show');
        $("#modalSendToReceivingEntity").off("hidden.bs.modal");
        $('#modalSendToReceivingEntity').on('hidden.bs.modal', function ()
        {
            $('#formSendPost').parsley().reset();
            swal.close();
            $("#modalSendToReceivingEntity").remove();
        });
    }

    sendToReceivingEntitySubmit(callback = null) {
        var self = this;
        var $form = $('#formSendPost');
        $form.parsley().reset();
        var isValid = $form.parsley().validate();
        let sendPurposeId = 0;

        if (isValid) {
            if (self.model.purposeIdForSignature != 0) {
                sendPurposeId = self.model.purposeIdForSignature;
            }
            
            else
                sendPurposeId = $("#cmbSendPurpose").val();

            if (self.model.defaultPurposeForExport != undefined)
                sendPurposeId = self.model.defaultPurposeForExport;
            //let sendPurposeId = $("#cmbSendPurpose").val();
            let sendDueDate = $("#sendDueDate").val();
            let id = self.model.documentId;
            let transferId = self.model.transferId;

            if (sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") == null) {
                sessionStorage.setItem($("#hdUserId").val() + "loggedInStructure", $('#hdLoggedInStructureId').val());
            }
            let userStructureId = window.EnablePerStructure ? sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") : sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
            let sendDelegationId = $("#hdSendDeligationId").val();
            let instruction = $('#txtAreaInstruction').val();
            //if ($(CKEDITOR.instances["txtAreaInstruction"].getData()).text().replace(/\r?\n|\r/gm, " ").replace(/\s\s+/g, " ").trim() !== "") {
            //    instruction = CKEDITOR.instances["txtAreaInstruction"].getData();
            //}
            let structureReceiverIds = $('.cmbCustomAttributeReceiver').val();
            // if (!self.model.action == "Attribute.Export" || self.model.fromResend) {
                if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || !structureReceiverIds || self.model.fromResend) {
                    structureReceiverIds = [];
                    for (var i = 0; i < self.model.receivingEntities.length; i++) {
                        structureReceiverIds.push(self.model.receivingEntities[i].id);
                    }
            }
            let structureReceiverIdsAndCC = $('.cmbCustomAttributeReceiver').val();
            if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || !structureReceiverIdsAndCC || self.model.fromResend) {
                structureReceiverIdsAndCC = [];
                for (var i = 0; i < self.model.receivingEntitiesWithCC.length; i++) {
                    structureReceiverIdsAndCC.push({ 'id': self.model.receivingEntitiesWithCC[i].id, isCC: self.model.receivingEntitiesWithCC[i].isCC });
                }
            }
            let params = {};
            if (self.model.fromResend) {
                params = {
                    'incomingDocumentId': self.model.incomingDocumentId,
                    'model': {
                        'id': id,
                        'transferId': transferId === "" ? null : transferId,
                        'purposeId': sendPurposeId,
                        'dueDate': sendDueDate,
                        'instruction': instruction,
                        'structureId': userStructureId,
                        'delegationId': sendDelegationId,
                        'structureReceivers': structureReceiverIds,
                        'structureReceiversWithCC': structureReceiverIdsAndCC,
                        'transferToType': self.model.transferToType,
                        'withSign': self.model.withSign,
                        'SignatureTemplateId': self.model.signatureTemplate,
                        'documentId': self.model.documentId === "" ? null : self.model.documentId,
                        'copyOptionsModal': self.model.CopyOptionsModal,
                        'fromResend': self.model.fromResend,
                        'externalReferenceNumber': self.model.externalReferenceNumber,
                        'fromStructure': self.model.fromStructureInbox,
                        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    }
                };
            } else {
                params = {
                    'id': id,
                    'transferId': transferId === "" ? null : transferId,
                    'purposeId': sendPurposeId,
                    'dueDate': sendDueDate,
                    'instruction': instruction,
                    'structureId': userStructureId,
                    'delegationId': sendDelegationId,
                    'structureReceivers': structureReceiverIds,
                    'structureReceiversWithCC': structureReceiverIdsAndCC,
                    'transferToType': self.model.transferToType,
                    'withSign': self.model.withSign,
                    'SignatureTemplateId': self.model.signatureTemplate,
                    'documentId': self.model.documentId === "" ? null : self.model.documentId,
                    'copyOptionsModal': self.model.CopyOptionsModal,
                    'fromResend': self.model.fromResend,
                    'externalReferenceNumber': self.model.externalReferenceNumber,
                    'fromStructure': self.model.fromStructureInbox,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };
            }
            
            let url = '/Document/Send';
            if (self.model.fromResend) {
                url = '/Document/ResendRejectedDocument';
            }
            else if (self.model.action == "Attribute.Export")
                url = '/Document/Export';
            let isComplete = false;
            let purposeIsEmpty = false;
            let isDraft = self.model.isDraft;
            let transferList = [];
            if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToStructure || self.model.transferToType == TransferType.ReplyToInitiator) {
                url = '/Transfer/Reply';
            } else if (self.model.transferToType == TransferType.BroadcastComplete) {
                if ($('#cmbSendPurpose').val() == null) {
                    Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                    purposeIsEmpty = true;
                }
                params.TransferIds = self.model.broadcastIds;
                url = '/Transfer/BroadcastComplete';
                isComplete = true;
            } else if (self.model.transferToType == TransferType.BroadcastSend) {
                if ($('#cmbSendPurpose').val() == null) {
                    Common.alertMsg(Resources.CheckAtleastOnePurposeCCed);
                    purposeIsEmpty = true;
                }
                url = '/Transfer/BroadcastSend';
            }

            else if (self.model.transferToType == TransferType.SignAndSend) {
                var transferToObj = $(self.refs['cmbCustomAttributeReceiver']).select2('data');
                if (typeof transferToObj !== typeof undefined && transferToObj.length > 0) {

                    transferToObj = transferToObj[0];
                    var selectedOption = $(transferToObj.element);
                    var isStructure = typeof transferToObj.isStructure === 'undefined' ? selectedOption.data('isStructure') : transferToObj.isStructure;
                    var toUserStructureId = typeof transferToObj.structureId === 'undefined' ? selectedOption.data('structureId') : transferToObj.structureId;
                    var toStructureId = isStructure ? parseInt(transferToObj.id.split("Structure")[1]) : parseInt(toUserStructureId);
                    var toUserId = isStructure ? null : parseInt(transferToObj.id.split("_")[2]);
                    var transferItem = {
                        documentId: self.model.documentId,
                        parentTransferId: self.model.transferId,
                        toStructureId: toStructureId,
                        toUserId: toUserId,
                        name: transferToObj.text,
                        dueDate: sendDueDate,
                        purposeId: sendPurposeId,
                        instruction: instruction,
                        cced: false,
                        fromStructureId: self.model.fromStructureId,
                        'fromStructure': self.model.fromStructureInbox,
                    }

                    transferList.push(transferItem);
                }

            }
            if (!purposeIsEmpty) {
                if (window.EnableConfirmationMessage === "True") {
                    var messageSend = Resources.SendCorrespondenceConfirmation;
                    var receiversArray = [];
                    if (self.model.transferToType == TransferType.ReplyToUser || self.model.transferToType == TransferType.ReplyToInitiator) {
                        receiversArray.push({ 'text': self.model.transferToUser });
                    } else if (self.model.transferToType == TransferType.ReplyToStructure) {
                        receiversArray = self.model.receivingEntities;
                    }
                    else {
                        receiversArray = $('.cmbCustomAttributeReceiver').select2("data");
                        if (!receiversArray) {
                            receiversArray = self.model.receivingEntities;
                        }
                    }
                    for (var i = 0; i < receiversArray.length; i++) {
                        messageSend += ' \n ○ ' + receiversArray[i].text;
                    }
                    Common.showConfirmMsg(messageSend, function () {
                        if (!gLocked) {
                            gLocked = true;
                            try {
                                if (self.model.transferToType == TransferType.SignAndSend) {
                                    Transfer(transferList, self.model.delegationId, false, false, callback);
                                }
                                else {
                                    sendToReceivingEntity(url, params, transferId, isComplete, isDraft, self.model.isBroadcast, self.model.fromVip, self.callbackm,self, callback);
                                }
                            } catch (e) {
                                gLocked = false;
                                Common.unmask("body-mask");
                            }
                        }
                    });
                } else {
                    if (!gLocked) {
                        gLocked = true;
                        try {
                            if (self.model.transferToType == TransferType.SignAndSend) {
                                Transfer(transferList, self.model.delegationId, false, false, callback);
                            }
                            else {
                                sendToReceivingEntity(url, params, transferId, isComplete, isDraft, self.model.isBroadcast, self.model.fromVip, self.callback,self, callback);
                            }
                        } catch (e) {
                            gLocked = false;
                            Common.unmask("body-mask");
                        }
                    }
                }
            }


        }
    }
}
export default { SendToReceivingEntityIndex, SendToReceivingEntityIndexView };
