import Intalio from './common.js'
import VisualTracking from './visualTracking.js'
import NoteList from './noteList.js'
import NonArchivedAttachmentList from './nonArchivedAttachmentsList.js'
import LinkedCorrespondence from './linkedCorrespondenceList.js'
import Document from './document.js'
import Attachment from './attachment.js'
import ActivityLogTimeline from './activityLogTimeline.js'
import TransferHistory from './transferHistoryList.js'
import MyTransfer from './myTransfer.js'
import AssigneeList from './assigneeList.js'

import { Categories, IdentityService, CategoryModel, DelegationUsers } from './lookup.js'
class DocumentDetails extends Intalio.Model
{
    constructor()
    {
        super();
        this.id = null;
        this.status = null;
        this.statusId = null;
        this.createdByUser = null;
        this.referenceNumber = null;
        this.categoryName = null;
        this.documentModel = null;
        this.statuses = null;
        this.showMyTransfer = null;
        this.showAssignees = null;
        this.readonly = null;
        this.delegationId = null;
        this.fromSearch = false;
        this.tabs = [];
        this.tabsWithStatic = [];
        this.tabsActions = [];
        this.showBackButton = true;
        this.fromDraft = false;
        this.fromRejectedDocument = false;
        this.sentToUser = false;
        this.attachmentId = null;
        this.fromSent = false;
        this.fromInbox = false;
        this.isCced = false;
        this.fromInbox = false;
        this.showVisualTrackingOnly = false;
        this.byTemplate = false;
        this.MeetingAgenda = false;
        this.parentLinkedDocumentId = null;
        this.senderPerson = null;
        this.receiverPerson = null;
        this.isExternalReceiver = false;
        this.isExternalSender = false;
        this.hasReferenceNumber = false;
        this.isFollowUp = null;
        this.fullControl = null;
        this.isTaskCreator = null;
        this.modalComponentId = null;
        this.parentComponentId = null;
        this.isModal = false;
        this.followupId = null;
        this.fromFollowUp = false;
        this.resendData = null;
        this.attachmentIslocked = false;
        this.fromSearch = false;
        this.g2gInternalId = null;
        this.attachmentVersion = null;
    }
}
var gOwnerUserId;
function reloadViewerSrc(gSelf, model, viewerData) {
    Common.ajaxGet('/Attachment/GetCurrentVersionNumber?attachmentId=' + model.attachmentId, null,
        function (attachmentVersion) {
            openViewer(gSelf, model, attachmentVersion)

        }, function (err) {
            openViewer(gSelf, model, viewerData.version)
        });

}
function openViewer(gSelf,model,attachmentVersion) {
    var isCustomMode = window.ViewerMode == '1' ? false : true;

    var isDraft = model.id === null || typeof model.id === 'undefined' ? true : false;
    var viewerUrl = window.ViewerUrl + "/templates?documentId=" + model.attachmentId + "&language=" + window.language + "&token=" +
        window.IdentityAccessToken + "&version=autocheck&ctsDocumentId=" + model.documentId +
        "&ctsTransferId=" + model.id + "&delegationId=" + model.delegationId + "&isDraft=" + isDraft + "&isCustomMode=" + isCustomMode;

    if (window.viewOnlyMode == 'full' && attachmentVersion !== undefined && attachmentVersion !== null && attachmentVersion.length > 0) {
        viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + attachmentVersion)
    }
    if (model.readonly || model.attachmentIslocked || model.fromRejectedDocument) {
        viewerUrl = viewerUrl + "&viewermode=view";
        if (window.viewOnlyMode == 'read')
            viewerUrl = viewerUrl.replace('/templates', '/assets/viewOnly/viewOnly.html').replace('version=autocheck', 'version=' + attachmentVersion)
    }
    $("#" + gSelf.model.ComponentId + "_" + "viewerContainer").removeClass("waitingBackground");
    $("#" + gSelf.model.ComponentId + "_" + "viewerFrame").attr("src", viewerUrl);
}
function getTransferDetail(transferId, documentModel, gSelf)
{
   
   
    var params = { "id": transferId };
    if (documentModel.delegationId !== null)
    {
        params.delegationId = documentModel.delegationId;
    }
    if (gSelf.model.fromRejectedDocument) {
        params.fromRejectedDocument = gSelf.model.fromRejectedDocument;
    }
    Common.ajaxGet("/Transfer/GetTransferInfoById", params, function (data)
    {
        gLocked = false;
        var wrapper = $("#" + gSelf.model.ComponentId + '_myTransfer');
        var model = new MyTransfer.MyTransfer();
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        if (!documentModel.readonly)
        {
            documentModel.readonly = data.viewMode;
        }
        model.parentTransferId = (data.parentTransferId == null || data.parentTransferId == undefined) ? null : data.parentTransferId;
        model.isCCed = data.cced;
        model.VoiceNote = data.voiceNote
        model.readonly = documentModel.readonly;
        model.transferId = transferId;
        model.delegationId = documentModel.delegationId;
        model.sendingEntity = data.sendingEntity;
        model.receivingEntity = data.receivingEntity;
        model.subject = data.subject;
        model.fromStructure = data.fromStructure;
        model.fromUser = data.fromUser;
        model.toStructure = data.toStructure;
        model.toUser = data.toUser;
        model.purpose = data.purpose;
        model.createdDate = data.createdDate;
        model.dueDate = data.dueDate;
        model.openedDate = data.openedDate;
        model.closedDate = data.closedDate;
        model.instruction = data.instruction;
        model.privacyId = data.privacyId;
        model.fromStructureId = data.fromStructureId;
        model.documentId = gSelf.model.documentId;
        model.receivingEntityId = data.receivingEntityId;
        model.ownerUserId = data.ownerUserId;
        model.instruction = data.instruction;
        model.toStructureId = data.toStructureId;
        model.replyToEntity = [{ id: data.fromStructureId, text: data.fromStructure }];
        model.sentToUser = data.sentToUser;
        model.withViewer = window.OpenCorrespondenceMode === CorrespondenceMode.WithViewer;
        model.fromSent = documentModel.fromSent;
        model.parentComponentId = gSelf.model.ComponentId;
        model.categoryId = documentModel.categoryId;
        model.fromInbox = documentModel.fromInbox;
        model.DocumentIsCompleted = documentModel.DocumentIsCompleted;
        model.closedTransfer = data.closedDate ? true : false;
        model.byTemplate = data.byTemplate;
        model.forSignature = data.forSignature;
        model.workflowStepId = data.workflowStepId;
        model.initiatorUser = data.initiatorUser;
        model.isWorkflowReturned = data.isWorkflowReturned;
        model.MeetingAgenda = (documentModel.categoryId == window.MeetingAgendaId) ? true : false;
        model.hasReferenceNumber = data.hasReferenceNumber;
        model.showAssignees = true;
        model.fullControl = data.fullControl;
        model.statusId = data.statusId;
        model.isTaskCreator = data.isTaskCreator;
        model.isFollowUp = data.categoryId == window.FollowUpCategory;
        model.readonly = data.isFollowUp == true ? false : documentModel.readonly;
        model.hasAttachments = data.hasAttachments;
        model.hasUserCofigureSignature = data.hasUserCofigureSignature;
        model.nextStepUserName = data.nextStepUserName;
        model.allowSign = data.allowSign;
        model.isSigned = data.isSigned;
        var currentCategoryModel = new CategoryModel().findFullById(documentModel.categoryId);
        if (typeof currentCategoryModel !== 'undefined' && currentCategoryModel !== "" && currentCategoryModel !== null)
        {
            if (currentCategoryModel.basicAttribute !== "" && currentCategoryModel.basicAttribute !== null)
            {
                let basicAttributes = JSON.parse(currentCategoryModel.basicAttribute);
                if (basicAttributes.length > 0)
                {
                    let receivingEntityObj = $.grep(basicAttributes, function (e)
                    {
                        return e.Name === "ReceivingEntity";
                    });
                    if (receivingEntityObj[0].BroadcastReceivingEntity && receivingEntityObj[0].Type === "internal")
                    {
                        model.isInternalBroadcast = true;
                        model.isBroadcast = true;
                    } else if (receivingEntityObj[0].BroadcastReceivingEntity)
                    {
                        model.isBroadcast = true;
                    }
                }
            }
        }
        var myTransferView = new MyTransfer.MyTransferView(wrapper, model);
        myTransferView.render();
        let target = "#" + gSelf.model.ComponentId + '_myTransfer';
        let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
        SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="myTaskActions"]').attr('id'), true);
        SecurityMatrix.InitTabContextMenu(actions);
        gOwnerUserId = data.ownerUserId;
    }, null, null, null, false);
}
function getVisualTracking(delegationId, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_visualTracking');
    var model = new VisualTracking.VisualTracking();
    model.delegationId = delegationId;
    model.documentId = gSelf.model.documentId;
    var documentView = new VisualTracking.VisualTrackingView(wrapper, model);
    documentView.render();
    if (window.OpenCorrespondenceMode !== "OpenCorrespondenceDefault")
    {
        $("#" + model.ComponentId + "_trackingChart").css("height", "353px");
    }
    let target = "#" + gSelf.model.ComponentId + "_visualTracking";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);

}
function getNoteByTask(transferId, documentId, readonly, gSelf)
{
    gLocked = false;
    var model = new NoteList.Note();
    model.transferId = transferId;
    model.documentId = documentId;
    model.delegationId = gSelf.model.delegationId;
    model.readOnly = readonly;
    var wrapper = $("#" + gSelf.model.ComponentId + '_notes');
    var noteView = new NoteList.NoteView(wrapper, model);
    noteView.render();
    let target = "#" + gSelf.model.ComponentId + "_notes";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="noteToolbarContainer"]').attr('id'), true);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getNonArchivedAttachmentsByTask(transferId, documentId, readonly, gSelf)
{
    gLocked = false;
    var model = new NonArchivedAttachmentList.NonArchivedAttachments();
    model.transferId = transferId;
    model.documentId = documentId;
    model.delegationId = gSelf.model.delegationId;
    model.readOnly = readonly;
    var wrapper = $("#" + gSelf.model.ComponentId + '_nonArchivedAttachments');//todo
    var nonArchivedAttachmentList = new NonArchivedAttachmentList.NonArchivedAttachmentsView(wrapper, model);
    nonArchivedAttachmentList.render();
    let target = "#" + gSelf.model.ComponentId + "_nonArchivedAttachments";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="nonArchivedAttachmentsToolbarConatiner"]').attr('id'), true);//todo
    SecurityMatrix.InitTabContextMenu(actions);

}
function getLinkedCorrespondences(transferId, documentId, delegationId, readonly, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_linkedDocument');
    var model = new LinkedCorrespondence.LinkedCorrespondence();
    model.transferId = transferId;
    model.documentId = documentId;
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.categories = new Categories().get(window.language);
    model.delegationId = delegationId;
    model.readOnly = readonly;
    var documentView = new LinkedCorrespondence.LinkedCorrespondenceView(wrapper, model);
    documentView.render();
    let target = "#" + gSelf.model.ComponentId + "_linkedDocument";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="linkedDocumentToolbarContainer"]').attr('id'), true);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getAttachments(transferId, documentId, delegationId, readOnly, categoryId, documentModel, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_attachments');
    var model = new Attachment.Attachment();
    model.transferId = transferId;
    model.documentId = documentId;
    model.readOnly = readOnly;
    model.delegationId = delegationId;
    model.categoryId = categoryId;
    model.fromInbox = documentModel.fromInbox;
    model.fromDraft = documentModel.fromDraft;
    model.isCced = documentModel.isCced;
    model.ownerUserId = gOwnerUserId;
    model.parentLinkedDocumentId = gSelf.model.parentLinkedDocumentId;
    model.parentComponentId = gSelf.model.ComponentId;
    var attachmentView = new Attachment.AttachmentView(wrapper, model);
    attachmentView.render();
    let target = "#" + gSelf.model.ComponentId + '_attachments';
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="attachmentsActions"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getActivityLog(documentId, delegationId, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_activityLog');
    var model = new ActivityLogTimeline.ActivityLogTimeline();
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.documentId = documentId;
    model.delegationId = delegationId;
    var documentView = new ActivityLogTimeline.ActivityLogTimelineView(wrapper, model);
    documentView.render();
    let target = "#" + gSelf.model.ComponentId + "_activityLog";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getTransfersHistory(transferId, documentId, sentToUser, delegationId, gSelf)
{
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_transferHistory');
    var model = new TransferHistory.TransferHistory();
    model.transferId = transferId;
    model.documentId = documentId;
    model.delegationId = delegationId;
    model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    model.sentToUser = sentToUser;
    var TransferHistoryView = new TransferHistory.TransferHistoryView(wrapper, model);
    TransferHistoryView.render();
    let target = "#" + gSelf.model.ComponentId + '_transferHistory';
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function executeAjaxCall(div, url, openInIframe)
{
   
    if (!openInIframe)
    {
        Common.ajaxGet(url, null, function (response)
        {
            let wrapper = null;
            wrapper = document.getElementById(div);
            wrapper.innerHTML = "";
            wrapper.innerHTML = response;
        });
    } else
    {
        let wrapper = null;
        wrapper = document.getElementById(div + "Iframe");
        wrapper.src = url;
    }
}
function initTabActions(tabs, tabsActions, readonly, divId)
{
    let tab = $.grep(tabs, function (element)
    {
        return element.Name.includes(divId);
    })[0];
    if (tab !== null && typeof tab !== "undefined")
    {
        let actions = $.grep(tabsActions[Number(tab.Id)], function (element)
        {
            return readonly ? element.ShowInReadMode === true : element.ShowInEditMode === true;
        });
        return actions;
    }
    return [];
}
function getDocument(data, readonly, gSelf)
{
  
    gLocked = false;
    var wrapper = $("#" + gSelf.model.ComponentId + '_documentMetadata');
    var model = new Document.Document();
    model.id = gSelf.model.documentId;
    model.categoryId = data.categoryId;
    model.categoryName = data.categoryName;
    model.referenceNumber = data.referenceNumber;
    model.subject = data.subject;
    model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
    model.customAttributes = data.customAttributes !== null && data.customAttributes !== "" ? JSON.parse(data.customAttributes) : null;
    model.customAttributesTranslation = data.customAttributesTranslation !== null && data.customAttributesTranslation !== "" ? JSON.parse(data.customAttributesTranslation) : null;
    model.formData = data.formData !== null && data.formData !== "" ? JSON.parse(data.formData) : [];
    model.receivers = data.receivers;
    model.sendingEntityId = data.sendingEntityId;
    model.dueDate = data.dueDate;
    model.priorityId = data.priorityId;
    model.privacyId = data.privacyId;
    model.carbonCopy = data.carbonCopy;
    model.importanceId = data.importanceId;
    model.classificationId = data.classificationId;
    model.sendingEntity = data.sendingEntity;
    model.receivingEntities = data.receivingEntities;
    model.carbonCopies = data.carbonCopies;
    model.classification = data.classification;
    model.documentType = data.documentType;
    model.readonly = readonly && data.enableEdit ? !data.enableEdit : readonly;
    model.delegationId = data.delegationId;
    model.userStructures = new IdentityService().getUserStructures(window.language);
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
    model.importances = new CoreComponents.Lookup.Importances().get(window.language);
    model.createdByStructureId = data.createdByStructureId;
    model.body = data.body;
    model.externalReferenceNumber = data.externalReferenceNumber;
    model.keyword = data.keyword;
    model.enableEdit = data.enableEdit;
    model.transferId = gSelf.model.id;
    model.senderPerson = data.senderPerson;
    model.receiverPerson = data.receiverPerson;
    model.isExternalReceiver = data.isExternalReceiver;
    model.isExternalSender = data.isExternalSender;
    model.isFollowUp = data.categoryId == window.FollowUpCategory;
    model.documentDate = data.documentDate;
    model.byTemplate = data.byTemplates;


    var documentView = new Document.DocumentView(wrapper, model);
    setTimeout(function ()
    {
        $('#txtCustomAttributeSubject').focus();
    }, 500);
    let url = readonly ? '/Document/Edit' : '/Document/Save';
    documentView.render({
        url: url,
        params: {
            'CategoryId': data.categoryId,
            'CategoryName': data.categoryName,
            
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response)
        {
            if (readonly)
            {
                GridCommon.RefreshCurrentPage("grdInboxItems", false);
                if (self.model.fromVip) {
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    $(".withBorders-o").addClass("waitingBackground");
                    $("#inboxDocumentDetailsContainer").empty();
                    $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                }
            } else
            {
                GridCommon.RefreshCurrentPage("grdDraftItems", false);
                if (self.model.fromVip) {
                    $(".withBorders-o").addClass("waitingBackground");
                    $("#draftDocumentDetailsContainer").empty();
                    $($("input[data-id='" + self.model.documentId + "']").parents("li")[0]).fadeOut().remove();
                } 
            }
        }
    }, {
        url: '/Document/Send',
        params: {
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response)
        {
            TreeNode.addToNodeCount(TreeNode.MyRequests, 1, 1);
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            Common.alertMsg(Common.format(Resources.ReferenceNumberMsg, response), function ()
            {
                //var nodeId = $('[data-inherit="' + TreeNode.MyRequests + '"]').first().data("id");
                //if (nodeId !== undefined && $.isNumeric(nodeId))
                //{
                //    window.location.href = '#myrequests/' + nodeId;
                //} else
                //{
                //    window.location.href = '/';
                //}

                if (readonly) {
                    GridCommon.RefreshCurrentPage("grdInboxItems", false);
                    if (self.model.fromVip) {
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);

                        $(".withBorders-o").addClass("waitingBackground");
                        $("#inboxDocumentDetailsContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    }
                } else {
                    GridCommon.RefreshCurrentPage("grdDraftItems", false);
                    if (self.model.fromVip) {
                        $(".withBorders-o").addClass("waitingBackground");
                        $("#draftListContainer").empty();
                        $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
                    } 
                }
            });
        }
    });
    let target = "#" + gSelf.model.ComponentId + '_documentMetadata';
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="' + target.split("_")[2] + '"]').attr('id'), false);
    SecurityMatrix.InitTabContextMenu(actions);
}
function getDocumentByTransfer(transferId, delegationId, gSelf)
{
   
    var params = { id: transferId, isFollowUp: gSelf.model.isFollowUp };

    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentByTransferId', params, function (data)
    {
       
        if (window.location.hash.toLowerCase().includes("sent")) {
            data.enableEdit = false;
        }
        getDocument(data, true, gSelf);
    }, null, true);

}
function getAssigneeByTask(documentId, gSelf) {
    gLocked = false;
    var model = new AssigneeList.Assignee();
    model.documentId = documentId;
    var wrapper = $("#" + gSelf.model.ComponentId + '_assignees');
    var assigneeView = new AssigneeList.AssigneeView(wrapper, model);
    assigneeView.render();
    let target = "#" + gSelf.model.ComponentId + "_assignees";
    let actions = initTabActions(gSelf.model.tabsWithStatic, gSelf.model.tabsActions, gSelf.model.readonly, target.split("_")[2]);
    SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[gSelf.model.ComponentId]).find('div[id*="assigneeToolbarContainer"]').attr('id'), true);
    SecurityMatrix.InitTabContextMenu(actions);
}
function checkReloadTab(tabs, divId, customId)
{

    divId = divId.split("_")[2];
    let tab = $.grep(tabs, function (element)
    {
        return element.Name.includes(divId) || element.Id.toString() === customId;
    })[0];
    if (tab !== null && typeof tab !== "undefined")
    {
        return tab.Reload === true;
    }
    return false;
}
function loadCustomTab(model, documentId, transferId, id, basketId,target, typeId, jsfunction, url, openiframe, gSelf)
{

    if (transferId == "") {
        transferId = null;
    }
    if (typeof typeId !== "undefined" && typeId !== null)
    {
        switch (typeId.toString())
        {
            case TabType.Url:
                url = url.replace("$documentId", documentId).replace("$transferId", transferId);
                executeAjaxCall(target.replace("#", ""), url, openiframe);
                break;
            case TabType.JavascriptFunction:
              
                var customactions = $.grep(model.tabsActions[id], function (element)
                {
                    return !model.readonly ? element.ShowInEditMode !== false : element.ShowInReadMode !== false;
                });
             
                let actions = undefined;
                for (var i = 0; i < model.tabsActions[id].length; i++) {
                    if (i == 0) {
                        actions =  model.tabsActions[id][i].Name ;
                        
                    }
                    else
                    {
                         actions +=  "_" + model.tabsActions[id][i].Name  ;
                    }
                    
                }

                var currentTab = model.tabs.find(tab => tab.Id === id);
                var isLinkedTab = currentTab && currentTab.Name === Resources.LinkedCorrespondence;
                var isActivityLogTab = currentTab && currentTab.Name === Resources.ActivityLog;
               
                jsfunction = jsfunction
                    .replace("$documentId", /*((isLinkedTab || isActivityLogTab) && window.FollowUpCategory == model.categoryId) ?: model.originalDocumentId.toString() :*/ documentId)
                    .replace("$basketId", basketId)
                    .replace("$transferId", transferId)
                    .replace("$customactions", JSON.stringify(customactions))
                    .replace("$readOnly", model.readonly)
                    .replace("$delegationId", model.delegationId)
                    .replace("$tabId", id)               
                    .replace("$ComponentId", "'" + model.ComponentId + "'")
                    .replace("$categoryId", model.categoryId)
                    .replace("$categories", model.categories)
                    .replace("$fromSent", model.fromSent)
                    .replace("$fromInbox", model.fromInbox)
                    .replace("$fromFollowUp", model.fromFollowUp)
                    .replace("$isCced", model.isCced)
                    .replace("$fromDraft", model.fromDraft)
                    .replace("$fromRejectedDocument", model.fromRejectedDocument)
                    .replace("$DocumentIsCompleted", model.DocumentIsCompleted)
                    .replace("$sentToUser", model.sentToUser)
                    .replace("$parentLinkedDocumentId", model.parentLinkedDocumentId)
                    .replace("$actionName", actions != undefined ? `"${actions}"` : `""`)
                    .replace("$modalComponentId", "'" + model.parentComponentId + "'")
                    .replace("$followupId", "'" + model.followupId + "'")
                    .replace("$nodeId", "'" + model.nodeId + "'")
                    .replace("$teamId", model.teamId )
                    .replace("$fromManageCorrespondance", model.fromManageCorrespondance)
                    .replace("$resendModel", JSON.stringify(model.resendData))
                    .replace("$fromSearch", model.fromSearch)
                    .replace("$attachmentVersion", model.attachmentVersion!=null?"'"+ model.attachmentVersion +"'":null)

                 eval(jsfunction);
                break;
        }
        gLocked = false;
    }
}
function openBarcode(documentId, data, isCached, componentId) {
    var imgSrc = "";
    if (typeof data !== "undefined") {
        imgSrc = "data:image/png;base64," + data;
    } else {
        imgSrc = "";
    }
    if (!isCached && typeof data !== "undefined") {
        sessionStorage.setItem(documentId + "_BarcodeData", data);
    }
    $("#" + componentId + "_" + "imgBarcodePanel").attr('src', imgSrc);
}
function imageSourcetoPrint(source) {
    return "<html><head><script>function step1(){\n" +
        "setTimeout('step2()', 10);}\n" +
        "function step2(){window.print();window.close()}\n" +
        "</scri" + "pt></head><body onload='step1()'>\n" +
        "<img src='" + source + "' /></body></html>";
}

function viewDocumentBarcode(documentId, referenceNumber, delegationId, gSelf) {
    if (referenceNumber) {
        let param = {
            'documentId': documentId,
            'delegationId': delegationId
        };
        var barcodeData = sessionStorage.getItem(documentId + "_BarcodeData");
        if (barcodeData !== null) {
            openBarcode(documentId, barcodeData, true, gSelf.model.ComponentId);

        } else {
            Common.ajaxPost("Document/PreviewBarcodeByDocument", param, function (data) {
                if (data !== "") {
                    openBarcode(documentId, data, false, gSelf.model.ComponentId);
                    sessionStorage.setItem(documentId + "_BarcodeData", data);
                    
                }
            },function (error, object) { Common.showScreenErrorMsg(object.responseText);}, true);
        }
    }
}

const registerCheckInFinishedEvent = (function()
{
    let eventHandler = () => {};
    return function(model)
    {
        if(model.id == null)
        {
            return;
        }
        window.removeEventListener("message", eventHandler);
        Common.ajaxGet('/Transfer/GetTransferDetailsById', { id: model.id }, function (result)
        {
            eventHandler = function(event)
            {
                //Customization for VIEWER to be able to send us updates on checkin
                if (event?.data?.type != "CheckInFinished")
                {
                    return;
                }
                if(result.isSigned)
                {
                    return;
                }
                Common.ajaxGet('/Transfer/GetTransferDetailsById', { id: result.id }, function (result)
                {
                    if(!result.isSigned)
                    {
                        return;
                    }
            
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            
                    $("#" + model.parentComponentId + "_btnClose").click();
                });
            }
            window.addEventListener("message", eventHandler);
        });
    }
})();

var gLocked = false;
class DocumentDetailsView extends Intalio.ViewAppend
{
    constructor(element, model)
    {
        if (window.OpenCorrespondenceMode == "OpenCorrespondenceDefault" || window.FollowUpCategory == model.categoryId)
        {
            super(element, "documentdetails", model);
        }
        else
        {
            super(element, "documentdetailswithviewer", model);
        }
    }
    render()
    {
        
        var gSelf = this;
        var model = this.model;
        var transferId = model.id;
        var documentId = model.documentId;
        var basketId = model.basketId;
        gLocked = false;

        window.addEventListener("message", function (event) {
            if (event.data.type) {
                switch (event.data.type) {
                    case "reload-viewer-file":
                        reloadViewerSrc(gSelf, model, event.data);
                        break;
                }
            }
        });

        if (window.HideAuditTrail === "True")
        {
            $("a[href='#" + gSelf.model.ComponentId + "_activityLog']").parent().hide();
        }
        if (model.isModal)
        {
            $(gSelf.refs['documentDetailsContainerDiv']).find("h3").hide();
        }
        registerCheckInFinishedEvent(gSelf.model);
        $("#" + gSelf.model.ComponentId + "_" + "refNumberPanelHeader").html(this.model.referenceNumber);
        if (model.showBackButton) {
            var contentWrapperBtns = "<div class='btn-group pull-right toRemove' style='margin-top: -5px;'>";
            var flipHorizental = "";
            if (language.toLowerCase() === "ar") {
                flipHorizental = "fa-flip-horizontal";
            }
            contentWrapperBtns += "<button type='button' class='btn-back btn btn-warning btn-sm lightGreyBorder toRemove'><em class='fa fa-reply " + flipHorizental + " mr-sm'></em>" + Resources.Back + "</button>";
            /*contentWrapperBtns += '<div id="' + model.ComponentId + '_ActionsPanel" class="btn-group"> </div>';*/
            if (!model.fromDraft) {
                contentWrapperBtns += "<button type='button' class='btn-export btn btn-secondary btn-sm lightGreyBorder toRemove'><em class='fa fa-cloud-download mr-sm'></em>" + Resources.Export + "</button>";
            }
            contentWrapperBtns += "</div>";
            $(".content-wrapper").prepend(contentWrapperBtns);
        }

        if (model.statusId !== 0) {
            var status = new CoreComponents.Lookup.Statuses().findById(model.statusId, window.language);
            if (status) {
                model.DocumentIsCompleted = status.text == 'Completed' ? true : false;

                var statusDiv = "<span class='label mr-sm' style='font-size:13px;padding:6px;background-color:" +
                    (status.color !== null ? status.color : "#27c24c") + "'>" + status.text + "</span>";
                $(gSelf.refs['documentDetailsContainerDiv']).find(".content-heading").append(statusDiv);
            }
        }

        if (model.showVisualTrackingOnly) {
            getVisualTracking(model.delegationId, gSelf);
            var reloadTab = checkReloadTab(model.tabsWithStatic, gSelf.model.ComponentId + "_visualTracking");
            if (!reloadTab) {
                $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(6)').attr("data-loaded", true);
                $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(6) a').attr("data-customloaded", true);
            }
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(6)').addClass("active");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').addClass("hidden");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(2)').addClass("hidden");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(3)').addClass("hidden");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(4)').addClass("hidden");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(5)').addClass("hidden");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(7)').addClass("hidden");
            $("#" + gSelf.model.ComponentId + '_visualTracking').addClass("active in");
        }
        else if (!model.showMyTransfer)
        {
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1)').addClass("active");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').addClass("active in");
            //console.log($(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("id"));
            var documentId = $(gSelf.refs['hdDocumentId']).val();
            var transferId = $(gSelf.refs['hdId']).val();
            var id = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("id");
            var target = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').attr("href");//activated tab
            var typeId = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("typeid");    
            var jsfunction = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("function");
            var url = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("url");
            var openiframe = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("openiframe");
            $(target).addClass("active in");
          
            loadCustomTab(model, documentId, transferId, id, basketId, target, typeId, jsfunction, url, openiframe, gSelf);
            //getDocument(model.documentModel, model.readonly, gSelf);
            //var reloadTab = checkReloadTab(model.tabsWithStatic, gSelf.model.ComponentId + "_documentMetadata");
            //if (!reloadTab)
            //{
            //    $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').attr("data-loaded", true);
            //    $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1) a').attr("data-customloaded", true);
            //}
            //$(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').addClass("active");
            //$("#" + gSelf.model.ComponentId + '_documentMetadata').addClass("active in");
            //let actions = initTabActions(model.tabsWithStatic, model.tabsActions, model.readonly, "documentMetadata");
            //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[model.ComponentId]).find('div[id*="documentMetadata"]').attr('id'), false);
            //SecurityMatrix.InitTabContextMenu(actions);
        } else
        {
            //getTransferDetail(transferId, model, gSelf);
            //loadCustomTab(model, documentId, transferId, id, target, typeId, jsfunction, url, openiframe, gSelf);
            //var reloadTab = checkReloadTab(model.tabsWithStatic, gSelf.model.ComponentId + "_myTransfer");
            //if (!reloadTab)
            //{
            //    $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1) a').attr("data-customloaded", true);
            //    $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').attr("data-loaded", true);
            //}
            //$(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').addClass("active");
            //$("#" + gSelf.model.ComponentId + '_myTransfer').addClass("active in");
            //let actions = initTabActions(model.tabsWithStatic, model.tabsActions, model.readonly, "myTransfer");
            //SecurityMatrix.getTabActions(actions, "#" + $(gSelf.refs[model.ComponentId]).find('div[id*="myTaskActions"]').attr('id'), true);
            //SecurityMatrix.InitTabContextMenu(actions);





            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1)').addClass("active");
            $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li:nth-child(1)').addClass("active in");
            //console.log($(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("id"));
            var documentId = $(gSelf.refs['hdDocumentId']).val();
            var transferId = $(gSelf.refs['hdId']).val();
            var id = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("id");
            var target = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').attr("href");//activated tab
            var typeId = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("typeid");
            var jsfunction = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("function");
            var url = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("url");
            var openiframe = $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"]  li:nth-child(1) a').data("openiframe");
            $(target).addClass("active in");
          
            loadCustomTab(model, documentId, transferId, id, basketId, target, typeId, jsfunction, url, openiframe, gSelf);


        }

        $(gSelf.refs['tabDocumentDetails']).scrollingTabs({ enableRtlSupport: true });





        $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] li').click(function ()
        {
            var tab = $(this).find("a[role='tab']").attr("href");
            var reloadTab = false;
            {
                var tabId = $(this).find("a[role='tab']").attr("data-id");
                reloadTab = checkReloadTab(model.tabsWithStatic, tab.split("#")[1], tabId);
            }
            var loaded = $(this).data("loaded");
            if (!loaded)
            {
                var count = 1;
                if (model.showMyTransfer)
                {
                    count = 2;
                }
                if (!reloadTab)
                {
                    $(this).attr("data-loaded", true);
                    $(this).attr("data-customloaded", true);
                }
                if (!gLocked)
                {
                    gLocked = true;
                    try
                    {
                        if (model.fromDraft)
                        {
                            //if ($(this).is(':nth-child(' + (count + 1) + ')'))
                            //{
                            //    getAttachments(transferId, documentId, model.delegationId, model.readonly, model.categoryId, model, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 2) + ')'))
                            //{
                            //    getNoteByTask(transferId, documentId, model.readonly, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 3) + ')'))
                            //{
                            //    getLinkedCorrespondences(transferId, documentId, model.delegationId, model.readonly, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 4) + ')'))
                            //{
                            //    getNonArchivedAttachmentsByTask(transferId, documentId, model.readonly, gSelf);
                            //} else if ($(this).index() < 5)
                            //{
                            //    if (!model.showMyTransfer)
                            //    {
                            //        getDocument(model.documentModel, model.readonly, gSelf);
                            //    } else
                            //    {
                            //        getTransferDetail(transferId, model, gSelf);
                            //    }
                            //}
                        } else
                        {
                            //if ($(this).is(':nth-child(' + count + ')'))
                            //{
                            //    getDocumentByTransfer(transferId, model.delegationId, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 1) + ')'))
                            //{
                            //    getAttachments(transferId, documentId, model.delegationId, model.readonly, model.categoryId, model, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 2) + ')'))
                            //{
                            //    getNoteByTask(transferId, documentId, model.readonly, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 3) + ')'))
                            //{
                            //    getLinkedCorrespondences(transferId, documentId, model.delegationId, model.readonly, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 4) + ')'))
                            //{
                            //    getNonArchivedAttachmentsByTask(transferId, documentId, model.readonly, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 5) + ')'))
                            //{
                            //    getVisualTracking(model.delegationId, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 6) + ')'))
                            //{
                            //    getActivityLog(documentId, model.delegationId, gSelf);
                            //} else if ($(this).is(':nth-child(' + (count + 7) + ')'))
                            //{
                            //    getTransfersHistory(transferId, documentId, model.sentToUser, model.delegationId, gSelf);
                            //} else if ($(this).index() < 5)
                            //{
                            //    if (!model.showMyTransfer)
                            //    {
                            //        getDocument(model.documentModel, model.readonly, gSelf);
                            //    } else
                            //    {
                            //        getTransferDetail(transferId, model, gSelf);
                            //    }
                            //}
                        }
                    } catch (e)
                    {
                        gLocked = false;
                    }
                }
            }
            //if ($(this).hasClass("active"))
            //{
            //    var customLoaded = $(this).find("a[role='tab']").data("customloaded");
            //    if (!customLoaded)
            //    {
            //        var id = $(this).find("a[role='tab']").data("id");
            //        var target = $(this).find("a[role='tab']").attr("href");//activated tab
            //        var typeId = $(this).find("a[role='tab']").data("typeid");
            //        var jsfunction = $(this).find("a[role='tab']").data("function");
            //        var url = $(this).find("a[role='tab']").data("url");
            //        var openiframe = $(this).find("a[role='tab']").data("openiframe");
            //        loadCustomTab(model, $("#hdDocumentId").val(), $("#hdId").val(), id, target, typeId, jsfunction, url, openiframe);
            //    }
            //}
        });











        $(gSelf.refs[model.ComponentId]).find('ul[role="tablist"] a[data-toggle="tab"]').on('shown.bs.tab', function (e)
        {
            
            var loaded = $(e.target).data("customloaded");
            if (!loaded)
            {
                var tab = $(this).attr("href");
                var reloadTab = false;
                if (tab !== undefined)
                {
                    var tabId = $(this).attr("data-id");
                    reloadTab = checkReloadTab(model.tabsWithStatic, tab.split("#")[1], tabId);
                }
                if (!reloadTab)
                {
                    $(e.target).attr("data-customloaded", true);
                }
                
                var documentId = $(gSelf.refs['hdDocumentId']).val();
                var transferId = $(gSelf.refs['hdId']).val();
                var id = $(e.target).data("id");
                var target = $(e.target).attr("href");//activated tab
                var typeId = $(e.target).data("typeid");
                var jsfunction = $(e.target).data("function");
                var url = $(e.target).data("url");
                var openiframe = $(e.target).data("openiframe");
              
                loadCustomTab(model, documentId, transferId, id, basketId, target, typeId, jsfunction, url, openiframe, gSelf);
            }
            setTimeout(function ()
            {
                $(gSelf.refs['tabDocumentDetails']).scrollingTabs('refresh');
            }, 300);
        });

        setTimeout(() => {
            const $documentDetailsViewer = $(gSelf.refs["documentDetailsContainerDiv"]).find("#documentWithViewerContainer");

            if ($documentDetailsViewer.length > 0) {
                if ($documentDetailsViewer.next(".resize-handle--x").length === 0) {
                    $("<div>", {
                        class: "resize-handle--x",
                        "data-target": "#documentWithViewerContainer",
                    }).insertAfter($documentDetailsViewer);
                }
            }

            const resize = {
                active: false,
                startX: 0,
                startWidth: 0,
                target: null,
                maxWidth: 0,
            };

            document.body.addEventListener("mousedown", (e) => {
                if (!e.target.classList.contains("resize-handle--x") || e.button !== 0) return;

                e.preventDefault();
                e.stopPropagation();

                const handle = e.target;
                const parent = handle.parentElement;
                if (!parent) return;
                const targetSelector = handle.getAttribute("data-target");
                if (!targetSelector) return;

                const targetEl = parent.querySelector(targetSelector);
                if (!targetEl) return;

                resize.active = true;
                resize.startX = e.screenX;
                resize.startWidth = targetEl.offsetWidth;
                resize.target = targetEl;
                resize.maxWidth = parent.clientWidth - 10;

                document.body.classList.add("resizing");
            });


            let lastMove = 0;
            window.addEventListener("mousemove", function resizeMove(e){
                if (!resize.active) return;
                const now = Date.now();
                if (now - lastMove < 10) return;
                lastMove = now;
               
                const delta = window.language === "ar"
                    ? resize.startX - e.screenX  
                    : e.screenX - resize.startX;
                const newWidth = Math.min(resize.startWidth + delta, resize.maxWidth);
                resize.target.style.width = newWidth + "px";
            });

            window.addEventListener("mouseup", () => {
                if (!resize.active) return;
                resize.active = false;
                document.body.classList.remove("resizing");

                $("body").trigger("forcerefresh.scrtabs");

            });
        }, 300);


        

        if (window.OpenCorrespondenceMode == "OpenCorrespondenceWithViewer" && this.model.attachmentId != null)
        {
            openViewer(gSelf,model,model.attachmentVersion)
        }
        $(window).resize(function ()
        {
            setTimeout(function ()
            {
                $(gSelf.refs['tabDocumentDetails']).scrollingTabs('refresh');
            }, 500);
        });
       
        let param = {
            'documentId': documentId,
                
        };
        var barcodeData = sessionStorage.getItem(documentId + "_BarcodeData");
        if (barcodeData !== null) {
            openBarcode(documentId, barcodeData, true, gSelf.model.ComponentId);

        } else {
            if (model.referenceNumber != '' && model.referenceNumber != undefined) {
                $("#" + gSelf.model.ComponentId + "_" + "refNumberPanel").removeClass('hidden');
                Common.ajaxPost("Document/PreviewBarcodeByDocument", param, function (data) {
                    if (data !== "") {
                        openBarcode(documentId, data, false, gSelf.model.ComponentId);
                    }
                }, function (error, object) { Common.showScreenErrorMsg(object.responseText); }, true);
            }
            else {
                $("#" + gSelf.model.ComponentId + "_" + "refNumberPanel").addClass('hidden');
            }
               
        }

        $(gSelf.refs['btnDocumentBarcodePrint']).on('click', function () {
            if ($(gSelf.refs['imgPreviewDocumentBarcode']).prop('src') !== "") {
                let source = $(gSelf.refs['imgPreviewDocumentBarcode']).attr('src');
                if (!source || source.trim() === "") {
                    source = $('#imgPrintBarcode').attr('src');
                }
                if (source) {
                    let Pagelink = "about:blank";
                    var pwa = window.open(Pagelink, "_new");
                    pwa.document.open();
                    pwa.document.write(imageSourcetoPrint(source));
                    pwa.document.close();
                } else {
                    Common.alertMsg(Resources.MakeSureImageExists);
                }
            }
        });

        $('.barcodePanel').on('click', function () {
            let refNumber = $('[id*="_refNumberPanelHeader"]').text();
            if (refNumber) {
                let docId = model.documentId;
                viewDocumentBarcode(docId, refNumber, model.delegationId, gSelf);
            }
        });


        //  imgPreviewDocumentBarcode is Null????????
        //$(self.refs['imgPreviewDocumentBarcode']).click(function () {
        //    let refNumber = $('[id*="_refNumberPanelHeader"]').text();
        //    if (refNumber) {
        //        let docId = self.model.id;
        //            viewDocumentBarcode(docId, refNumber, model.delegationId);
        //    }
        //});

        
    };



    remove()
    {
        $(this.refs[this.model.ComponentId]).remove();
        const $existingHandle = $(this.refs["documentDetailsContainerDiv"]).find(".resize-handle--x");
        if ($existingHandle.length) $existingHandle.remove();

        document.body.classList.remove("resizing");

    };
}
export default { DocumentDetails, DocumentDetailsView };