using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Intalio.CTS.Core.Model
{
    public class G2GDocumentModel
    {
        public long DOC_ID { get; set; }
        public short DOC_CATEGORY { get; set; }
        public string? REFERENCE { get; set; }
        public short? PRIORITY { get; set; }
        public short? Id { get; set; }
        public string? SUBJECT { get; set; }
        public string? Form { get; set; }
        public string? DOC_RECIPIENT_ID { get; set; }
        public string? DOC_RECIPIENT { get; set; }
        public long? TSF_FROM_UserId { get; set; }
        public long? TSF_TO_UserId { get; set; }
        public long? DELEGATE_FROM { get; set; }
        public DateTime? TSF_DUE_DATE { get; set; }
        public DateTime? TSF_CLOSURE_DATE { get; set; }
        public DateTime? TSF_DATE { get; set; }
        public DateTime? TSF_LOCKDATE { get; set; }
        public string TSF_DESCRIPTION { get; set; }
        public short? TSF_PURPOSE_ID { get; set; }
        public long? DOC_REGISTERBY_STRUCTURE { get; set; }
        public short? DOC_STATUS_ID { get; set; }
        public DateTime? DOC_REGISTERDATE { get; set; }
        public string? DOC_SENDER_FULL_NAME { get; set; }
        public string? DOC_SENDER_FULL_NAME_AR { get; set; }
        public long? TSF_TO_STRUCTURE { get; set; }
        public long? TSF_ID { get; set; }
        public string? DOC_RECIPIENT_FULL_NAME_AR { get; set; }
        public string? TOUSER { get; set; }
        public long? TSF_FROM_STRUCTURE { get; set; }
        public string? TSF_FROM_STRUCT_STC_NAME { get; set; }
        public string? TSF_FROM_STRUCT_STC_NAME_AR { get; set; }
        public string? TSF_TO_STRUCT_STC_NAME { get; set; }
        public string? TSF_TO_STRUCT_STC_NAME_AR { get; set; }
        public DateTime? RegisteredDateFrom { get; set; }
        public string? Category { get; set; }
        public string? G2G_REF_NO { get; set; }
        public long? MainGctIdTo { get; set; }
        public long? MainGctIdRedirectTo { get; set; }
        public long? SubGctIdTo { get; set; }
        public long? SubGctIdRedirectTo { get; set; }
        public short? Level { get; set; }
        public long? ExportedByStcGctId { get; set; }
        public long? ExportedBy { get; set; }

         public long G2G_Internal_ID { get; set; }
        //public long DOC_SENDER { get; set; }
        //public string STATUS { get; set; }
        //public DateTime RECEIVEDATE { get; set; }
        //public string DOC_SUMMARY { get; set; }
        //public string DOC_FILPLANTABLE { get; set; }
        //public string DOCUMENTRECEPIENT { get; set; }
        //public short PRIVACYLEVEL { get; set; }
        //public string TAGS { get; set; }
        //public string DOC_TEXT8 { get; set; }
        //public string DOC_TEXT15 { get; set; }
        //public DateTime DOCUMENTDATE { get; set; }
        //public string MAILNUMBER { get; set; }
        //public string DOC_PRIORITY { get; set; }
        //public short DOC_TRANSFER_STATUS { get; set; }
        //public short? PriorityId { get; set; }
        //public long DOC_REGISTEREDBY { get; set; }
        //public bool duedate { get; set; }
        //public DateTime? DOC_DATE1 { get; set; }
        //public string Document_Status { get; set; }
        //public bool overdue { get; set; }
        //public string SENDER { get; set; }
        //public string DOC_REGISTERBY_STRUCTURE_FULLNAME { get; set; }
        //public string DOC_REGISTEREDBY_FULLNAME { get; set; }
        //public DateTime RegisteredDateTo { get; set; }
        //public string O_OCR_TEXT { get; set; }
        //public short? DOC_TYPE { get; set; }
        //public short? CLASSIFICATION { get; set; }
        //public string? DOC_SEQUENCE { get; set; }
        //public string DOC_NUMBER_OF_OPENED_TRANSFERS { get; set; }
        //public string REGISTRATION_NUMBER { get; set; }
        //public string ID_CUSTOM { get; set; }
        //public string NAME { get; set; }
        //public short PRIORITY_ID { get; set; }
        //public string REGISTERDATE { get; set; }
        //public string PrivacyState { get; set; }
        //public int AttachmentsCount { get; set; }
        //public string MainFromAr { get; set; }
        //public string MainFrom { get; set; }
        //public string SubFromAr { get; set; }
        //public string SubFrom { get; set; }
        //public string MainToAr { get; set; }
        //public string MainTo { get; set; }
        //public string SubToAr { get; set; }
        //public string SubTo { get; set; }
        //public long? TSF_TO { get; set; }
        //public string? G2G_SERIAL { get; set; }
        //public long G2G_MAIN_SITE_FROM_ID { get; set; }
        //public long G2G_SUB_SITE_FROM_ID { get; set; }
        //public long G2G_MAIN_SITE_TO_ID { get; set; }
        //public long G2G_SUB_SITE_TO_ID { get; set; }
        //public string G2G_Category { get; set; }
        //public string GStatus { get; set; }
        //public string GStatusAr { get; set; }
        //public bool? IsLocked { get; set; } = false;
        //public int MaxAttempt { get; set; }
        //public int G2GStatusID { get; set; }
        //public string FROMUSER { get; set; }
        //public string Correspondence { get; set; }
        //public string Correspondence_Name { get; set; }
        //public DateTime? SYSREGDATE { get; set; }
        //public string ORIGINAL_FROM_STRUCTURE { get; set; }
        //public string G2G_VSID { get; set; }
        public DateTime? TRANSFERDATE { get; set; }
    }
}