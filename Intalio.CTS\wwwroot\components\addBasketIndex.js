import Intalio from './common.js'
import { IdentityService } from './lookup.js'

class AddBasketIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.itemsNames = [];
    }
}
var itemsNames;
var gStructureIds = [];

function fillAddressBookContainer(mode, callback)
{
    let modalWrapper = $(".modal-window");
    let view = new CoreComponents.AddressBookComponent(null, false, true, true, mode, AddressBookSelectionMode.Multiple, null, callback, modalWrapper);
    view.render();
    $('.modalAddressBook').modal('show');
    $(".modalAddressBook").off("hidden.bs.modal");
    $(".modalAddressBook").off("shown.bs.modal");
    $('.modalAddressBook').on('hidden.bs.modal', function ()
    {
        $(".modalAddressBook").parent().remove();
        swal.close();
    });
}
function drawTable()
{
    let retTable = $('#selectedItemsTable').on('draw.dt',
        function ()
        {
            $('#selectedItemsTable tbody tr td').each(function ()
            {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            "bInfo": false,
            "bPaginate": true,
            "targets": 'no-sort',
            "bSort": false,
            columns: [
                {
                    title: '<input id="chkAllItems" type="checkbox" />',
                    className: "sorting_disabled width10",
                    'orderable': false,
                    'sortable': false,
                    render: function (data, type, row)
                    {
                        return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.nameId + " />";
                    }
                },
                { title: "Id", data: "nameId", visible: false, "orderable": false },
                {
                    title: Resources.Name, 'orderable': false, width: '150px', render: function (data, type, row)
                    {
                        if (window.language === "ar")
                        {
                            return '<div><bdo dir="ltr">' + row.name + '</bdo></div >';
                        } else
                        {
                            return row.name;
                        }
                    }
                },
                { title: "Id", data: "roleId", visible: false, "orderable": false },
                {
                    title: Resources.Role, 'orderable': false, width: '150px', render: function (data, type, row)
                    {
                        if (window.language === "ar")
                        {
                            return '<div><bdo dir="ltr">' + row.role + '</bdo></div >';
                        } else
                        {
                            return row.role;
                        }
                    }
                },
                { title: "Id", data: "privacyId", visible: false, "orderable": false },
                {
                    title: Resources.Privacy, 'orderable': false, width: '150px', render: function (data, type, row)
                    {
                        if (window.language === "ar")
                        {
                            return '<div><bdo dir="ltr">' + row.privacy + '</bdo></div >';
                        } else
                        {
                            return row.privacy;
                        }
                    }
                },
                {
                    data: "",
                    className: "dt-center width10",
                    'orderable': false,
                    render: function (data, type, row)
                    {
                        var btn = document.createElement('button');
                        btn.setAttribute('class', 'btn btn-xs btn-danger js-delete');
                        btn.setAttribute('id', 'delete-' + row.rowId);
                        btn.setAttribute("type", "button");
                        btn.setAttribute('clickattr', 'deleteSelectedItem(' + row.nameId + ')');
                        btn.innerHTML = "<i class='fa fa-trash-o'/>";
                        return btn.outerHTML;
                    }
                }
            ],
            data: itemsNames,
            dom: 'trpi'
        });
}
function deleteSelectedItem(rowdata)
{
    itemsNames = $("#selectedItemsTable").DataTable().rows().data().toArray();
    var removedItem = itemsNames.map(function (i) { return i.nameId; }).indexOf(rowdata.toString());
    if (removedItem < 0)
    {
        removedItem = itemsNames.map(function (i) { return i.nameId; }).indexOf(rowdata);
    }
    if (removedItem >= 0)
    {
        itemsNames.splice(removedItem, 1);
        $("#selectedItemsTable").DataTable().clear().draw();
        $("#selectedItemsTable").DataTable().rows.add(itemsNames).draw();
        if ($("#selectedItemsTable").DataTable().rows().count() === 0)
        {
            $('#radioExternal').removeAttr('disabled');
            $('#radioExternal').removeAttr('style');
            $('#radioInternal').removeAttr('disabled');
            $('#radioInternal').removeAttr('style');
        }
    }

}
function getStructureName(structureId)
{
    var structure = new IdentityService().getFullStructure(structureId);
    var structureName = "";
    if (structure)
    {
        structureName = structure.name;
        if (structure.attributes != null && structure.attributes.length > 0)
        {
            var attributeLang = $.grep(structure.attributes, function (e)
            {
                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
            });
            if (attributeLang.length > 0)
            {
                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
            }
        }
    }
    return structureName;
}
var SelectedUserIds = [];
class AddBasketIndexView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "addBasketindex", model);
    }
    render()
    {
        var self = this;
        itemsNames = self.model.itemsNames;
        $.fn.select2.defaults.set("theme", "bootstrap");
        $('#btnCloseBasket').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnSubmitBasket').focus();
                }
                else
                {
                    $('#txtName').focus();
                }
            }
        });
        $('#txtName').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnCloseBasket').focus();
                }
                else
                {
                    $('#txtNameAr').focus();
                }
            }
        });
        $('#txtNameAr').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#txtName').focus();
                }
                else
                {
                    $('#txtNameFr').focus();
                }
            }
        });
        $('#txtNameFr').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#txtNameAr').focus();
                }
                else
                {
                    $('#radioInternal').focus();
                }
            }
        });
        
        $('#formBasketPost').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13)
            {
                e.preventDefault();
                $('#btnSubmitBasket').trigger("click");
            }
        });
        Common.gridCommon();
            let table = drawTable();
        $('#selectedItemsTable tbody').on('click', 'tr', function ()
        {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });
        $("#cmbIndexBasket").val('').trigger('change');

        $('#cmbIndexBasketRole').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            minimumInputLength: -1,
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#indexBasketRoleContainer')
        }).on('change', function () {
            $(this).trigger('input');
        });
        $(self.refs['cmbPrivacyIndex']).select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $(self.refs['privacyIndexContainer'])
        }).on('change', function () {
            $(this).trigger('input');
        });
        var lstPrivacies = new CoreComponents.Lookup.Privacies().get(window.language);
        $(self.refs["cmbPrivacyIndex"]).val(lstPrivacies[0].id).select2();
        $("#cmbIndexBasket").select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            multiple: "multiple",
            dropdownParent: $("#indexBasketContainer"),
            ajax: {
                type: 'GET',
                url: /*window.IdentityUrl + '/api/SearchUsers'*/'/User/GetUsersFromCTS',
                //headers: {
                //    "Authorization": 'Bearer ' + window.IdentityAccessToken
                //},
                dataType: 'json',
                delay: 400,
                data: function (params) {
                    //var query = {
                    //    'ids': gStructureIds,
                    //    'text': params.term || "",
                    //    'language': "",
                    //    'showOnlyActiveUsers': true
                    //};
                    //return query;
                    return { "userName": params.term ? params.term : "", delegationId: null };
                },
                processResults: function (data) {
                    var tableIds = [];
                    var userid = parseInt($("#hdUserId").val());
                    var DataFilteration = [];

                    if (itemsNames.length >= 0) {
                        tableIds.push(userid);
                        itemsNames = $("#selectedItemsTable").DataTable().rows().data().toArray();
                        for (var i = 0; i < itemsNames.length; i++) {
                            tableIds.push(parseInt(itemsNames[i].UserId))
                        }
                        if (data!= undefined) {
                            for (var i = 0; i < data.length; i++) {
                                if (!tableIds.includes(data[i].id)) {
                                    DataFilteration.push(data[i]);
                                }
                            }
                        }
                    }
                    else {
                        DataFilteration = data;
                        var index = numbers.indexOf(userid);
                        DataFilteration.splice(index, 1);
                    }
                    var listitemsMultiList = [];
                    var ids = [];
                    $.each(DataFilteration, function (key, val) {
                        var item = {};
                        item.id = val.id;
                        item.text = (val.firstname + ' ' + val.lastname);
                        item.firstName = val.firstname;
                        item.lastName = val.lastname;
                        item.roleId = val.roleId;
                        listitemsMultiList.push(item);

                        ids.push(val.id);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            }
        }).on('change', function () {
            $(this).trigger('input');
        });
        $('#selectedItemsTable tbody').on('click', ".btn", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#chkAllItems').change(function () {
            let isChecked = $(this).prop('checked');
            let table = $('#selectedItemsTable').DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]:not(#chkAllItems)', pageNodes).prop('checked', isChecked);
        });

        $('#selectedItemsTable').on('change', 'input[type="checkbox"]:not(#chkAllItems)', function () {
            setTimeout(() => {
                let table = $('#selectedItemsTable').DataTable();
                let pageNodes = table.rows({ page: 'current' }).nodes();
                let totalCheckboxes = $('input[type="checkbox"]:not(#chkAllItems)', pageNodes).length;
                let checkedCheckboxes = $('input[type="checkbox"]:not(#chkAllItems):checked', pageNodes).length;

                $('#chkAllItems').prop('checked', totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes);
            }, 0);
        });


        $("#selectedItemsTable").DataTable().on('draw', function () {
            $('#chkAllItems').prop('checked', false);

            let pageNodes = $("#selectedItemsTable").DataTable().rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);
        });
        $("#btnAdd").on("click", function ()
        {
            if (itemsNames.length === 0)
            {
                itemsNames = $("#selectedItemsTable").DataTable().rows().data().toArray();
            }
            var lan = "ltr";
            if (window.language=="ar") {
                lan = "rtl";
            }
            var lstPrivacies = new CoreComponents.Lookup.Privacies().get(window.language);
            var rolename = "";
            var $form = $('#formBasketPost');
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            var selectElement = document.getElementById('cmbIndexBasket');
            var role = document.getElementById('cmbIndexBasketRole').selectedOptions;
            var privacy = $(self.refs['cmbPrivacyIndex']);
            var privacyId = privacy[0].value;
            var privacyName = '';
            for (var i = 0; i < lstPrivacies.length; i++) {
                if (Number(privacyId) == lstPrivacies[i].id) {
                    privacyName = lstPrivacies[i].text;
                    break;
                }
            }
            if (isValid) {
                for (var i = 0; i < selectElement.options.length; i++) {
                    if (selectElement.options[i].selected) {
                        if (role[0].value=="1") {
                            rolename = "ReadOnly";
                        }
                        else if (role[0].value == "3")
                        {
                            rolename = "Administrator";
                        }
                        else {
                            rolename = "Edit";
                        }
                        itemsNames.push({
                            'UserId': selectElement.options[i].value,
                            'name': '<span dir="' + lan +'">' + selectElement.options[i].text + '</span>',
                            'nameId': selectElement.options[i].value,
                            'role': '<span dir="' + lan +'">' + Resources[rolename] + '</span>',
                            'roleId': role[0].value, 'isUser': false,
                            'privacy':  '<span dir="' + lan +'">' + privacyName + '</span>' ,
                            'privacyId': privacyId
                        });
                    }
                }
                $("#selectedItemsTable").DataTable().clear().draw();
                $("#selectedItemsTable").DataTable().rows.add(itemsNames).draw();
                $("#cmbIndexBasket").val('').trigger('change');

            }
           
        });
        $("#btnDelete").on("click", function ()
        {
            var checkedRows = $('#selectedItemsTable tbody').find('input[type="checkbox"]:checked');
            if (checkedRows.length > 0)
            {
                Common.showConfirmMsg(Resources.DeleteConfirmation, function ()
                {
                    itemsNames = $("#selectedItemsTable").DataTable().rows().data().toArray();
                    for (var j = 0; j < checkedRows.length; j++)
                    {
                        var removedItem = itemsNames.map(function (i) { return i.nameId; }).indexOf(parseInt(checkedRows[j].dataset.id));
                        itemsNames.splice(removedItem, 1);
                    }
                    $("#selectedItemsTable").DataTable().clear().draw();
                    $("#selectedItemsTable").DataTable().rows.add(itemsNames).draw();
                    if ($("#selectedItemsTable").DataTable().rows().count() === 0)
                    {
                        $('#radioExternal').removeAttr('disabled');
                        $('#radioExternal').removeAttr('style');
                        $('#radioInternal').removeAttr('disabled');
                        $('#radioInternal').removeAttr('style');
                    }
                });
            }
            else
            {
                Common.alertMsg(Resources.NoRowSelected);
            }
        });
        $('#btnSubmitBasket').on('click', function ()
        {
            var edit = false;
            var $form = $('#formBasketPost');
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            if (isValid) {
                    let params = {
                        'Id': $('#hdId').val(),
                        'Name': $('#txtName').val(),
                        'NameAr': $('#txtNameAr').val(),
                        'NameFr': $('#txtNameFr').val(),
                        'BasketUsersModel': itemsNames,
                        'Type': $("#radioInternal").is(":checked") ? Type.Internal : Type.External,
                        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };
                var Basketname = $('#txtName').val();
                if (window.language == 'ar') {
                    Basketname = $('#txtNameAr').val();
                } else if (window.language == 'fr') {
                    Basketname = $('#txtNameFr').val();
                }

                    if (document.getElementById('hdId').value) {
                        params.Id = document.getElementById('hdId').value;
                        edit = true;
                    }
                var btn = $('#btnSubmitBasket');
                    btn.button('loading');
                var btnClose = $('#btnCloseBasket');
                    var btnCloseX = $('#BasketClose');
                    btnClose.attr('disabled', 'disabled');
                    btnCloseX.attr('disabled', 'disabled');
                Common.ajaxPost('/Basket/Index', params, function (data) {
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        if (data.message !== null && data.message !== "") {
                            setTimeout(function () {
                                Common.alertMsg(data.message);
                            }, 300);
                        } else {
                            Common.showScreenSuccessMsg();
                            if (edit) {
                                let BasketCahed = window.userMenus.baskets.find(x => x.id == params.Id)
                                BasketCahed.name = params.Name;
                                BasketCahed.nameAr = params.NameAr;
                                BasketCahed.nameFr = params.NameFr;
                            } else {
                                window.userMenus.baskets.push({
                                    'basketDocuments': null,
                                    'basketPermissions': null,
                                    'createdByUser': null,
                                    'createdByUserId': $('#hdUserId').val(),
                                    'id': data.id,
                                    'name': params.Name,
                                    'nameAr': params.NameAr,
                                    'nameFr': params.NameFr
                                });
                            }
                            window.location.href = window.location.origin + "/#basket/" + data.id;
                            $('#BasketName').text(Basketname);
                            var liId = "#liBasket" + data.id;
                            var liElement = $(liId);
                            if (liElement.length) {
                                var spanElement = liElement.find('span');
                                spanElement.text(Basketname);
                            }
                            else {
                                $('#MyBaskets').removeClass('hidden');
                                var liHtml = '<li id="liBasket' + data.id + '"><a href="#basket/' + data.id + '" title="' + Basketname + '"><span style="white-space: normal; word-break: break-word;">' + Basketname + '</span></a></li>';
                                $('#nav-MyBaskets').append(liHtml);
                            }

                            $("#grdItems").DataTable().ajax.reload();
                            $('.modalAddressBook').modal('hide');
                            $('#modalBasket').modal('hide');
                        }
                    },
                        function () {
                            btn.button('reset');
                            btnClose.removeAttr('disabled');
                            btnCloseX.removeAttr('disabled');
                            Common.alertMsg(Resources.BasketNameAlreadyExist); // Add this line to display the error alert
                        }, false);
            }

        });
    };
    setData(data)
    {
        itemsNames = data;
    }
}
window.AddBasketIndex = AddBasketIndex;
window.AddBasketIndexView = AddBasketIndexView;
export default { AddBasketIndex, AddBasketIndexView };
