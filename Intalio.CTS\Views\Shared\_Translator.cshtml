@inject Intalio.Core.Translation Localizer;
@using Intalio.Core;
if (typeof Resources !== 'object') {
 var Resources = (function (R)
    {
        var R = {};
         return R;
        })(Resources);
}
        Resources.NoData ='@Html.Raw(Localizer["NoData"].Encode())';
        Resources.Arabic ='@Html.Raw(Localizer["Arabic"].Encode())';
        Resources.French ='@Html.Raw(Localizer["French"].Encode())';
        Resources.MachineName ='@Html.Raw(Localizer["MachineName"].Encode())';
        Resources.ExceptionLogs ='@Html.Raw(Localizer["ExceptionLogs"].Encode())';
        Resources.Parameters = '@Html.Raw(Localizer["Parameters"].Encode())';
        Resources.EmailSettings = '@Html.Raw(Localizer["EmailSettings"].Encode())';
        Resources.SmtpServer = '@Html.Raw(Localizer["SmtpServer"].Encode())';
        Resources.SmtpPort = '@Html.Raw(Localizer["SmtpPort"].Encode())';
        Resources.Username = '@Html.Raw(Localizer["Username"].Encode())';
        Resources.Password = '@Html.Raw(Localizer["Password"].Encode())';
        Resources.SmtpSystemEmail = '@Html.Raw(Localizer["SmtpSystemEmail"].Encode())';
        Resources.EnableSSL = '@Html.Raw(Localizer["EnableSSL"].Encode())';
        Resources.Validate = '@Html.Raw(Localizer["Validate"].Encode())';
        Resources.EnableCaching = '@Html.Raw(Localizer["EnableCaching"].Encode())';
        Resources.EnableEmailNotification = '@Html.Raw(Localizer["EnableEmailNotification"].Encode())';
        Resources.EnableConfirmationMessage = '@Html.Raw(Localizer["EnableConfirmationMessage"].Encode())';
        Resources.CounterNumberOfZeroes = '@Html.Raw(Localizer["CounterNumberOfZeroes"].Encode())';
        Resources.CustomJavascript = '@Html.Raw(Localizer["CustomJavascript"].Encode())';
        Resources.CustomCSS = '@Html.Raw(Localizer["CustomCSS"].Encode())';
        Resources.Valid = '@Html.Raw(Localizer["Valid"].Encode())';
        Resources.JavascriptFileType = '@Html.Raw(Localizer["JavascriptFileType"].Encode())';
        Resources.CSSFileType = '@Html.Raw(Localizer["CSSFileType"].Encode())';
        Resources.Others = '@Html.Raw(Localizer["Others"].Encode())';
        Resources.ScanConfiguration = '@Html.Raw(Localizer["ScanConfiguration"].Encode())';
        Resources.CategoryName = '@Html.Raw(Localizer["CategoryName"].Encode())';
        Resources.Resolution = '@Html.Raw(Localizer["Resolution"].Encode())';
        Resources.ThumbnailPerRow = '@Html.Raw(Localizer["ThumbnailPerRow"].Encode())';
        Resources.Preview = '@Html.Raw(Localizer["Preview"].Encode())';
        Resources.Scan = '@Html.Raw(Localizer["Scan"].Encode())';
        Resources.Load = '@Html.Raw(Localizer["Load"].Encode())';
        Resources.Scanners = '@Html.Raw(Localizer["Scanners"].Encode())';
        Resources.AllCategories = '@Html.Raw(Localizer["AllCategories"].Encode())';
        Resources.AllowedResolutions = '@Html.Raw(Localizer["AllowedResolutions"].Encode())';
        Resources.AllowedColors = '@Html.Raw(Localizer["AllowedColors"].Encode())';
        Resources.DefaultResolution = '@Html.Raw(Localizer["DefaultResolution"].Encode())';
        Resources.DefaultColor = '@Html.Raw(Localizer["DefaultColor"].Encode())';
        Resources.SearchAssignedStructures ='@Html.Raw(Localizer["SearchAssignedStructures"].Encode())';
        Resources.EnableSendingRules ='@Html.Raw(Localizer["EnableSendingRules"].Encode())';
        Resources.ManageTemplates ='@Html.Raw(Localizer["ManageTemplates"].Encode())';
        Resources.GlobalTemplate ='@Html.Raw(Localizer["GlobalTemplate"].Encode())';
        Resources.Template ='@Html.Raw(Localizer["Template"].Encode())';
        Resources.ViewFile ='@Html.Raw(Localizer["ViewFile"].Encode())';
        Resources.Upload ='@Html.Raw(Localizer["Upload"].Encode())';
        Resources.DocExtensionOnly ='@Html.Raw(Localizer["DocExtensionOnly"].Encode())';
        Resources.File ='@Html.Raw(Localizer["File"].Encode())';
        Resources.SelectData ='@Html.Raw(Localizer["SelectData"].Encode())';
        Resources.CounterExists ='@Html.Raw(Localizer["CounterExists"].Encode())';
        Resources.Previous ='@Html.Raw(Localizer["Previous"].Encode())';
        Resources.Content ='@Html.Raw(Localizer["Content"].Encode())';
        Resources.ReferenceNumber ='@Html.Raw(Localizer["ReferenceNumber"].Encode())';
        Resources.SelectOrder ='@Html.Raw(Localizer["SelectOrder"].Encode())';
        Resources.Year ='@Html.Raw(Localizer["Year"].Encode())';
        Resources.Month ='@Html.Raw(Localizer["Month"].Encode())';
        Resources.String ='@Html.Raw(Localizer["String"].Encode())';
        Resources.Counter ='@Html.Raw(Localizer["Counter"].Encode())';
        Resources.Separator ='@Html.Raw(Localizer["Separator"].Encode())';
        Resources.ResetByYear ='@Html.Raw(Localizer["ResetByYear"].Encode())';
        Resources.TemplateAddedToGlobal ='@Html.Raw(Localizer["TemplateAddedToGlobal"].Encode())';
        Resources.CategoriesInUse ='@Html.Raw(Localizer["CategoriesInUse"].Encode())';
        Resources.ReferenceCounter ='@Html.Raw(Localizer["ReferenceCounter"].Encode())';
        Resources.Value ='@Html.Raw(Localizer["Value"].Encode())';
        Resources.SelectAtLeastOneConfig ='@Html.Raw(Localizer["SelectAtLeastOneConfig"].Encode())';
        Resources.AllCategoryReferenceAlreadyExists = '@Html.Raw(Localizer["AllCategoryReferenceAlreadyExists"].Encode())';
        Resources.Next = '@Html.Raw(Localizer["Next"].Encode())';
        Resources.ClassificationsInUse = '@Html.Raw(Localizer["ClassificationsInUse"].Encode())';
        Resources.DownloadIamFormat = '@Html.Raw(Localizer["DownloadIamFormat"].Encode())';
        Resources.FillAtLeastOne = '@Html.Raw(Localizer["FillAtLeastOne"].Encode())';
        Resources.EventReceiverClassInfo = '@Html.Raw(Localizer["EventReceiverClassInfo"].Encode())';
        Resources.CSFileType = '@Html.Raw(Localizer["CSFileType"].Encode())';
        Resources.EventReceiver = '@Html.Raw(Localizer["EventReceiver"].Encode())';
        Resources.AssemblyFullQualifiedName  = '@Html.Raw(Localizer["AssemblyFullQualifiedName"].Encode())';
        Resources.Class   = '@Html.Raw(Localizer["Class"].Encode())';
        Resources.ActivityLog = '@Html.Raw(Localizer["ActivityLog"].Encode())';
        Resources.Status = '@Html.Raw(Localizer["Status"].Encode())';
        Resources.TopNavBar = '@Html.Raw(Localizer["TopNavBar"].Encode())';
        Resources.AllRolesMenus = '@Html.Raw(Localizer["AllRolesMenus"].Encode())';
        Resources.AllTabs = '@Html.Raw(Localizer["AllTabs"].Encode())';
        Resources.AllActions = '@Html.Raw(Localizer["AllActions"].Encode())';
        Resources.Security = '@Html.Raw(Localizer["Security"].Encode())';
        Resources.Group = '@Html.Raw(Localizer["Group"].Encode())';
        Resources.ParentMenu = '@Html.Raw(Localizer["ParentMenu"].Encode())';
        Resources.AttachmentFolders = '@Html.Raw(Localizer["AttachmentFolders"].Encode())';
        Resources.ManageAttachmentFolders = '@Html.Raw(Localizer["ManageAttachmentFolders"].Encode())';
        Resources.AttachmentFolderAddedToGlobal = '@Html.Raw(Localizer["AttachmentFolderAddedToGlobal"].Encode())';
        Resources.GlobalAttachmentFolders = '@Html.Raw(Localizer["GlobalAttachmentFolders"].Encode())';
        Resources.SpecificCharactersNotAllowed = '@Html.Raw(Localizer["SpecificCharactersNotAllowed"].Encode())';
        Resources.Custom = '@Html.Raw(Localizer["Custom"].Encode())';
        Resources.NameFr = '@Html.Raw(Localizer["French"].Encode())';
        Resources.NameAr = '@Html.Raw(Localizer["Arabic"].Encode())';
        Resources.ManageCategories = '@Html.Raw(Localizer["ManageCategories"].Encode())';
        Resources.TransferDate = '@Html.Raw(Localizer["TransferDate"].Encode())';
        Resources.TransferTime = '@Html.Raw(Localizer["TransferTime"].Encode())';
        Resources.CreatedBy ='@Html.Raw(Localizer["CreatedBy"].Encode())';
        Resources.OpenedDate ='@Html.Raw(Localizer["OpenedDate"].Encode())';
        Resources.LockedBy ='@Html.Raw(Localizer["LockedBy"].Encode())';
        Resources.LockedDate ='@Html.Raw(Localizer["LockedDate"].Encode())';
        Resources.Owner ='@Html.Raw(Localizer["Owner"].Encode())';
        Resources.ClosedDate ='@Html.Raw(Localizer["ClosedDate"].Encode())';
        Resources.Day ='@Html.Raw(Localizer["Day"].Encode())';
        Resources.Read ='@Html.Raw(Localizer["Read"].Encode())';
        Resources.UnlockConfirmation ='@Html.Raw(Localizer["UnlockConfirmation"].Encode())';
        Resources.LockConfirmation ='@Html.Raw(Localizer["LockConfirmation"].Encode())';
        Resources.OpenConfirmation ='@Html.Raw(Localizer["OpenConfirmation"].Encode())';
        Resources.KindlyNoteMarkedRead ='@Html.Raw(Localizer["KindlyNoteMarkedRead"].Encode())';
        Resources.AlreadyLockedBy ='@Html.Raw(Localizer["AlreadyLockedBy"].Encode())';
        Resources.DoYouWantToOpen ='@Html.Raw(Localizer["DoYouWantToOpen"].Encode())';
        Resources.SentToStructure = '@Html.Raw(Localizer["SentToStructure"].Encode())';
        Resources.SentToUser = '@Html.Raw(Localizer["SentToUser"].Encode())';
        Resources.From = '@Html.Raw(Localizer["From"].Encode())';
        Resources.SelectAllCategories= '@Html.Raw(Localizer["SelectAllCategories"].Encode())';
        Resources.DelegationAlreadyExists = '@Html.Raw(Localizer["DelegationAlreadyExists"].Encode())';
        Resources.ManageCategory = '@Html.Raw(Localizer["ManageCategory"].Encode())';
        Resources.BasicInformation = '@Html.Raw(Localizer["BasicInformation"].Encode())';
        Resources.BasicAttributes = '@Html.Raw(Localizer["BasicAttributes"].Encode())';
        Resources.CustomAttributes = '@Html.Raw(Localizer["CustomAttributes"].Encode())';
        Resources.WithoutFile = '@Html.Raw(Localizer["WithoutFile"].Encode())';
        Resources.ByFile = '@Html.Raw(Localizer["ByFile"].Encode())';
        Resources.ByTemplate = '@Html.Raw(Localizer["ByTemplate"].Encode())';
        Resources.Inbox = '@Html.Raw(Localizer["Inbox"].Encode())';
        Resources.Default = '@Html.Raw(Localizer["Default"].Encode())';
        Resources.DefaultWithViewer = '@Html.Raw(Localizer["DefaultWithViewer"].Encode())';
        Resources.VIPView = '@Html.Raw(Localizer["VIPView"].Encode())';
        Resources.DefaultView = '@Html.Raw(Localizer["DefaultView"].Encode())';
        Resources.Attachment = '@Html.Raw(Localizer["Attachment"].Encode())';
        Resources.Attachments = '@Html.Raw(Localizer["Attachments"].Encode())';
        Resources.LinkedCorrespondence = '@Html.Raw(Localizer["LinkedCorrespondence"].Encode())';
        Resources.BaseOnTransfer = '@Html.Raw(Localizer["BaseOnTransfer"].Encode())';
        Resources.OpenWithSecurity = '@Html.Raw(Localizer["OpenWithSecurity"].Encode())';
        Resources.DeleteForCreatorOnly = '@Html.Raw(Localizer["DeleteForCreatorOnly"].Encode())';
        Resources.IAMAttributeMapping = '@Html.Raw(Localizer["IAMAttributeMapping"].Encode())';
        Resources.Manage = '@Html.Raw(Localizer["Manage"].Encode())';
        Resources.Category = '@Html.Raw(Localizer["Category"].Encode())';
        Resources.StructureNameAr = '@Html.Raw(Localizer["StructureNameAr"].Encode())';
        Resources.StructureNameFr = '@Html.Raw(Localizer["StructureNameFr"].Encode())';
        Resources.UserStructureReceiver = '@Html.Raw(Localizer["UserStructureReceiver"].Encode())';
        Resources.UserStructureSender = '@Html.Raw(Localizer["UserStructureSender"].Encode())';
        Resources.UserPrivacy = '@Html.Raw(Localizer["UserPrivacy"].Encode())';
        Resources.UserPosition = '@Html.Raw(Localizer["UserPosition"].Encode())';
        Resources.MyTransfer = '@Html.Raw(Localizer["MyTransfer"].Encode())';
        Resources.DocumentMetadata = '@Html.Raw(Localizer["DocumentMetadata"].Encode())';
        Resources.VisualTracking = '@Html.Raw(Localizer["VisualTracking"].Encode())';
        Resources.Next = '@Html.Raw(Localizer["Next"].Encode())';
        Resources.Previous = '@Html.Raw(Localizer["Previous"].Encode())';
        Resources.Print = '@Html.Raw(Localizer["Print"].Encode())';
        Resources.Reset = '@Html.Raw(Localizer["Reset"].Encode())';
        Resources.ZoomOut = '@Html.Raw(Localizer["ZoomOut"].Encode())';
        Resources.ZoomIn = '@Html.Raw(Localizer["ZoomIn"].Encode())';
        Resources.UnRead = '@Html.Raw(Localizer["UnRead"].Encode())';
        Resources.EnableMouseZoom = '@Html.Raw(Localizer["EnableMouseZoom"].Encode())';
        Resources.DisableMouseZoom = '@Html.Raw(Localizer["DisableMouseZoom"].Encode())';
        Resources.NotStartedYet = '@Html.Raw(Localizer["NotStartedYet"].Encode())';
        Resources.Legend = '@Html.Raw(Localizer["Legend"].Encode())';
        Resources.ThisCorrespondenceDontHaveAnyTransfer = '@Html.Raw(Localizer["ThisCorrespondenceDontHaveAnyTransfer"].Encode())';
        Resources.You = '@Html.Raw(Localizer["You"].Encode())';
        Resources.OverDue = '@Html.Raw(Localizer["OverDue"].Encode())';
        Resources.English = '@Html.Raw(Localizer["English"].Encode())';
        Resources.Translation = '@Html.Raw(Localizer["Translation"].Encode())';
        Resources.Sender = '@Html.Raw(Localizer["Sender"].Encode())';
        Resources.Receiver = '@Html.Raw(Localizer["Receiver"].Encode())';
        Resources.Classification = '@Html.Raw(Localizer["Classification"].Encode())';
        Resources.Importance = '@Html.Raw(Localizer["Importance"].Encode())';
        Resources.Privacy = '@Html.Raw(Localizer["Privacy"].Encode())';
        Resources.Priority = '@Html.Raw(Localizer["Priority"].Encode())';
        Resources.FromUser = '@Html.Raw(Localizer["FromUser"].Encode())';
        Resources.ToUser = '@Html.Raw(Localizer["ToUser"].Encode())';
        Resources.FromStructure = '@Html.Raw(Localizer["FromStructure"].Encode())';
        Resources.ToStructure = '@Html.Raw(Localizer["ToStructure"].Encode())';
        Resources.FromTransferDate = '@Html.Raw(Localizer["FromTransferDate"].Encode())';
        Resources.ToTransferDate = '@Html.Raw(Localizer["ToTransferDate"].Encode())';
        Resources.FromCreatedDate = '@Html.Raw(Localizer["FromCreatedDate"].Encode())';
        Resources.ToCreatedDate = '@Html.Raw(Localizer["ToCreatedDate"].Encode())';
        Resources.SearchInInboxOf = '@Html.Raw(Localizer["SearchInInboxOf"].Encode())';
        Resources.MyInbox = '@Html.Raw(Localizer["MyInbox"].Encode())';
        Resources.SendingEntity = '@Html.Raw(Localizer["SendingEntity"].Encode())';
        Resources.ReceivingEntity = '@Html.Raw(Localizer["ReceivingEntity"].Encode())';
        Resources.TransferIsExist = '@Html.Raw(Localizer["TransferIsExist"].Encode())';
        Resources.DocumentSender = '@Html.Raw(Localizer["DocumentSender"].Encode())';
        Resources.DocumentReceiver = '@Html.Raw(Localizer["DocumentReceiver"].Encode())';
        Resources.DefaultValue = '@Html.Raw(Localizer["DefaultValue"].Encode())';
        Resources.Grouping = '@Html.Raw(Localizer["Grouping"].Encode())';
        Resources.NoteAlreadyExists = '@Html.Raw(Localizer["NoteAlreadyExists"].Encode())';
        Resources.Private = '@Html.Raw(Localizer["Private"].Encode())';
        Resources.Note = '@Html.Raw(Localizer["Note"].Encode())';
        Resources.Notes = '@Html.Raw(Localizer["Notes"].Encode())';
        Resources.DeleteNoteConfirmation = '@Html.Raw(Localizer["DeleteNoteConfirmation"].Encode())';
        Resources.NoPermission = '@Html.Raw(Localizer["NoPermission"].Encode())';
        Resources.CarbonCopy = '@Html.Raw(Localizer["CarbonCopy"].Encode())';
        Resources.UseCurrentStructure = '@Html.Raw(Localizer["UseCurrentStructure"].Encode())';
        Resources.OnBehalfOf = '@Html.Raw(Localizer["OnBehalfOf"].Encode())';
        Resources.RelatedToPriority = '@Html.Raw(Localizer["RelatedToPriority"].Encode())';
        Resources.InheritGlobalConfiguration = '@Html.Raw(Localizer["InheritGlobalConfiguration"].Encode())';
        Resources.Extensions = '@Html.Raw(Localizer["Extensions"].Encode())';
        Resources.Extension = '@Html.Raw(Localizer["Extension"].Encode())';
        Resources.Templates = '@Html.Raw(Localizer["Templates"].Encode())';
        Resources.NoTemplateSelected = '@Html.Raw(Localizer["NoTemplateSelected"].Encode())';
        Resources.InvalidFileType = '@Html.Raw(Localizer["InvalidFileType"].Encode())';
        Resources.TransferFromUser = '@Html.Raw(Localizer["TransferFromUser"].Encode())';
        Resources.TransferToUser = '@Html.Raw(Localizer["TransferToUser"].Encode())';
        Resources.TransferFromStructure = '@Html.Raw(Localizer["TransferFromStructure"].Encode())';
        Resources.TransferToStructure = '@Html.Raw(Localizer["TransferToStructure"].Encode())';
        Resources.TransferFromDate = '@Html.Raw(Localizer["TransferFromDate"].Encode())';
        Resources.TransferToDate = '@Html.Raw(Localizer["TransferToDate"].Encode())';
        Resources.IncludeCategory = '@Html.Raw(Localizer["IncludeCategory"].Encode())';
        Resources.SimpleSearch = '@Html.Raw(Localizer["SimpleSearch"].Encode())';
        Resources.AdvanceSearch = '@Html.Raw(Localizer["AdvanceSearch"].Encode())';
        Resources.AdvanceSearchConfiguration = '@Html.Raw(Localizer["AdvanceSearchConfiguration"].Encode())';
        Resources.EnableTransferToUsers = '@Html.Raw(Localizer["EnableTransferToUsers"].Encode())';
        Resources.SearchAssignedStructureSearchUsersDocuments = '@Html.Raw(Localizer["SearchAssignedStructureSearchUsersDocuments"].Encode())';
        Resources.Source = '@Html.Raw(Localizer["Source"].Encode())';
        Resources.ShowImageEditor = '@Html.Raw(Localizer["ShowImageEditor"].Encode())';
        Resources.RotateLeft = '@Html.Raw(Localizer["RotateLeft"].Encode())';
        Resources.RotateRight = '@Html.Raw(Localizer["RotateRight"].Encode())';
        Resources.Rotate180 = '@Html.Raw(Localizer["Rotate180"].Encode())';
        Resources.Mirror = '@Html.Raw(Localizer["Mirror"].Encode())';
        Resources.RemoveSelectedImages = '@Html.Raw(Localizer["RemoveSelectedImages"].Encode())';
        Resources.RemoveAllImages = '@Html.Raw(Localizer["RemoveAllImages"].Encode())';
        Resources.ChangeImageSize = '@Html.Raw(Localizer["ChangeImageSize"].Encode())';
        Resources.Crop = '@Html.Raw(Localizer["Crop"].Encode())';
        Resources.Back = '@Html.Raw(Localizer["Back"].Encode())';
        Resources.Duplex = '@Html.Raw(Localizer["Duplex"].Encode())';
        Resources.ShowUI = '@Html.Raw(Localizer["ShowUI"].Encode())';
        Resources.AutoFeeder = '@Html.Raw(Localizer["AutoFeeder"].Encode())';
        Resources.MyRequests = '@Html.Raw(Localizer["MyRequests"].Encode())';
        Resources.PleaseSelectATemplate = '@Html.Raw(Localizer["PleaseSelectATemplate"].Encode())';
        Resources.Quantity = '@Html.Raw(Localizer["Quantity"].Encode())';
        Resources.SelectType = '@Html.Raw(Localizer["SelectType"].Encode())';
        Resources.DeleteRowConfirmation = '@Html.Raw(Localizer["DeleteRowConfirmation"].Encode())';
        Resources.NonArchivedAttachments = '@Html.Raw(Localizer["NonArchivedAttachments"].Encode())';
        Resources.ScannerFirst = '@Html.Raw(Localizer["ScannerFirst"].Encode())';
        Resources.NonArchivedAttachmentsTypes = '@Html.Raw(Localizer["NonArchivedAttachmentsTypes"].Encode())';
        Resources.TypeAlreadyExists = '@Html.Raw(Localizer["TypeAlreadyExists"].Encode())';
        Resources.TypesInUse = '@Html.Raw(Localizer["TypesInUse"].Encode())';
        Resources.Locked= '@Html.Raw(Localizer["Locked"].Encode())';
        Resources.SelectAtLeastOneDocument = '@Html.Raw(Localizer["SelectAtLeastOneDocument"].Encode())';
        Resources.LinkedCorrespondences = '@Html.Raw(Localizer["LinkedCorrespondences"].Encode())';
        Resources.Transfers = '@Html.Raw(Localizer["Transfers"].Encode())';
        Resources.TransferDetails = '@Html.Raw(Localizer["TransferDetails"].Encode())';
        Resources.Attributes = '@Html.Raw(Localizer["Attributes"].Encode())';
        Resources.LinkedDocuments = '@Html.Raw(Localizer["LinkedDocuments"].Encode())';
        Resources.UserStructure = '@Html.Raw(Localizer["UserStructure"].Encode())';
        Resources.Register = '@Html.Raw(Localizer["Register"].Encode())';
        Resources.Registering = '@Html.Raw(Localizer["Registering"].Encode())';
        Resources.WorkingDays = '@Html.Raw(Localizer["WorkingDays"].Encode())';
        Resources.ExtensionByFile = '@Html.Raw(Localizer["ExtensionByFile"].Encode())';
        Resources.AddFolder = '@Html.Raw(Localizer["AddFolder"].Encode())';
        Resources.Rename = '@Html.Raw(Localizer["Rename"].Encode())';
        Resources.Replace = '@Html.Raw(Localizer["Replace"].Encode())';
        Resources.ViewHistory = '@Html.Raw(Localizer["ViewHistory"].Encode())';
        Resources.DropFilesOrClick = '@Html.Raw(Localizer["DropFilesOrClick"].Encode())';
        Resources.MultipleUpload = '@Html.Raw(Localizer["MultipleUpload"].Encode())';
        Resources.OriginalDocument = '@Html.Raw(Localizer["OriginalDocument"].Encode())';
        Resources.FilingPlan = '@Html.Raw(Localizer["FilingPlan"].Encode())';
        Resources.UseDefaultKeywords = '@Html.Raw(Localizer["UseDefaultKeywords"].Encode())';
        Resources.NotAllowedCharacters = '@Html.Raw(Localizer["NotAllowedCharacters"].Encode())'
        Resources.Table = '@Html.Raw(Localizer["Table"].Encode())';
        Resources.PropertyName = '@Html.Raw(Localizer["PropertyName"].Encode())';
        Resources.PropertyType = '@Html.Raw(Localizer["PropertyType"].Encode())';
        Resources.Nullable = '@Html.Raw(Localizer["Nullable"].Encode())';
        Resources.SearchableProperties = '@Html.Raw(Localizer["SearchableProperties"].Encode())';
        Resources.NotAllowedCharacters = '@Html.Raw(Localizer["NotAllowedCharacters"].Encode())';
        Resources.Size = '@Html.Raw(Localizer["Size"].Encode())';
        Resources.Restore = '@Html.Raw(Localizer["Restore"].Encode())';
        Resources.VersionHistory = '@Html.Raw(Localizer["VersionHistory"].Encode())';
        Resources.RestoreVersionConfirmation = '@Html.Raw(Localizer["RestoreVersionConfirmation"].Encode())';
        Resources.ReferenceNumberMsg = '@Html.Raw(Localizer["ReferenceNumberMsg"].Encode())';
        Resources.SentSuccessfully = '@Html.Raw(Localizer["SentSuccessfully"].Encode())';
        Resources.EnableSendWithoutReceiverOrPrivacy = '@Html.Raw(Localizer["EnableSendWithoutReceiverOrPrivacy"].Encode())';
        Resources.NoStructureReceivers = '@Html.Raw(Localizer["NoStructureReceivers"].Encode())';
        Resources.NoStructureReceiversWithSelectedPrivacy = '@Html.Raw(Localizer["NoStructureReceiversWithSelectedPrivacy"].Encode())';
        Resources.SendConfirmation = '@Html.Raw(Localizer["SendConfirmation"].Encode())';
        Resources.NotStructureSender = '@Html.Raw(Localizer["NotStructureSender"].Encode())';
        Resources.CreateCorrespondencByFile = '@Html.Raw(Localizer["CreateCorrespondencByFile"].Encode())';
        Resources.FileUpload = '@Html.Raw(Localizer["FileUpload"].Encode())';
        Resources.TodayToDoList = '@Html.Raw(Localizer["TodayToDoList"].Encode())';
        Resources.CategoriesStatistics = '@Html.Raw(Localizer["CategoriesStatistics"].Encode())';
        Resources.DocumentTransferAverageCompletion = '@Html.Raw(Localizer["DocumentTransferAverageCompletion"].Encode())';
        Resources.MyTransfers = '@Html.Raw(Localizer["MyTransfers"].Encode())';
        Resources.AllTransfers = '@Html.Raw(Localizer["AllTransfers"].Encode())';
        Resources.AverageInDays = '@Html.Raw(Localizer["AverageInDays"].Encode())';
        Resources.AverageInHours = '@Html.Raw(Localizer["AverageInHours"].Encode())';
        Resources.AverageInMinutes = '@Html.Raw(Localizer["AverageInMinutes"].Encode())';
        Resources.AverageInSeconds = '@Html.Raw(Localizer["AverageInSeconds"].Encode())';
        Resources.TransfersHistory = '@Html.Raw(Localizer["TransfersHistory"].Encode())'
        Resources.SizeMB = '@Html.Raw(Localizer["SizeMB"].Encode())'
        Resources.Restore = '@Html.Raw(Localizer["Restore"].Encode())'
        Resources.VersionHistory = '@Html.Raw(Localizer["VersionHistory"].Encode())'
        Resources.RestoreVersionConfirmation = '@Html.Raw(Localizer["RestoreVersionConfirmation"].Encode())'
        Resources.CreateCorrespondencByFile = '@Html.Raw(Localizer["CreateCorrespondencByFile"].Encode())'
        Resources.FileUpload = '@Html.Raw(Localizer["FileUpload"].Encode())'
        Resources.TodayToDoList = '@Html.Raw(Localizer["TodayToDoList"].Encode())'
        Resources.CategoriesStatistics = '@Html.Raw(Localizer["CategoriesStatistics"].Encode())'
        Resources.BarcodeConfiguration = '@Html.Raw(Localizer["BarcodeConfiguration"].Encode())'
        Resources.BarHeight = '@Html.Raw(Localizer["BarHeight"].Encode())'
        Resources.ImageWidth = '@Html.Raw(Localizer["ImageWidth"].Encode())'
        Resources.ImageHeight = '@Html.Raw(Localizer["ImageHeight"].Encode())'
        Resources.CodeTextVisible = '@Html.Raw(Localizer["CodeTextVisible"].Encode())'
        Resources.CaptionAboveVisible = '@Html.Raw(Localizer["CaptionAboveVisible"].Encode())'
        Resources.CaptionBelowVisible = '@Html.Raw(Localizer["CaptionBelowVisible"].Encode())'
        Resources.Align = '@Html.Raw(Localizer["Align"].Encode())'
        Resources.Left = '@Html.Raw(Localizer["Left"].Encode())'
        Resources.Center = '@Html.Raw(Localizer["Center"].Encode())'
        Resources.Right = '@Html.Raw(Localizer["Right"].Encode())'
        Resources.FontSize = '@Html.Raw(Localizer["FontSize"].Encode())'
        Resources.FontFamilyName = '@Html.Raw(Localizer["FontFamilyName"].Encode())'
        Resources.BarcodeType = '@Html.Raw(Localizer["BarcodeType"].Encode())'
        Resources.ArabicCharactersOnlyCompatibleWithPdf417Barcode = '@Html.Raw(Localizer["ArabicCharactersOnlyCompatibleWithPdf417Barcode"].Encode())'
        Resources.ClickOnIconToViewDocumentBarcode = '@Html.Raw(Localizer["ClickOnIconToViewDocumentBarcode"].Encode())'
        Resources.ViewBarcode = '@Html.Raw(Localizer["ViewBarcode"].Encode())'
        Resources.DocumentBarcode = '@Html.Raw(Localizer["DocumentBarcode"].Encode())'
        Resources.True='@Html.Raw(Localizer["True"].Encode())';
        Resources.False='@Html.Raw(Localizer["False"].Encode())';
        Resources.TransfersHistory = '@Html.Raw(Localizer["TransfersHistory"].Encode())';
        Resources.Remove = '@Html.Raw(Localizer["Remove"].Encode())';
        Resources.CancelUpload = '@Html.Raw(Localizer["CancelUpload"].Encode())';
        Resources.CancelUploadConfirmation = '@Html.Raw(Localizer["CancelUploadConfirmation"].Encode())';
        Resources.FileDeletedSuccessfully = '@Html.Raw(Localizer["FileDeletedSuccessfully"].Encode())';
        Resources.FolderDeletedSuccessfully = '@Html.Raw(Localizer["FolderDeletedSuccessfully"].Encode())';
        Resources.FileUploadedSuccessfully = '@Html.Raw(Localizer["FileUploadedSuccessfully"].Encode())';
        Resources.FileReplacedSuccessfully = '@Html.Raw(Localizer["FileReplacedSuccessfully"].Encode())';
        Resources.VersionDeletedSuccessfully = '@Html.Raw(Localizer["VersionDeletedSuccessfully"].Encode())';
        Resources.VersionRestoredSuccessfully = '@Html.Raw(Localizer["VersionRestoredSuccessfully"].Encode())';
        Resources.FolderAddedSuccessfully = '@Html.Raw(Localizer["FolderAddedSuccessfully"].Encode())';
        Resources.FolderRenamedSuccessfully = '@Html.Raw(Localizer["FolderRenamedSuccessfully"].Encode())';
        Resources.FileRenamedSuccessfully = '@Html.Raw(Localizer["FileRenamedSuccessfully"].Encode())';
        Resources.Browse = '@Html.Raw(Localizer["Browse"].Encode())';
        Resources.Uploading ='@Html.Raw(Localizer["Uploading"].Encode())';
        Resources.LoadMore ='@Html.Raw(Localizer["LoadMore"].Encode())';
        Resources.End ='@Html.Raw(Localizer["End"].Encode())';
        Resources.MaxFileSize ='@Html.Raw(Localizer["MaxFileSize"].Encode())';
        Resources.FileTooBig ='@Html.Raw(Localizer["FileTooBig"].Encode())';
        Resources.RefreshMessage ='@Html.Raw(Localizer["RefreshMessage"].Encode())';
        Resources.Complete ='@Html.Raw(Localizer["Complete"].Encode())';
        Resources.CompleteConfirmation ='@Html.Raw(Localizer["CompleteConfirmation"].Encode())';
        Resources.CannotCompleteWarning ='@Html.Raw(Localizer["CannotCompleteWarning"].Encode())';
        Resources.Barcode ='@Html.Raw(Localizer["Barcode"].Encode())';
        Resources.BarcodeClassInfo ='@Html.Raw(Localizer["BarcodeClassInfo"].Encode())';
        Resources.GridTrue ='@Html.Raw(Localizer["GridTrue"].Encode())';
        Resources.GridFalse ='@Html.Raw(Localizer["GridFalse"].Encode())';
        Resources.SameBarcodeConfigurationExists ='@Html.Raw(Localizer["SameBarcodeConfigurationExists"].Encode())';
        Resources.MakeSureImageExists ='@Html.Raw(Localizer["MakeSureImageExists"].Encode())';
        Resources.CreatedCategoriesStatistics ='@Html.Raw(Localizer["CreatedCategoriesStatistics"].Encode())';
        Resources.ReceivedCategoriesStatistics ='@Html.Raw(Localizer["ReceivedCategoriesStatistics"].Encode())';
        Resources.CaptionAboveText ='@Html.Raw(Localizer["CaptionAboveText"].Encode())';
        Resources.CodeText ='@Html.Raw(Localizer["CodeText"].Encode())';
        Resources.CaptionBelowText ='@Html.Raw(Localizer["CaptionBelowText"].Encode())';
        Resources.CodeTextIsRequired ='@Html.Raw(Localizer["CodeTextIsRequired"].Encode())';
        Resources.NewHeight ='@Html.Raw(Localizer["NewHeight"].Encode())';
        Resources.NewWidth ='@Html.Raw(Localizer["NewWidth"].Encode())';
        Resources.InterpolationMethod ='@Html.Raw(Localizer["InterpolationMethod"].Encode())';
        Resources.NearestNeighbor ='@Html.Raw(Localizer["NearestNeighbor"].Encode())';
        Resources.Bilinear ='@Html.Raw(Localizer["Bilinear"].Encode())';
        Resources.Bicubic ='@Html.Raw(Localizer["Bicubic"].Encode())';
        Resources.ReplyToStructure ='@Html.Raw(Localizer["ReplyToStructure"].Encode())';
        Resources.ReplyToUser ='@Html.Raw(Localizer["ReplyToUser"].Encode())';
        Resources.CannotCompleteOneDocument ='@Html.Raw(Localizer["CannotCompleteOneDocument"].Encode())';
        Resources.CompleteOneTsfConfirmation ='@Html.Raw(Localizer["CompleteOneTsfConfirmation"].Encode())';
        Resources.RecallConfirmation ='@Html.Raw(Localizer["RecallConfirmation"].Encode())';
        Resources.Recall ='@Html.Raw(Localizer["Recall"].Encode())';
        Resources.Export ='@Html.Raw(Localizer["Export"].Encode())';
        Resources.Import ='@Html.Raw(Localizer["Import"].Encode())';
        Resources.CTSFileType ='@Html.Raw(Localizer["CTSFileType"].Encode())';
        Resources.Importfile ='@Html.Raw(Localizer["Importfile"].Encode())';
        Resources.Logout = '@Html.Raw(Localizer["Logout"].Encode())';
        Resources.LoggedOutMessage = '@Html.Raw(Localizer["LoggedOutMessage"].Encode())';
        Resources.Login = '@Html.Raw(Localizer["Login"].Encode())';
        Resources.RedirectToIdentity = '@Html.Raw(Localizer["RedirectToIdentity"].Encode())';
        Resources.DoNotRedirect = '@Html.Raw(Localizer["DoNotRedirect"].Encode())';
        Resources.NoUserWithSelectedPrivacy ='@Html.Raw(Localizer["NoUserWithSelectedPrivacy"].Encode())';
        Resources.TransferAndClose ='@Html.Raw(Localizer["TransferAndClose"].Encode())';
        Resources.cCedConfirmation ='@Html.Raw(Localizer["cCedConfirmation"].Encode())';
        Resources.AuditTrail = '@Html.Raw(Localizer["AuditTrail"].Encode())';
        Resources.Item = '@Html.Raw(Localizer["Item"].Encode())';
        Resources.NewOriginalValue = '@Html.Raw(Localizer["NewOriginalValue"].Encode())';
        Resources.OriginalValue = '@Html.Raw(Localizer["OriginalValue"].Encode())';
        Resources.AuditTrailMode = '@Html.Raw(Localizer["AuditTrailMode"].Encode())';
        Resources.BasicAuditInfo = '@Html.Raw(Localizer["BasicAuditInfo"].Encode())';
        Resources.TheFollowingActionsWillBeOverridden = '@Html.Raw(Localizer["TheFollowingActionsWillBeOverridden"].Encode())';
        Resources.FullAuditInfo = '@Html.Raw(Localizer["FullAuditInfo"].Encode())';
        Resources.AuditValues ='@Html.Raw(Localizer["AuditValues"].Encode())';
        Resources.Original ='@Html.Raw(Localizer["Original"].Encode())';
        Resources.NewOriginal ='@Html.Raw(Localizer["NewOriginal"].Encode())';
        Resources.AddAttachment ='@Html.Raw(Localizer["AddAttachment"].Encode())';
        Resources.DownloadAttachment ='@Html.Raw(Localizer["DownloadAttachment"].Encode())';
        Resources.DeleteAttachment ='@Html.Raw(Localizer["DeleteAttachment"].Encode())';
        Resources.PrintAttachment ='@Html.Raw(Localizer["PrintAttachment"].Encode())';
        Resources.ReplaceAttachment ='@Html.Raw(Localizer["ReplaceAttachment"].Encode())';
        Resources.RestoreAttachment ='@Html.Raw(Localizer["RestoreAttachment"].Encode())';
        Resources.ViewAttachment ='@Html.Raw(Localizer["ViewAttachment"].Encode())';
        Resources.ViewTransfer ='@Html.Raw(Localizer["ViewTransfer"].Encode())';
        Resources.ViewDocument ='@Html.Raw(Localizer["ViewDocument"].Encode())';
        Resources.ViewAttachments ='@Html.Raw(Localizer["ViewAttachments"].Encode())';
        Resources.ViewNotes ='@Html.Raw(Localizer["ViewNotes"].Encode())';
        Resources.ViewLinkedDocument ='@Html.Raw(Localizer["ViewLinkedDocument"].Encode())';
        Resources.ViewNonArchivedAttachments ='@Html.Raw(Localizer["ViewNonArchivedAttachments"].Encode())';
        Resources.ViewTransfersHistory ='@Html.Raw(Localizer["ViewTransfersHistory"].Encode())';
        Resources.ViewVisualTracking ='@Html.Raw(Localizer["ViewVisualTracking"].Encode())';
        Resources.ViewActivityLog ='@Html.Raw(Localizer["ViewActivityLog"].Encode())';
        Resources.Lock ='@Html.Raw(Localizer["Lock"].Encode())';
        Resources.Unlock ='@Html.Raw(Localizer["Unlock"].Encode())';
        Resources.CaptionAboveStructure ='@Html.Raw(Localizer["CaptionAboveStructure"].Encode())';
        Resources.CaptionBelowStructure ='@Html.Raw(Localizer["CaptionBelowStructure"].Encode())';
        Resources.StructureCode ='@Html.Raw(Localizer["StructureCode"].Encode())';
        Resources.CaptionAboveCreatedDate ='@Html.Raw(Localizer["CaptionAboveCreatedDate"].Encode())';
        Resources.CaptionBelowCreatedDate ='@Html.Raw(Localizer["CaptionBelowCreatedDate"].Encode())';
        Resources.AttachmentEditable ='@Html.Raw(Localizer["AttachmentEditable"].Encode())';
        Resources.FileInUse ='@Html.Raw(Localizer["FileInUse"].Encode())';
        Resources.DocumentIsCompleted ='@Html.Raw(Localizer["DocumentIsCompleted"].Encode())';
        Resources.OriginalFileInUse ='@Html.Raw(Localizer["OriginalFileInUse"].Encode())';
        Resources.ThisCorresWillStayInYourDraft ='@Html.Raw(Localizer["ThisCorresWillStayInYourDraft"].Encode())';
        Resources.ThisCorresWillStayInYourInbox ='@Html.Raw(Localizer["ThisCorresWillStayInYourInbox"].Encode())';
        Resources.Document ='@Html.Raw(Localizer["Document"].Encode())';
        Resources.SendCorrespondenceConfirmation = '@Html.Raw(Localizer["SendCorrespondenceConfirmation"].Encode())';
        Resources.TransferWasCompletedNotLastOpenTransfer = '@Html.Raw(Localizer["TransferWasCompletedNotLastOpenTransfer"].Encode())';
        Resources.TransferWasCompletedNoOriginalMail = '@Html.Raw(Localizer["TransferWasCompletedNoOriginalMail"].Encode())';
        Resources.CorrespondenceNotCompleteNoOriginalMail = '@Html.Raw(Localizer["CorrespondenceNotCompleteNoOriginalMail"].Encode())';
        Resources.OpenCorrespondence = '@Html.Raw(Localizer["OpenCorrespondence"].Encode())';
        Resources.TransferConfirmation = '@Html.Raw(Localizer["TransferConfirmation"].Encode())';
        Resources.AllSelectedItemsCCed = '@Html.Raw(Localizer["AllSelectedItemsCCed"].Encode())';
        Resources.AllSelectedItemsAreReadOnly = '@Html.Raw(Localizer["AllSelectedItemsAreReadOnly"].Encode())';
        Resources.TheBelowTransfersFileInUse = '@Html.Raw(Localizer["TheBelowTransfersFileInUse"].Encode())';
        Resources.CouldntTransferTheBelow = '@Html.Raw(Localizer["CouldntTransferTheBelow"].Encode())';
        Resources.Draft = '@Html.Raw(Localizer["Draft"].Encode())';
        Resources.Sent = '@Html.Raw(Localizer["Sent"].Encode())';
        Resources.External = '@Html.Raw(Localizer["External"].Encode())';
        Resources.Internal = '@Html.Raw(Localizer["Internal"].Encode())';
        Resources.Incoming = '@Html.Raw(Localizer["Incoming"].Encode())';
        Resources.Outgoing = '@Html.Raw(Localizer["Outgoing"].Encode())';

        Resources.Enable = '@Html.Raw(Localizer["Enable"].Encode())';
        Resources.Gray = '@Html.Raw(Localizer["Gray"].Encode())';
        Resources.Reply = '@Html.Raw(Localizer["Reply"].Encode())';
        Resources.Dismiss = '@Html.Raw(Localizer["Dismiss"].Encode())';
        Resources.DismissCarbonCopyConfirmation = '@Html.Raw(Localizer["DismissCarbonCopyConfirmation"].Encode())';
        Resources.AllSelectedItemsNotCCed = '@Html.Raw(Localizer["AllSelectedItemsNotCCed"].Encode())';
        Resources.CannotDismissCarbonCopyWarning = '@Html.Raw(Localizer["CannotDismissCarbonCopyWarning"].Encode())';
        Resources.DismissCarbonCopyOneTsfConfirmation = '@Html.Raw(Localizer["DismissCarbonCopyOneTsfConfirmation"].Encode())';
        Resources.Multiple = '@Html.Raw(Localizer["Multiple"].Encode())';
        Resources.DismissCopy ='@Html.Raw(Localizer["DismissCopy"].Encode())';
        Resources.StructuresAndUsers ='@Html.Raw(Localizer["StructuresAndUsers"].Encode())';
        Resources.Structures ='@Html.Raw(Localizer["Structures"].Encode())';
        Resources.Bookmarks = '@Html.Raw(Localizer["Bookmarks"].Encode())';
        Resources.BookmarksReplaceMessage = '@Html.Raw(Localizer["BookmarksReplaceMessage"].Encode())';
        Resources.DateFormat = '@Html.Raw(Localizer["DateFormat"].Encode())';
        Resources.MultipleValueSeperator = '@Html.Raw(Localizer["MultipleValueSeperator"].Encode())';
        Resources.BookmarksSettings = '@Html.Raw(Localizer["BookmarksSettings"].Encode())';
        Resources.SendingEntityAr = '@Html.Raw(Localizer["SendingEntityAr"].Encode())';
        Resources.SendingEntityFr = '@Html.Raw(Localizer["SendingEntityFr"].Encode())';
        Resources.ReceivingEntityAr = '@Html.Raw(Localizer["ReceivingEntityAr"].Encode())';
        Resources.ReceivingEntityFr = '@Html.Raw(Localizer["ReceivingEntityFr"].Encode())';
        Resources.CategoryAr = '@Html.Raw(Localizer["CategoryAr"].Encode())';
        Resources.CategoryFr = '@Html.Raw(Localizer["CategoryFr"].Encode())';
        Resources.PriorityAr = '@Html.Raw(Localizer["PriorityAr"].Encode())';
        Resources.PriorityFr = '@Html.Raw(Localizer["PriorityFr"].Encode())';
        Resources.PrivacyAr = '@Html.Raw(Localizer["PrivacyAr"].Encode())';
        Resources.PrivacyFr = '@Html.Raw(Localizer["PrivacyFr"].Encode())';
        Resources.ClassificationAr = '@Html.Raw(Localizer["ClassificationAr"].Encode())';
        Resources.ClassificationFr = '@Html.Raw(Localizer["ClassificationFr"].Encode())';
        Resources.ImportanceAr = '@Html.Raw(Localizer["ImportanceAr"].Encode())';
        Resources.ImportanceFr = '@Html.Raw(Localizer["ImportanceFr"].Encode())';
        Resources.DocumentTypeAr = '@Html.Raw(Localizer["DocumentTypeAr"].Encode())';
        Resources.DocumentTypeFr = '@Html.Raw(Localizer["DocumentTypeFr"].Encode())';
        Resources.CarbonCopyAr = '@Html.Raw(Localizer["CarbonCopyAr"].Encode())';
        Resources.CarbonCopyFr = '@Html.Raw(Localizer["CarbonCopyFr"].Encode())';
        Resources.TodayDate = '@Html.Raw(Localizer["TodayDate"].Encode())';
        Resources.LoggedInUser = '@Html.Raw(Localizer["LoggedInUser"].Encode())';
        Resources.Visible = '@Html.Raw(Localizer["Visible"].Encode())';
        Resources.Inherit = '@Html.Raw(Localizer["Inherit"].Encode())';
        Resources.EnableTotalCount = '@Html.Raw(Localizer["EnableTotalCount"].Encode())';
        Resources.EnableTodayCount = '@Html.Raw(Localizer["EnableTodayCount"].Encode())';
        Resources.Filters = '@Html.Raw(Localizer["Filters"].Encode())';
        Resources.Columns = '@Html.Raw(Localizer["Columns"].Encode())';
        Resources.ColumnDetail = '@Html.Raw(Localizer["ColumnDetail"].Encode())';
        Resources.Conditions = '@Html.Raw(Localizer["Conditions"].Encode())';
        Resources.Column = '@Html.Raw(Localizer["Column"].Encode())';
        Resources.Number = '@Html.Raw(Localizer["Number"].Encode())';
        Resources.MustReturnValidString = '@Html.Raw(Localizer["MustReturnValidString"].Encode())';
        Resources.SelectFilters = '@Html.Raw(Localizer["SelectFilters"].Encode())';
        Resources.SelectColumn = '@Html.Raw(Localizer["SelectColumn"].Encode())';
        Resources.SelectCondition = '@Html.Raw(Localizer["SelectCondition"].Encode())';
        Resources.ColumnDetail = '@Html.Raw(Localizer["ColumnDetail"].Encode())';
        Resources.AlreadyExists = '@Html.Raw(Localizer["AlreadyExists"].Encode())';
        Resources.NameAlreadyExists = '@Html.Raw(Localizer["NameAlreadyExists"].Encode())';
        Resources.DocumentCreatedDate = '@Html.Raw(Localizer["DocumentCreatedDate"].Encode())';
        Resources.DocumentCompletedDate = '@Html.Raw(Localizer["DocumentCompletedDate"].Encode())';
        Resources.TransferClosedDate = '@Html.Raw(Localizer["TransferClosedDate"].Encode())';
        Resources.TransferCreatedDate = '@Html.Raw(Localizer["TransferCreatedDate"].Encode())';
        Resources.Closed = '@Html.Raw(Localizer["Closed"].Encode())';
        Resources.TotalCountJsFunctionName  = '@Html.Raw(Localizer["TotalCountJsFunctionName"].Encode())';
        Resources.TodayCountJsFunctionName  = '@Html.Raw(Localizer["TodayCountJsFunctionName"].Encode())';
        Resources.ThisMethodShouldBeResponsibleOfCreatingTheInbox  = '@Html.Raw(Localizer["ThisMethodShouldBeResponsibleOfCreatingTheInbox"].Encode())';
        Resources.ShouldReturnInteger  = '@Html.Raw(Localizer["ShouldReturnInteger"].Encode())';
        Resources.IsClosedTransfer  = '@Html.Raw(Localizer["IsClosedTransfer"].Encode())';
        Resources.MustReturnValidString  = '@Html.Raw(Localizer["MustReturnValidString"].Encode())';
        Resources.DraftInheritMessage = '@Html.Raw(Localizer["DraftInheritMessage"].Encode())';
        Resources.InboxInheritMessage = '@Html.Raw(Localizer["InboxInheritMessage"].Encode())';
        Resources.CompletedInheritMessage = '@Html.Raw(Localizer["CompletedInheritMessage"].Encode())';
        Resources.MyRequestsInheritMessage = '@Html.Raw(Localizer["MyRequestsInheritMessage"].Encode())';
        Resources.ClosedInheritMessage = '@Html.Raw(Localizer["ClosedInheritMessage"].Encode())';
        Resources.SentInheritMessage = '@Html.Raw(Localizer["SentInheritMessage"].Encode())';
        Resources.Public = '@Html.Raw(Localizer["Public"].Encode())';
        Resources.SearchInfo = '@Html.Raw(Localizer["SearchInfo"].Encode())';
        Resources.SearchComparisonOperatorMessage = '@Html.Raw(Localizer["SearchComparisonOperatorMessage"].Encode())';
        Resources.SearchComparisonOperatorMessageLocation = '@Html.Raw(Localizer["SearchComparisonOperatorMessageLocation"].Encode())';
        Resources.SearchComparisonOperatorMessageValue = '@Html.Raw(Localizer["SearchComparisonOperatorMessageValue"].Encode())';
        Resources.DefaultSearchSignPerComponentTypeMessage = '@Html.Raw(Localizer["DefaultSearchSignPerComponentTypeMessage"].Encode())';
        Resources.ComponentType = '@Html.Raw(Localizer["ComponentType"].Encode())';
        Resources.DefaultSearchSign = '@Html.Raw(Localizer["DefaultSearchSign"].Encode())';
        Resources.TextfieldTextareaRadioTags = '@Html.Raw(Localizer["TextfieldTextareaRadioTags"].Encode())';
        Resources.CheckboxNumberDatetimeTimeSelect = '@Html.Raw(Localizer["CheckboxNumberDatetimeTimeSelect"].Encode())';
        Resources.AllSelectedItemsHaveDifferentSender = '@Html.Raw(Localizer["AllSelectedItemsHaveDifferentSender"].Encode())';
        Resources.SubmitClose = '@Html.Raw(Localizer["SubmitClose"].Encode())';
        Resources.BookmarkWarningMessage = '@Html.Raw(Localizer["BookmarkWarningMessage"].Encode())';
        Resources.ExportDescription = '@Html.Raw(Localizer["ExportDescription"].Encode())';
        Resources.ExportDescriptionVerif = '@Html.Raw(Localizer["ExportDescriptionVerif"].Encode())';
        Resources.ExportYourConfiguration = '@Html.Raw(Localizer["ExportYourConfiguration"].Encode())';
        Resources.ExportYourConfigurationDescription = '@Html.Raw(Localizer["ExportYourConfigurationDescription"].Encode())';
        Resources.ExportAll = '@Html.Raw(Localizer["ExportAll"].Encode())';
        Resources.SelectSpecificConfiguration = '@Html.Raw(Localizer["SelectSpecificConfiguration"].Encode())';
        Resources.Export = '@Html.Raw(Localizer["Export"].Encode())';
        Resources.Import = '@Html.Raw(Localizer["Import"].Encode())';
        Resources.CheckAtleastOneConfig = '@Html.Raw(Localizer["CheckAtleastOneConfig"].Encode())';
        Resources.CustomParameters = '@Html.Raw(Localizer["CustomParameters"].Encode())';
        Resources.LookupAndLookupItems = '@Html.Raw(Localizer["LookupAndLookupItems"].Encode())';
        Resources.SecurityMatrixExportWarning = '@Html.Raw(Localizer["SecurityMatrixExportWarning"].Encode())';
        Resources.CustomParametersExportWarning = '@Html.Raw(Localizer["CustomParametersExportWarning"].Encode())';
        Resources.LookupExportWarning = '@Html.Raw(Localizer["LookupExportWarning"].Encode())';
        Resources.ImportDescription = '@Html.Raw(Localizer["ImportDescription"].Encode())';
        Resources.CategoriesExportWarning = '@Html.Raw(Localizer["CategoriesExportWarning"].Encode())';
        Resources.NodeExportWarning = '@Html.Raw(Localizer["NodeExportWarning"].Encode())';
        Resources.Action = '@Html.Raw(Localizer["Action"].Encode())';
        Resources.SubmitNoteConfirmation = '@Html.Raw(Localizer["SubmitNoteConfirmation"].Encode())';
        Resources.SubmitLinkedCorresConfirmation = '@Html.Raw(Localizer["SubmitLinkedCorresConfirmation"].Encode())';
        Resources.ProceedConfirmation = '@Html.Raw(Localizer["ProceedConfirmation"].Encode())';
        Resources.RenameAttachment = '@Html.Raw(Localizer["RenameAttachment"].Encode())';
        Resources.RenameFolder = '@Html.Raw(Localizer["RenameFolder"].Encode())';
        Resources.None = '@Html.Raw(Localizer["None"].Encode())';
        Resources.Basic = '@Html.Raw(Localizer["Basic"].Encode())';
        Resources.Full = '@Html.Raw(Localizer["Full"].Encode())';
        Resources.CannotDeleteRegisteredDocumentsWarning = '@Html.Raw(Localizer["CannotDeleteRegisteredDocumentsWarning"].Encode())';
        Resources.HasNodeChildrenMessage = '@Html.Raw(Localizer["HasNodeChildrenMessage"].Encode())';
        Resources.AtLeastOneInternal = '@Html.Raw(Localizer["AtLeastOneInternal"].Encode())';
        Resources.ExternalReferenceNumber = '@Html.Raw(Localizer["ExternalReferenceNumber"].Encode())';
        Resources.ExternalReferencedNumberInUse = '@Html.Raw(Localizer["ExternalReferencedNumberInUse"].Encode())';
        Resources.CannotDeleteRegisteredDocumentsWarning = '@Html.Raw(Localizer["CannotDeleteRegisteredDocumentsWarning"].Encode())';
        Resources.TheBelowTransfersLockedByAnotherUser = '@Html.Raw(Localizer["TheBelowTransfersLockedByAnotherUser"].Encode())';
        Resources.CCed = '@Html.Raw(Localizer["CCed"].Encode())';
        Resources.SystemDashboard = '@Html.Raw(Localizer["SystemDashboard"].Encode())';
        Resources.CountPerCategoryAndStatus = '@Html.Raw(Localizer["CountPerCategoryAndStatus"].Encode())';
        Resources.CreatedCorrespondencesPerDay = '@Html.Raw(Localizer["CreatedCorrespondencesPerDay"].Encode())';
        Resources.StatisticsPerStructure = '@Html.Raw(Localizer["StatisticsPerStructure"].Encode())';
        Resources.StatisticsPerUser = '@Html.Raw(Localizer["StatisticsPerUser"].Encode())';
        Resources.StatisticsPerTransferPurpose = '@Html.Raw(Localizer["StatisticsPerTransferPurpose"].Encode())';
        Resources.Count = '@Html.Raw(Localizer["Count"].Encode())';
        Resources.PercentageOfCorrespondencesCompletedVsInProgress = '@Html.Raw(Localizer["PercentageOfCorrespondencesCompletedVsInProgress"].Encode())';
        Resources.OnTime = '@Html.Raw(Localizer["OnTime"].Encode())';
        Resources.StatisticsForInProgressCorrespondences = '@Html.Raw(Localizer["StatisticsForInProgressCorrespondences"].Encode())';
        Resources.StatisticsForCompletedCorrespondences = '@Html.Raw(Localizer["StatisticsForCompletedCorrespondences"].Encode())';
        Resources.StatisticsForInProgressTransfers = '@Html.Raw(Localizer["StatisticsForInProgressTransfers"].Encode())';
        Resources.StatisticsForCompletedTransfers = '@Html.Raw(Localizer["StatisticsForCompletedTransfers"].Encode())';
        Resources.Total = '@Html.Raw(Localizer["Total"].Encode())';
        Resources.FixedLayout = '@Html.Raw(Localizer["FixedLayout"].Encode())';
        Resources.TotalCreatedCorrespondences = '@Html.Raw(Localizer["TotalCreatedCorrespondences"].Encode())';
        Resources.Correspondences = '@Html.Raw(Localizer["Correspondences"].Encode())';
        Resources.TotalCorrespondencesInProgress = '@Html.Raw(Localizer["TotalCorrespondencesInProgress"].Encode())';
        Resources.AverageCorrespondencesPerDay = '@Html.Raw(Localizer["AverageCorrespondencesPerDay"].Encode())';
        Resources.AveragePerDay = '@Html.Raw(Localizer["AveragePerDay"].Encode())';
        Resources.AverageCorrespondencesPerMonth = '@Html.Raw(Localizer["AverageCorrespondencesPerMonth"].Encode())';
        Resources.AveragePerMonth = '@Html.Raw(Localizer["AveragePerMonth"].Encode())';
        Resources.EnableDelegation = '@Html.Raw(Localizer["EnableDelegation"].Encode())';
        Resources.StatisticsPerStructureInfo = '@Html.Raw(Localizer["StatisticsPerStructureInfo"].Encode())';
        Resources.StatisticsPerUserInfo = '@Html.Raw(Localizer["StatisticsPerUserInfo"].Encode())';
        Resources.StatisticsPerTransferPurposeInfo = '@Html.Raw(Localizer["StatisticsPerTransferPurposeInfo"].Encode())';
        Resources.OuterCircle = '@Html.Raw(Localizer["OuterCircle"].Encode())';
        Resources.InnerCircle = '@Html.Raw(Localizer["InnerCircle"].Encode())';
        Resources.StatisticsForInProgressCorrespondencesInfo = '@Html.Raw(Localizer["StatisticsForInProgressCorrespondencesInfo"].Encode())';
        Resources.StatisticsForCompletedCorrespondencesInfo = '@Html.Raw(Localizer["StatisticsForCompletedCorrespondencesInfo"].Encode())';
        Resources.StatisticsForInProgressTransfersInfo = '@Html.Raw(Localizer["StatisticsForInProgressTransfersInfo"].Encode())';
        Resources.StatisticsForCompletedTransfersInfo = '@Html.Raw(Localizer["StatisticsForCompletedTransfersInfo"].Encode())';
        Resources.CountPerCategoryAndStatusUserInfo = '@Html.Raw(Localizer["CountPerCategoryAndStatusUserInfo"].Encode())';
        Resources.PercentageOfCorrespondencesCompletedVsInProgressUserInfo = '@Html.Raw(Localizer["PercentageOfCorrespondencesCompletedVsInProgressUserInfo"].Encode())';
        Resources.StatisticsForInProgressCorrespondencesUserInfo = '@Html.Raw(Localizer["StatisticsForInProgressCorrespondencesUserInfo"].Encode())';
        Resources.StatisticsForCompletedCorrespondencesUserInfo = '@Html.Raw(Localizer["StatisticsForCompletedCorrespondencesUserInfo"].Encode())';
        Resources.StatisticsForInProgressTransfersUserInfo = '@Html.Raw(Localizer["StatisticsForInProgressTransfersUserInfo"].Encode())';
        Resources.StatisticsForCompletedTransfersUserInfo = '@Html.Raw(Localizer["StatisticsForCompletedTransfersUserInfo"].Encode())';
        Resources.PrintReport = '@Html.Raw(Localizer["PrintReport"].Encode())';
        Resources.ExportReportToExcel = '@Html.Raw(Localizer["ExportReportToExcel"].Encode())';
        Resources.ExportReportToPdf = '@Html.Raw(Localizer["ExportReportToPdf"].Encode())';
        Resources.VisibleForAllStructure = '@Html.Raw(Localizer["VisibleForAllStructure"].Encode())';
        Resources.Completing = '@Html.Raw(Localizer["Completing"].Encode())';
        Resources.CompleteCorrespondenceConfirmation = '@Html.Raw(Localizer["CompleteCorrespondenceConfirmation"].Encode())';
        Resources.CompleteCorrespondenceSuccessfully = '@Html.Raw(Localizer["CompleteCorrespondenceSuccessfully"].Encode())';
        Resources.TransferCompletionStatistics = '@Html.Raw(Localizer["TransferCompletionStatistics"].Encode())';
        Resources.TransferCompletionStatisticsInfo = '@Html.Raw(Localizer["TransferCompletionStatisticsInfo"].Encode())';
        Resources.AverageDurationForCorrespondenceCompletion = '@Html.Raw(Localizer["AverageDurationForCorrespondenceCompletion"].Encode())';
        Resources.AveragePerYear = '@Html.Raw(Localizer["AveragePerYear"].Encode())';
        Resources.AverageDays = '@Html.Raw(Localizer["AverageDays"].Encode())';
        Resources.TotalAverage = '@Html.Raw(Localizer["TotalAverage"].Encode())';
        Resources.Days = '@Html.Raw(Localizer["Days"].Encode())';
        Resources.AveragePerStructure = '@Html.Raw(Localizer["AveragePerStructure"].Encode())';
        Resources.CompareToTotalAverage = '@Html.Raw(Localizer["CompareToTotalAverage"].Encode())';
        Resources.YearlyGraph = '@Html.Raw(Localizer["YearlyGraph"].Encode())';
        Resources.AveragePerUser = '@Html.Raw(Localizer["AveragePerUser"].Encode())';
        Resources.PerformancePerStructure = '@Html.Raw(Localizer["PerformancePerStructure"].Encode())';
        Resources.BestPerformance = '@Html.Raw(Localizer["BestPerformance"].Encode())';
        Resources.WorstPerformance = '@Html.Raw(Localizer["WorstPerformance"].Encode())';
        Resources.Average = '@Html.Raw(Localizer["Average"].Encode())';
        Resources.AverageDurationForCorrespondenceDelay = '@Html.Raw(Localizer["AverageDurationForCorrespondenceDelay"].Encode())';
        Resources.AverageDurationForTransferCompletion = '@Html.Raw(Localizer["AverageDurationForTransferCompletion"].Encode())';
        Resources.AverageDurationForTransferDelay = '@Html.Raw(Localizer["AverageDurationForTransferDelay"].Encode())';
        Resources.Dashboard = '@Html.Raw(Localizer["Dashboard"].Encode())';
        Resources.InProgressTransfers = '@Html.Raw(Localizer["InProgressTransfers"].Encode())';
        Resources.CompletedTransfers = '@Html.Raw(Localizer["CompletedTransfers"].Encode())';
        Resources.OperationByUser = '@Html.Raw(Localizer["OperationByUser"].Encode())';
        Resources.Reports = '@Html.Raw(Localizer["Reports"].Encode())';
        Resources.OperationByCorrespondence = '@Html.Raw(Localizer["OperationByCorrespondence"].Encode())';
        Resources.AverageDurationForTransferDelay = '@Html.Raw(Localizer["AverageDurationForTransferDelay"].Encode())';
        Resources.CompareToStructureTotalAverage = '@Html.Raw(Localizer["CompareToStructureTotalAverage"].Encode())';
        Resources.RegisteredBy = '@Html.Raw(Localizer["RegisteredBy"].Encode())';
        Resources.Priority = '@Html.Raw(Localizer["Priorities"].Encode())';
        Resources.CopyToReceivingEntity = '@Html.Raw(Localizer["CopyToReceivingEntity"].Encode())';
        Resources.CopyToSendingEntity = '@Html.Raw(Localizer["CopyToSendingEntity"].Encode())';
        Resources.ExportReportToPdf = '@Html.Raw(Localizer["ExportReportToPdf"].Encode())';
        Resources.InProgressTransfers = '@Html.Raw(Localizer["InProgressTransfers"].Encode())';
        Resources.StatisticalCorrespondences = '@Html.Raw(Localizer["StatisticalCorrespondences"].Encode())';
        Resources.ReceivedTransfers = '@Html.Raw(Localizer["ReceivedTransfers"].Encode())';
        Resources.SentTransfers = '@Html.Raw(Localizer["SentTransfers"].Encode())';
        Resources.CorrespondenceDetail = '@Html.Raw(Localizer["CorrespondenceDetail"].Encode())';
        Resources.Correspondence = '@Html.Raw(Localizer["Correspondence"].Encode())';
        Resources.RegisterDate = '@Html.Raw(Localizer["RegisterDate"].Encode())';
        Resources.CarbonCopies = '@Html.Raw(Localizer["CarbonCopies"].Encode())';
        Resources.TransferDate = '@Html.Raw(Localizer["TransferDate"].Encode())';
        Resources.YouNeedToUseTheFilterToViewData = '@Html.Raw(Localizer["YouNeedToUseTheFilterToViewData"].Encode())';
        Resources.InProgressCorrespondences = '@Html.Raw(Localizer["InProgressCorrespondences"].Encode())';
        Resources.CompletedCorrespondences = '@Html.Raw(Localizer["CompletedCorrespondences"].Encode())';
        Resources.PM = '@Html.Raw(Localizer["pm"].Encode())';
        Resources.AM = '@Html.Raw(Localizer["am"].Encode())';
        Resources.Yesterday = '@Html.Raw(Localizer["Yesterday"].Encode())';
        Resources.YouNeedToUseTheFilterToViewData = '@Html.Raw(Localizer["YouNeedToUseTheFilterToViewData"].Encode())';
        Resources.ResultsPerUser = '@Html.Raw(Localizer["ResultsPerUser"].Encode())';
        Resources.ReplyBy = '@Html.Raw(Localizer["ReplyBy"].Encode())';
        Resources.AuditTrailMustBeActivated = '@Html.Raw(Localizer["AuditTrailMustBeActivated"].Encode())';
        Resources.BW = '@Html.Raw(Localizer["BW"].Encode())';
        Resources.RGB = '@Html.Raw(Localizer["RGB"].Encode())';
        Resources.Customize = '@Html.Raw(Localizer["Customize"].Encode())';
        Resources.CustomizeHomePage = '@Html.Raw(Localizer["CustomizeHomePage"].Encode())';
        Resources.HomePageMaximumWidget = '@Html.Raw(Localizer["HomePageMaximumWidget"].Encode())';
        Resources.Widget = '@Html.Raw(Localizer["Widget"].Encode())';
        Resources.ResetConfirmation = '@Html.Raw(Localizer["ResetConfirmation"].Encode())';
        Resources.MaxWidgetExceedLimit = '@Html.Raw(Localizer["MaxWidgetExceedLimit"].Encode())';
        Resources.CountPerCategoryAndStatusInfo = '@Html.Raw(Localizer["CountPerCategoryAndStatusInfo"].Encode())';
        Resources.DesignMode = '@Html.Raw(Localizer["DesignMode"].Encode())';
        Resources.EntityGroup = '@Html.Raw(Localizer["EntityGroup"].Encode())';
        Resources.GroupsInUse = '@Html.Raw(Localizer["GroupsInUse"].Encode())';
        Resources.HasLockedAttachmentsByUser = '@Html.Raw(Localizer["HasLockedAttachmentsByUser"].Encode())';
        Resources.OriginalDocumentLockedByUser = '@Html.Raw(Localizer["OriginalDocumentLockedByUser"].Encode())';
        Resources.TransferHasDifferentOwnerOrIsCarbonCopy = '@Html.Raw(Localizer["TransferHasDifferentOwnerOrIsCarbonCopy"].Encode())';
        Resources.Broadcast = '@Html.Raw(Localizer["Broadcast"].Encode())';
        Resources.CheckAtleastOnePurposeCCed = '@Html.Raw(Localizer["CheckAtleastOnePurposeCCed"].Encode())';
        Resources.TransferIsOwnedByAnotherUser = '@Html.Raw(Localizer["TransferIsOwnedByAnotherUser"].Encode())';
        Resources.AllSelectedItemsAreBroadcast = '@Html.Raw(Localizer["AllSelectedItemsAreBroadcast"].Encode())';
        Resources.AllSelectedItemsAreBroadcastAndCarbonCopies = '@Html.Raw(Localizer["AllSelectedItemsAreBroadcastAndCarbonCopies"].Encode())';
        Resources.FileNameTooBig = '@Html.Raw(Localizer["FileNameTooBig"].Encode())';
        Resources.InValidValueLength = '@Html.Raw(Localizer["InValidValueLength"].Encode())';
        Resources.SelectAtLeastOneNonCCTransfer = '@Html.Raw(Localizer["SelectAtLeastOneNonCCTransfer"].Encode())';
        Resources.AllSelectedTransfersAreCCconfirmation = '@Html.Raw(Localizer["AllSelectedTransfersAreCCconfirmation"].Encode())';
        Resources.CalendarType = '@Html.Raw(Localizer["CalendarType"].Encode())';
        Resources.Gregorian = '@Html.Raw(Localizer["Gregorian"].Encode())';
        Resources.HijriUmmAlQura = '@Html.Raw(Localizer["HijriUmmAlQura"].Encode())';
        Resources.HijriTabular = '@Html.Raw(Localizer["HijriTabular"].Encode())';
        Resources.HijriCivil = '@Html.Raw(Localizer["HijriCivil"].Encode())';
        Resources.EnableOCR = '@Html.Raw(Localizer["EnableOCR"].Encode())';
        Resources.OCRContent = '@Html.Raw(Localizer["OCRContent"].Encode())';
        Resources.EnableAttributeEdit = '@Html.Raw(Localizer["EnableAttributeEdit"].Encode())';
        Resources.EnableAttributeEditSign = '@Html.Raw(Localizer["EnableAttributeEditSign"].Encode())';
        Resources.MustBeAllowEditAttributeAndNotSigned = '@Html.Raw(Localizer["MustBeAllowEditAttributeAndNotSigned"].Encode())';
        Resources.WidgetMinMaxNumber = '@Html.Raw(Localizer["WidgetMinMaxNumber"].Encode())';
        Resources.LikeAdditionQueryTypePorperty = '@Html.Raw(Localizer["LikeAdditionQueryTypePorperty"].Encode())';
        Resources.MatchQueryPropertyBrief = '@Html.Raw(Localizer["MatchQueryPropertyBrief"].Encode())';
        Resources.MatchPhrasePropertyBrief = '@Html.Raw(Localizer["MatchPhrasePropertyBrief"].Encode())';
        Resources.InvalidEmptyFile = '@Html.Raw(Localizer["InvalidEmptyFile"].Encode())';
        Resources.ContainersOverlappingHasExceededLimit = '@Html.Raw(Localizer["ContainersOverlappingHasExceededLimit"].Encode())';
        Resources.ConvertToPdf = '@Html.Raw(Localizer["ConvertToPdf"].Encode())';
        Resources.GenerateLetterNotComplete = '@Html.Raw(Localizer["GenerateLetterNotComplete"].Encode())';
        Resources.ConvertedSuccessfully = '@Html.Raw(Localizer["ConvertedSuccessfully"].Encode())';
        Resources.GenerateLetter = '@Html.Raw(Localizer["GenerateLetter"].Encode())';
        Resources.GeneratedLetterSuccessfully = '@Html.Raw(Localizer["GeneratedLetterSuccessfully"].Encode())';
        Resources.GenerateLetterForeachReceivingEntity = '@Html.Raw(Localizer["GenerateLetterForeachReceivingEntity"].Encode())';
        Resources.GenerateLetterForeachCCed = '@Html.Raw(Localizer["GenerateLetterForeachCCed"].Encode())';
        Resources.SelectReceivingEntityOrCarbonCopy = '@Html.Raw(Localizer["SelectReceivingEntityOrCarbonCopy"].Encode())';
        Resources.DeleteFolder = '@Html.Raw(Localizer["DeleteFolder"].Encode())';
        Resources.AtLeastOneReceivingEntityOrCarbonCopy = '@Html.Raw(Localizer["AtLeastOneReceivingEntityOrCarbonCopy"].Encode())';
        Resources.GenerateLetterMessage = '@Html.Raw(Localizer["GenerateLetterMessage"].Encode())';
        Resources.CantRestoreVersionOfSignedDocument = '@Html.Raw(Localizer["CantRestoreVersionOfSignedDocument"].Encode())';
        Resources.CantReplaceSignedDocument = '@Html.Raw(Localizer["CantReplaceSignedDocument"].Encode())';
        Resources.FormioFieldWarningMsg  = '@Html.Raw(Localizer["FormioFieldWarningMsg"].Encode())';
        Resources.MobileConfiguration = '@Html.Raw(Localizer["MobileConfiguration"].Encode())';
        Resources.Previewing = '@Html.Raw(Localizer["Previewing"].Encode())';
        Resources.SendEmail = '@Html.Raw(Localizer["SendEmail"].Encode())';
        Resources.Comment = '@Html.Raw(Localizer["Comment"].Encode())';
        Resources.DeleteConfirmationCorrespondence = '@Html.Raw(Localizer["DeleteConfirmationCorrespondence"].Encode())';
        Resources.FirstNameAr = '@Html.Raw(Localizer["FirstNameAr"].Encode())';
        Resources.MiddleNameAr = '@Html.Raw(Localizer["MiddleNameAr"].Encode())';
        Resources.LastNameAr = '@Html.Raw(Localizer["LastNameAr"].Encode())';
        Resources.FirstNameFr = '@Html.Raw(Localizer["FirstNameFr"].Encode())';
        Resources.MiddleNameFr = '@Html.Raw(Localizer["MiddleNameFr"].Encode())';
        Resources.LastNameFr = '@Html.Raw(Localizer["LastNameFr"].Encode())';
        Resources.ManageCorrespondence = '@Html.Raw(Localizer["ManageCorrespondence"].Encode())';
        Resources.ReturnToDraftConfirmation = '@Html.Raw(Localizer["ReturnToDraftConfirmation"].Encode())';
        Resources.ReturnToDraft = '@Html.Raw(Localizer["ReturnToDraft"].Encode())';
        Resources.CancelConfirmation = '@Html.Raw(Localizer["CancelConfirmation"].Encode())';
        Resources.NoTemplatesFound =  '@Html.Raw(Localizer["NoTemplatesFound"].Encode())';
        Resources.SignatureRequired =  '@Html.Raw(Localizer["SignatureRequired"].Encode())';
        Resources.ShowOldCorrespondence = '@Html.Raw(Localizer["ShowOldCorrespondence"].Encode())';
        Resources.StartDate = '@Html.Raw(Localizer["StartDate"].Encode())';
        Resources.Sign = '@Html.Raw(Localizer["Sign"].Encode())';
        Resources.ReAssign = '@Html.Raw(Localizer["ReAssign"].Encode())';
        Resources.ReassignConfirmation = '@Html.Raw(Localizer["ReassignConfirmation"].Encode())';
        Resources.ExceptionList = '@Html.Raw(Localizer["ExceptionList"].Encode())';
        Resources.AutoForward = '@Html.Raw(Localizer["AutoForward"].Encode())';
        Resources.ConfirmChangeStructure = '@Html.Raw(Localizer["ConfirmChangeStructure"].Encode())';        
        Resources.MaintainTransfer = '@Html.Raw(Localizer["MaintainTransfer"].Encode())';
        Resources.EnablePerStructure = '@Html.Raw(Localizer["EnablePerStructure"].Encode())';
        Resources.MoveUp = '@Html.Raw(Localizer["MoveUp"].Encode())';
        Resources.MoveDown = '@Html.Raw(Localizer["MoveDown"].Encode())';
        Resources.AttachmentSecurity = '@Html.Raw(Localizer["AttachmentSecurity"].Encode())';
        Resources.accessedToAllUsers = '@Html.Raw(Localizer["accessedToAllUsers"].Encode())';
        Resources.TheAttachmentIsCheckedOutBy = '@Html.Raw(Localizer["TheAttachmentIsCheckedOutBy"].Encode())';
        Resources.DoYouWantToDiscardCheckoutAndContinue = '@Html.Raw(Localizer["DoYouWantToDiscardCheckoutAndContinue"].Encode())';
        Resources.DuplicateAttachmentSecurity = '@Html.Raw(Localizer["DuplicateAttachmentSecurity"].Encode())';
        Resources.VoiceNote ='@Html.Raw(Localizer["VoiceNote"].Encode())';
        Resources.MoveTransfers = '@Html.Raw(Localizer["MoveTransfers"].Encode())';
        Resources.Structure = '@Html.Raw(Localizer["Structure"].Encode())';
        Resources.Migrate = '@Html.Raw(Localizer["Migrate"].Encode())';
        Resources.MigrateTransfersConfirmation = '@Html.Raw(Localizer["MigrateTransfersConfirmation"].Encode())';
        Resources.MigrateTransfersEmpty = '@Html.Raw(Localizer["MigrateTransfersEmpty"].Encode())';
        Resources.ErrorInDiscardCheckOut = '@Html.Raw(Localizer["ErrorInDiscardCheckOut"].Encode())';
        Resources.FilesNeededToDiscardCheckOut = '@Html.Raw(Localizer["FilesNeededToDiscardCheckOut"].Encode())';
        Resources.UserAddedSuccessfully = '@Html.Raw(Localizer["UserAddedSuccessfully"].Encode())';
        Resources.AddUser ='@Html.Raw(Localizer["AddUser"].Encode())';
        Resources.ConfigureSignature = '@Html.Raw(Localizer["ConfigureSignature"].Encode())';
        Resources.AddNewSignature='@Html.Raw(Localizer["AddNewSignature"].Encode())';
        Resources.DeleteUserWithSignatures = '@Html.Raw(Localizer["DeleteUserWithSignatures"].Encode())';
        Resources.UserDeletedSuccessfully = '@Html.Raw(Localizer["UserDeletedSuccessfully"].Encode())';
        Resources.SignAndSend = '@Html.Raw(Localizer["SignAndSend"].Encode())';
        Resources.SignAndTransfer = '@Html.Raw(Localizer["SignAndTransfer"].Encode())';
        Resources.SignAndReplyToStructure = '@Html.Raw(Localizer["SignAndReplyToStructure"].Encode())';
        Resources.SignAndReplyToUser = '@Html.Raw(Localizer["SignAndReplyToUser"].Encode())';
        Resources.StartWorkflow = '@Html.Raw(Localizer["StartWorkflow"].Encode())';
        Resources.Workflow = '@Html.Raw(Localizer["Workflow"].Encode())';
        Resources.NoWorkflowStepsAdded = '@Html.Raw(Localizer["NoWorkflowStepsAdded"].Encode())';
        Resources.Return = '@Html.Raw(Localizer["Return"].Encode())';
        Resources.Proceed = '@Html.Raw(Localizer["Proceed"].Encode())';
        Resources.Resume = '@Html.Raw(Localizer["Resume"].Encode())';
        Resources.ViewFlow = '@Html.Raw(Localizer["ViewFlow"].Encode())';
        Resources.DeliveryNote = '@Html.Raw(Localizer["DeliveryNote"].Encode())';
        Resources.Reinitiate = '@Html.Raw(Localizer["Reinitiate"].Encode())';
        Resources.NotAllUserHasSignatureRegion = '@Html.Raw(Localizer["NotAllUserHasSignatureRegion"].Encode())';
        Resources.InvalidOrder = '@Html.Raw(Localizer["InvalidOrder"].Encode())';
        Resources.UserAlreadyExists = '@Html.Raw(Localizer["UserAlreadyExists"].Encode())';
        Resources.PrivacyLevel = '@Html.Raw(Localizer["PrivacyLevel"].Encode())';
        Resources.AddAssignedStructures = '@Html.Raw(Localizer["AddAssignedStructures"].Encode())';        
        Resources.DefaultWithGrouping = '@Html.Raw(Localizer["DefaultWithGrouping"].Encode())';
        Resources.NoRowInTable = '@Html.Raw(Localizer["NoRowInTable"].Encode())';
        Resources.CouldNotDeleteDocument = '@Html.Raw(Localizer["CouldNotDeleteDocument"].Encode())';
        Resources.RemoveFromBasket = '@Html.Raw(Localizer["RemoveFromBasket"].Encode())';
        Resources.DeleteBasketConfirmation = '@Html.Raw(Localizer["DeleteBasketConfirmation"].Encode())';
        Resources.BasketNameAlreadyExist = '@Html.Raw(Localizer["BasketNameAlreadyExist"].Encode())';
        Resources.EditBasket = '@Html.Raw(Localizer["EditBasket"].Encode())';
        Resources.DeleteBasket = '@Html.Raw(Localizer["DeleteBasket"].Encode())';
        Resources.Baskets = '@Html.Raw(Localizer["Baskets"].Encode())';
        Resources.AddBasket = '@Html.Raw(Localizer["AddBasket"].Encode())';
        Resources.ReadOnly = '@Html.Raw(Localizer["ReadOnly"].Encode())';
        Resources.Leave = '@Html.Raw(Localizer["Leave"].Encode())';
        Resources.MyBaskets = '@Html.Raw(Localizer["MyBaskets"].Encode())';
        Resources.OtherBaskets = '@Html.Raw(Localizer["OtherBaskets"].Encode())';
        Resources.Committee = '@Html.Raw(Localizer["Committee"].Encode())';
        Resources.ReplyMeetingResolution = '@Html.Raw(Localizer["ReplyMeetingResolution"].Encode())';
        Resources.CreateSepResolution = '@Html.Raw(Localizer["CreateSepResolution"].Encode())';
        Resources.CreateMinutes = '@Html.Raw(Localizer["CreateMinutes"].Encode())';
        Resources.Proceed = '@Html.Raw(Localizer["Proceed"].Encode())';
        Resources.NewMeetingAgenda = '@Html.Raw(Localizer["NewMeetingAgenda"].Encode())';
        Resources.CheckoutFaild = '@Html.Raw(Localizer["CheckoutFaild"].Encode())';
        Resources.SignFailed = '@Html.Raw(Localizer["SignFailed"].Encode())';
        Resources.HasNoAccess = '@Html.Raw(Localizer["HasNoAccess"].Encode())';
        Resources.BasketAttribute = '@Html.Raw(Localizer["BasketAttribute"].Encode())';
        Resources.NotAllowedToAddYourSelf = '@Html.Raw(Localizer["NotAllowedToAddYourSelf"].Encode())';
        Resources.DesignatedPerson = '@Html.Raw(Localizer["DesignatedPerson"].Encode())';
        Resources.Position = '@Html.Raw(Localizer["Position"].Encode())';
        Resources.ExternalStructures = '@Html.Raw(Localizer["ExternalStructures"].Encode())';
        Resources.PositionAr = '@Html.Raw(Localizer["PositionAr"].Encode())';
        Resources.PositionFr = '@Html.Raw(Localizer["PositionFr"].Encode())';
        Resources.FullNameAr = '@Html.Raw(Localizer["FullNameAr"].Encode())';
        Resources.FullNameFr = '@Html.Raw(Localizer["FullNameFr"].Encode())';
        Resources.TitleAr = '@Html.Raw(Localizer["TitleAr"].Encode())';
        Resources.TitleFr = '@Html.Raw(Localizer["TitleFr"].Encode())';
        Resources.Someusersareassignedtoexistingdocuments = '@Html.Raw(Localizer["Someusersareassignedtoexistingdocuments"].Encode())';
        Resources.UnArchiveConfirmation = '@Html.Raw(Localizer["UnArchiveConfirmation"].Encode())';
        Resources.UnArchive = '@Html.Raw(Localizer["UnArchive"].Encode())';
        Resources.LoginProviders = '@Html.Raw(Localizer["LoginProviders"].Encode())';
        Resources.ForceChangePassword = '@Html.Raw(Localizer["ForceChangePassword"].Encode())';
        Resources.PasswordRequirementMessage = '@Html.Raw(Localizer["PasswordRequirementMessage"].Encode())';
        Resources.AddUser = '@Html.Raw(Localizer["AddUser"].Encode())';
        Resources.EditUser = '@Html.Raw(Localizer["EditUser"].Encode())';
        Resources.UserNotFound = '@Html.Raw(Localizer["UserNotFound"].Encode())';
        Resources.UserNameAlreadyExists = '@Html.Raw(Localizer["UserNameAlreadyExists"].Encode())';
        Resources.UserAlreadyExists = '@Html.Raw(Localizer["UserAlreadyExists"].Encode())';
        Resources.SelectGender = '@Html.Raw(Localizer["SelectGender"].Encode())';
        Resources.SelectDefaultStructure = '@Html.Raw(Localizer["SelectDefaultStructure"].Encode())';
        Resources.SelectManager = '@Html.Raw(Localizer["SelectManager"].Encode())';
        Resources.SelectGroups = '@Html.Raw(Localizer["SelectGroups"].Encode())';
        Resources.SelectSystemRole = '@Html.Raw(Localizer["SelectSystemRole"].Encode())';
        Resources.ExistedUser = '@Html.Raw(Localizer["ExistedUser"].Encode())';
        Resources.NewUser = '@Html.Raw(Localizer["NewUser"].Encode())';
        Resources.Metadata = '@Html.Raw(Localizer["Metadata"].Encode())';
        Resources.MiddleName = '@Html.Raw(Localizer["MiddleName"].Encode())';
        Resources.Gender = '@Html.Raw(Localizer["Gender"].Encode())';
        Resources.Manager = '@Html.Raw(Localizer["Manager"].Encode())';
        Resources.Groups = '@Html.Raw(Localizer["Groups"].Encode())';
        Resources.Active = '@Html.Raw(Localizer["Active"].Encode())';
        Resources.Administrator = '@Html.Raw(Localizer["Administrator"].Encode())';
        Resources.ctsAndViewerAppAndRoleIdInParameters = '@Html.Raw(Localizer["ctsAndViewerAppAndRoleIdInParameters"].Encode())';
        Resources.Dynamic = '@Html.Raw(Localizer["Dynamic"].Encode())';
        Resources.CantGenerateReferenceNumber = '@Html.Raw(Localizer["CantGenerateReferenceNumber"].Encode())';
        Resources.GenerateReferenceNumber  = '@Html.Raw(Localizer["GenerateReferenceNumber"].Encode())';
        Resources.basketnode  = '@Html.Raw(Localizer["basketnode"].Encode())';
        Resources.GenerateReferenceNumber  = '@Html.Raw(Localizer["GenerateReferenceNumber"].Encode())';        
        Resources.NoAttachmentToSign = '@Html.Raw(Localizer["NoAttachmentToSign"].Encode())';
        Resources.NoSignatureConfigured = '@Html.Raw(Localizer["NoSignatureConfigured"].Encode())';
        Resources.NotAllowedToAddSameUserToNextStep = '@Html.Raw(Localizer["NotAllowedToAddSameUserToNextStep"].Encode())';
        Resources.NoPermssionCreateMeeting = '@Html.Raw(Localizer["NoPermssionCreateMeeting"].Encode())';
        Resources.ExpandAll = '@Html.Raw(Localizer["ExpandAll"].Encode())';
        Resources.UserNotSynchronized = '@Html.Raw(Localizer["UserNotSynchronized"].Encode())';
        Resources.DuplicatedAssignee = '@Html.Raw(Localizer["DuplicatedAssignee"].Encode())';
        Resources.Assignee = '@Html.Raw(Localizer["Assignee"].Encode())';
        Resources.Assignees = '@Html.Raw(Localizer["Assignees"].Encode())';
        Resources.Permission = '@Html.Raw(Localizer["Permission"].Encode())';
        Resources.RequestToComplete = '@Html.Raw(Localizer["RequestToComplete"].Encode())';
        Resources.RequestToCompleteOneTaskConfirmation = '@Html.Raw(Localizer["RequestToCompleteOneTaskConfirmation"].Encode())';
        Resources.Postpone = '@Html.Raw(Localizer["Postpone"].Encode())';
        Resources.ManageFollowUp = '@Html.Raw(Localizer["ManageFollowUp"].Encode())';
        Resources.Instructions = '@Html.Raw(Localizer["Instructions"].Encode())';
        Resources.SendEmailReminder = '@Html.Raw(Localizer["SendEmailReminder"].Encode())';
        Resources.SendReminder = '@Html.Raw(Localizer["SendReminder"].Encode())';
        Resources.CC = '@Html.Raw(Localizer["CC"].Encode())';
        Resources.RecordNotFound =  '@Html.Raw(Localizer["RecordNotFound"].Encode())';
        Resources.EmailValidationPlaceholder =  '@Html.Raw(Localizer["EmailValidationPlaceholder"].Encode())';
        Resources.MyFollowUp =  '@Html.Raw(Localizer["MyFollowUp"].Encode())';
        Resources.AssignedToMe =  '@Html.Raw(Localizer["AssignedToMe"].Encode())';
        Resources.AssignedToOthers =  '@Html.Raw(Localizer["AssignedToOthers"].Encode())';
        Resources.DepartmentFollowUps =  '@Html.Raw(Localizer["DepartmentFollowUps"].Encode())';
        Resources.NewFollowUp =  '@Html.Raw(Localizer["NewFollowUp"].Encode())';
        Resources.TaskSearch = '@Html.Raw(Localizer["TaskSearch"].Encode())';
        Resources.CancelTaskConfirmation = '@Html.Raw(Localizer["CancelTaskConfirmation"].Encode())';
        Resources.CancelTask = '@Html.Raw(Localizer["CancelTask"].Encode())';
        Resources.CancelTaskNoPermission = '@Html.Raw(Localizer["CancelTaskNoPermission"].Encode())';
        Resources.FollowUpCompleted = '@Html.Raw(Localizer["FollowUpCompleted"].Encode())';
        Resources.DeleteConfirmationFollowUp = '@Html.Raw(Localizer["DeleteConfirmationFollowUp"].Encode())';
        Resources.CannotDeleteRegisteredFollowUpsWarning = '@Html.Raw(Localizer["CannotDeleteRegisteredFollowUpsWarning"].Encode())';
        Resources.AssignedTo = '@Html.Raw(Localizer["AssignedTo"].Encode())';
        Resources.NoAttachmentToSign = '@Html.Raw(Localizer["NoAttachmentToSign"].Encode())';
        Resources.NoSignatureConfigured = '@Html.Raw(Localizer["NoSignatureConfigured"].Encode())';
        Resources.CheckinFailed = '@Html.Raw(Localizer["CheckinFailed"].Encode())';
        Resources.AllSelectedItemsAreReadOnlyOrHasWorkflow = '@Html.Raw(Localizer["AllSelectedItemsAreReadOnlyOrHasWorkflow"].Encode())';
        Resources.EmailAlreadyExists = '@Html.Raw(Localizer["EmailAlreadyExists"].Encode())';
        Resources.NewCorrespondence = '@Html.Raw(Localizer["NewCorrespondence"].Encode())';
        Resources.SynchronizUsersData = '@Html.Raw(Localizer["SynchronizUsersData"].Encode())';
        Resources.NewBasket = '@Html.Raw(Localizer["NewBasket"].Encode())';
        Resources.ExistBasket = '@Html.Raw(Localizer["ExistBasket"].Encode())';
        Resources.AddToBasket = '@Html.Raw(Localizer["AddToBasket"].Encode())';
        Resources.SureToSignAndProceed = '@Html.Raw(Localizer["SureToSignAndProceed"].Encode())';
        Resources.SureToProceed = '@Html.Raw(Localizer["SureToProceed"].Encode())';
        Resources.SureToReturn = '@Html.Raw(Localizer["SureToReturn"].Encode())';
        Resources.FilesNeededToDiscardCheckOutByViewerAdmin = '@Html.Raw(Localizer["FilesNeededToDiscardCheckOutByViewerAdmin"].Encode())';
        Resources.TextColor = '@Html.Raw(Localizer["TextColor"].Encode())';
        Resources.UserFolder = '@Html.Raw(Localizer["UserFolder"].Encode())';
        Resources.MyNodes = '@Html.Raw(Localizer["MyNodes"].Encode())';
        Resources.EnableUnreadCount = '@Html.Raw(Localizer["EnableUnreadCount"].Encode())';
        Resources.UnreadCountJsFunctionName  = '@Html.Raw(Localizer["UnreadCountJsFunctionName"].Encode())';
        Resources.CopiedToClipboard  = '@Html.Raw(Localizer["CopiedToClipboard"].Encode())';
        Resources.ManageDepartmentUsers  = '@Html.Raw(Localizer["ManageDepartmentUsers"].Encode())';
        Resources.Activitydetails = '@Html.Raw(Localizer["Activitydetails"].Encode())';
        Resources.General = '@Html.Raw(Localizer["General"].Encode())';
        Resources.Advanced = '@Html.Raw(Localizer["Advanced"].Encode())';
        Resources.IsCompletedFollowUp = '@Html.Raw(Localizer["IsCompletedFollowUp"].Encode())';
        Resources.Reopen = '@Html.Raw(Localizer["Reopen"].Encode())';
        Resources.ReopenConfirmation = '@Html.Raw(Localizer["ReopenConfirmation"].Encode())';
        Resources.FavoriteContacts = '@Html.Raw(Localizer["FavoriteContacts"].Encode())';
        Resources.reportsnotesfollowup = '@Html.Raw(Localizer["reportsnotesfollowup"].Encode())';
        Resources.ParentNodeName = '@Html.Raw(Localizer["ParentNodeName"].Encode())';
        Resources.reportInbox = '@Html.Raw(Localizer["reportInbox"].Encode())';
        Resources.DraftReport ='@Html.Raw(Localizer["DraftReport"].Encode())';
        Resources.CompletedReport = '@Html.Raw(Localizer["CompletedReport"].Encode())';
        Resources.MyRequestReport = '@Html.Raw(Localizer["MyRequestReport"].Encode())';
        Resources.SentReport = '@Html.Raw(Localizer["SentReport"].Encode())';
        Resources.ClosedReport = '@Html.Raw(Localizer["ClosedReport"].Encode())';
        Resources.ListStructureUsers = '@Html.Raw(Localizer["ListStructureUsers"].Encode())';
        Resources.ListAllStructureUsers = '@Html.Raw(Localizer["ListAllStructureUsers"].Encode())';
        Resources.CompleteFollowUp = '@Html.Raw(Localizer["CompleteFollowUp"].Encode())';
        Resources.DuplicateType = '@Html.Raw(Localizer["DuplicateType"].Encode())';
        Resources.AllPages = '@Html.Raw(Localizer["AllPages"].Encode())';
        Resources.Duplicate = '@Html.Raw(Localizer["Duplicate"].Encode())';
        Resources.Invalidpagenumber = '@Html.Raw(Localizer["Invalidpagenumber"].Encode())';
        Resources.FollowUp = '@Html.Raw(Localizer["FollowUp"].Encode())';
        Resources.ShowFavoriteTransfers = '@Html.Raw(Localizer["ShowFavoriteTransfers"].Encode())';
        Resources.Add = '@Html.Raw(Localizer["Add"].Encode())';
        Resources.CustomizeColumns = '@Html.Raw(Localizer["CustomizeColumns"].Encode())';
        Resources.OrganizationName = '@Html.Raw(Localizer["OrganizationName"].Encode())';
        Resources.Categories = '@Html.Raw(Localizer["Categories"].Encode())';
        Resources.CustomViewerMode = '@Html.Raw(Localizer["CustomViewerMode"].Encode())';
        Resources.Simple = '@Html.Raw(Localizer["Simple"].Encode())';
        Resources.CantCompleteFollowUp = '@Html.Raw(Localizer["CantCompleteFollowUp"].Encode())';
        Resources.ReferenceAlreadyExists = '@Html.Raw(Localizer["ReferenceAlreadyExists"].Encode())';
        Resources.GenerateBarcodeMsg = '@Html.Raw(Localizer["GenerateBarcodeMsg"].Encode())';
        Resources.DynamicProcedure = '@Html.Raw(Localizer["DynamicProcedure"].Encode())';
        Resources.DynamicProcedureName = '@Html.Raw(Localizer["DynamicProcedureName"].Encode())';
        Resources.CompleteAndArchive = '@Html.Raw(Localizer["CompleteAndArchive"].Encode())';
        Resources.CompleteAndArchiveManual = '@Html.Raw(Localizer["CompleteAndArchiveManual"].Encode())';
        Resources.ArchivingFailed = '@Html.Raw(Localizer["ArchivingFailed"].Encode())';
        Resources.Expand = '@Html.Raw(Localizer["Expand"].Encode())';
        Resources.Period = '@Html.Raw(Localizer["Period"].Encode())';
        Resources.OrderAlreadyExists = '@Html.Raw(Localizer["OrderAlreadyExists"].Encode())';
        Resources.Close = '@Html.Raw(Localizer["Close"].Encode())';
        Resources.TransfersSentToStructure = '@Html.Raw(Localizer["TransfersSentToStructure"].Encode())';
        Resources.DraftInbox = '@Html.Raw(Localizer["DraftInbox"].Encode())';
        Resources.AttachmentProperties = '@Html.Raw(Localizer["AttachmentProperties"].Encode())';
        Resources.ManageStructureUsersCorrespondences='@Html.Raw(Localizer["ManageStructureUsersCorrespondences"].Encode())';
        Resources.MessageType= '@Html.Raw(Localizer["MessageType"].Encode())';
        Resources.FillNotes='@Html.Raw(Localizer["FillNotes"].Encode())';
        Resources.DocumentDate = '@Html.Raw(Localizer["DocumentDate"].Encode())';
        Resources.Mandatory = '@Html.Raw(Localizer["Mandatory"].Encode())';
        Resources.PleaseSelectAttachments = '@Html.Raw(Localizer["PleaseSelectAttachments"].Encode())';
        Resources.WithTemplate = '@Html.Raw(Localizer["WithTemplate"].Encode())';
        Resources.NoItemsSelected = '@Html.Raw(Localizer["NoItemsSelected"].Encode())';
        Resources.SelectPurpose = '@Html.Raw(Localizer["SelectPurpose"].Encode())';
        Resources.GridView = '@Html.Raw(Localizer["GridView"].Encode())';
        Resources.Timeline = '@Html.Raw(Localizer["Timeline"].Encode())';
        Resources.Both = '@Html.Raw(Localizer["Both"].Encode())';
        Resources.UseCurrentDepartment = '@Html.Raw(Localizer["UseCurrentDepartment"].Encode())';
        Resources.DisableField = '@Html.Raw(Localizer["DisableField"].Encode())';
        Resources.StructureOption = '@Html.Raw(Localizer["StructureOption"].Encode())';
        Resources.PreviewBeforeSign='@Html.Raw(Localizer["PreviewBeforeSign"].Encode())';
        Resources.adminlog = '@Html.Raw(Localizer["adminlog"].Encode())';
        Resources.adminlogdetails = '@Html.Raw(Localizer["adminlogdetails"].Encode())';
        Resources.orignalValue = '@Html.Raw(Localizer["orignalValue"].Encode())';
        Resources.NewValue = '@Html.Raw(Localizer["NewValue"].Encode())';
        Resources.ChooseSign = '@Html.Raw(Localizer["ChooseSign"].Encode())';
        Resources.IsLinkedToIncoming='@Html.Raw(Localizer["IsLinkedToIncoming"].Encode())';
        Resources.OutgoingFromDepartment='@Html.Raw(Localizer["OutgoingFromDepartment"].Encode())';
        Resources.Department='@Html.Raw(Localizer["Department"].Encode())';
        Resources.InvalidSubject = '@Html.Raw(Localizer["InvalidSubject"].Encode())';
        Resources.SignOnly = '@Html.Raw(Localizer["SignOnly"].Encode())';
        Resources.StructuresTemplates='@Html.Raw(Localizer["StructuresTemplates"].Encode())';
        Resources.PersonalTemplates='@Html.Raw(Localizer["PersonalTemplates"].Encode())';
        Resources.TemplatesManagement='@Html.Raw(Localizer["TemplatesManagement"].Encode())';
        Resources.RequiredField = '@Html.Raw(Localizer["RequiredField"].Encode())';
        Resources.CopySpecificVersion = '@Html.Raw(Localizer["CopySpecificVersion"].Encode())';
        Resources.CopyCurrentVersion = '@Html.Raw(Localizer["CopyCurrentVersion"].Encode())';
        Resources.CopyOptions = '@Html.Raw(Localizer["CopyOptions"].Encode())';
        Resources.Entities = '@Html.Raw(Localizer["Entities"].Encode())';
        Resources.DefaultPurposeAlreadyExists = '@Html.Raw(Localizer["DefaultPurposeAlreadyExists"].Encode())';
        Resources.DefaultPurposeForSignature = '@Html.Raw(Localizer["DefaultPurposeForSignature"].Encode())';
        Resources.ForSignature = '@Html.Raw(Localizer["ForSignature"].Encode())';
        Resources.DefaultPurpose = '@Html.Raw(Localizer["DefaultPurpose"].Encode())';
        Resources.AddCCAndSend='@Html.Raw(Localizer["AddCCAndSend"].Encode())';
        Resources.SendCopies='@Html.Raw(Localizer["SendCopies"].Encode())';
        Resources.OutgoingRequests='@Html.Raw(Localizer["OutgoingRequests"].Encode())';
        Resources.RequestStatus='@Html.Raw(Localizer["RequestStatus"].Encode())';
        Resources.Pending='@Html.Raw(Localizer["Pending"].Encode())';
        Resources.RejectRequestConfirmation='@Html.Raw(Localizer["RejectRequestConfirmation"].Encode())';
        Resources.Reject='@Html.Raw(Localizer["Reject"].Encode())';
        Resources.Accept='@Html.Raw(Localizer["Accept"].Encode())';
        Resources.RejectReason='@Html.Raw(Localizer["RejectReason"].Encode())';
        Resources.ExportedRequests='@Html.Raw(Localizer["ExportedRequests"].Encode())';
        Resources.IsHasNote='@Html.Raw(Localizer["IsHasNote"].Encode())';
        Resources.FollowUpStatus='@Html.Raw(Localizer["FollowUpStatus"].Encode())';
        Resources.FollowedUpBy='@Html.Raw(Localizer["FollowedUpBy"].Encode())';
        Resources.KeyWord='@Html.Raw(Localizer["KeyWord"].Encode())';
        Resources.CreateFollowUp='@Html.Raw(Localizer["CreateFollowUp"].Encode())';
        Resources.IsPrivate = '@Html.Raw(Localizer["IsPrivate"].Encode())';
        Resources.CopyCorrespondence = '@Html.Raw(Localizer["CopyCorrespondence"].Encode())';
        Resources.CreateFollowUpNote = '@Html.Raw(Localizer["CreateFollowUpNote"].Encode())';
        Resources.FollowUpExistsForSameUser = '@Html.Raw(Localizer["FollowUpExistsForSameUser"].Encode())';
        Resources.UserCantAccessFollowUp = '@Html.Raw(Localizer["UserCantAccessFollowUp"].Encode())';
        Resources.Postponement = '@Html.Raw(Localizer["Postponement"].Encode())';
        Resources.OpenTransfer = '@Html.Raw(Localizer["OpenTransfer"].Encode())';
        Resources.PostponementFollowUp = '@Html.Raw(Localizer["PostponementFollowUp"].Encode())';
        Resources.CancelFollowUp = '@Html.Raw(Localizer["CancelFollowUp"].Encode())';
        Resources.FollowUpTeamEmptyAndNotPrivate = '@Html.Raw(Localizer["FollowUpTeamEmptyAndNotPrivate"].Encode())';
        Resources.AddFollowUpUser = '@Html.Raw(Localizer["AddFollowUpUser"].Encode())';
        Resources.RemoveFollowUpUser = '@Html.Raw(Localizer["RemoveFollowUpUser"].Encode())';
        Resources.PostponeFollowUp = '@Html.Raw(Localizer["PostponeFollowUp"].Encode())';
        Resources.LinkedCorrespondenceLevels = '@Html.Raw(Localizer["LinkedCorrespondenceLevels"].Encode())';
        Resources.LinkedCorrespondenceLevelsMax = '@Html.Raw(Localizer["LinkedCorrespondenceLevelsMax"].Encode())';
        Resources.SendingRules='@Html.Raw(Localizer["SendingRules"].Encode())';
        Resources.DashboardAssignedStructures='@Html.Raw(Localizer["DashboardAssignedStructures"].Encode())';
        Resources.AssignedStructures='@Html.Raw(Localizer["AssignedStructures"].Encode())';
        Resources.AddRemoveSendingRules='@Html.Raw(Localizer["AddRemoveSendingRules"].Encode())';
        Resources.SecureUserId='@Html.Raw(Localizer["SecureUserId"].Encode())';
        Resources.EditSecureUserId='@Html.Raw(Localizer["EditSecureUserId"].Encode())';
        Resources.EditAfterSign='@Html.Raw(Localizer["EditAfterSign"].Encode())';
        Resources.CreateNewIncomingCorrespondence='@Html.Raw(Localizer["CreateNewIncomingCorrespondence"].Encode())';
        Resources.CantCancelFollowUp='@Html.Raw(Localizer["CantCancelFollowUp"].Encode())';
        Resources.PostponeConfirmation='@Html.Raw(Localizer["PostponeConfirmation"].Encode())';
        Resources.FollowUpSendingEntity='@Html.Raw(Localizer["FollowUpSendingEntity"].Encode())';
        Resources.FollowUpReceivingEntity='@Html.Raw(Localizer["FollowUpReceivingEntity"].Encode())';
        Resources.FollowUpPriority='@Html.Raw(Localizer["FollowUpPriority"].Encode())';
        Resources.FollowUpSubject='@Html.Raw(Localizer["FollowUpSubject"].Encode())';
        Resources.FollowUpIsPrivate='@Html.Raw(Localizer["FollowUpIsPrivate"].Encode())';
        Resources.SubmitEventConfirmation='@Html.Raw(Localizer["SubmitEventConfirmation"].Encode())';
        Resources.SubmitInstructionConfirmation='@Html.Raw(Localizer["SubmitInstructionConfirmation"].Encode())';
        Resources.submitTeamConfirmation='@Html.Raw(Localizer["submitTeamConfirmation"].Encode())';
        Resources.FollowUpUsers='@Html.Raw(Localizer["FollowUpUsers"].Encode())';
        Resources.FollowUpDetails='@Html.Raw(Localizer["FollowUpDetails"].Encode())';
        Resources.InvalidFollowUpUserId='@Html.Raw(Localizer["InvalidFollowUpUserId"].Encode())';
        Resources.FollowUpUserNotFound='@Html.Raw(Localizer["FollowUpUserNotFound"].Encode())';
        Resources.FollowUpNotFound='@Html.Raw(Localizer["FollowUpNotFound"].Encode())';
        Resources.EditFollowUpUserRole='@Html.Raw(Localizer["EditFollowUpUserRole"].Encode())';
        Resources.YouAreNotFollowUpOwner='@Html.Raw(Localizer["YouAreNotFollowUpOwner"].Encode())';
        Resources.CantChangeFollowUpCreatorRole='@Html.Raw(Localizer["CantChangeFollowUpCreatorRole"].Encode())';
        Resources.WithNote='@Html.Raw(Localizer["WithNote"].Encode())';
        Resources.Postponed='@Html.Raw(Localizer["Postponed"].Encode())';
        Resources.Overdued='@Html.Raw(Localizer["Overdued"].Encode())';
        Resources.DocumentCreatedDate='@Html.Raw(Localizer["DocumentCreatedDate"].Encode())';
        Resources.Event='@Html.Raw(Localizer["Event"].Encode())';
        Resources.EventDate='@Html.Raw(Localizer["EventDate"].Encode())';
        Resources.TransferredTo='@Html.Raw(Localizer["TransferredTo"].Encode())';
        Resources.TransferredDate='@Html.Raw(Localizer["TransferredDate"].Encode())';
        Resources.ResponsibleUser='@Html.Raw(Localizer["ResponsibleUser"].Encode())';
        Resources.Notes='@Html.Raw(Localizer["Notes"].Encode())';
        Resources.ModifiedByUSer='@Html.Raw(Localizer["ModifiedByUSer"].Encode())';
        Resources.LastModifiedDate='@Html.Raw(Localizer["LastModifiedDate"].Encode())';
        Resources.FollowUpPanelStatus='@Html.Raw(Localizer["FollowUpPanelStatus"].Encode())';
        Resources.FollowUpCreatedByUser='@Html.Raw(Localizer["FollowUpCreatedByUser"].Encode())';
        Resources.FollowUpPanel='@Html.Raw(Localizer["FollowUpPanel"].Encode())';
        Resources.EditAfterSignConfirmation='@Html.Raw(Localizer["EditAfterSignConfirmation"].Encode())';
        Resources.MyCorrespondences='@Html.Raw(Localizer["MyCorrespondences"].Encode())';
        Resources.IncomingRequests='@Html.Raw(Localizer["IncomingRequests"].Encode())';
        Resources.ReadyForExport='@Html.Raw(Localizer["ReadyForExport"].Encode())';
        Resources.TeamName='@Html.Raw(Localizer["TeamName"].Encode())';
        Resources.TeamMembers='@Html.Raw(Localizer["TeamMembers"].Encode())';
        Resources.FollowUpUsersPrivateNote='@Html.Raw(Localizer["FollowUpUsersPrivateNote"].Encode())';
        Resources.HasNote='@Html.Raw(Localizer["HasNote"].Encode())';
        Resources.OldDueDate='@Html.Raw(Localizer["OldDueDate"].Encode())';
        Resources.NewDueDate='@Html.Raw(Localizer["NewDueDate"].Encode())';
        Resources.DeleteFollowUpCreator='@Html.Raw(Localizer["DeleteFollowUpCreator"].Encode())';
        Resources.UserCantPostponeFollowUp='@Html.Raw(Localizer["UserCantPostponeFollowUp"].Encode())';
        Resources.UserCantCancelFollowUp='@Html.Raw(Localizer["UserCantCancelFollowUp"].Encode())';
        Resources.UserCantCompleteFollowUp='@Html.Raw(Localizer["UserCantCompleteFollowUp"].Encode())';
        Resources.TransferPostponeConfirmation ='@Html.Raw(Localizer["TransferPostponeConfirmation"].Encode())';
        Resources.FollowUpPostponeConfirmation ='@Html.Raw(Localizer["FollowUpPostponeConfirmation"].Encode())';
        Resources.FollowUpCancelConfirmation ='@Html.Raw(Localizer["FollowUpCancelConfirmation"].Encode())';
        Resources.FollowUpCompleteConfirmation ='@Html.Raw(Localizer["FollowUpCompleteConfirmation"].Encode())';
        Resources.FollowUpReport ='@Html.Raw(Localizer["FollowUpReport"].Encode())';
        Resources.Link ='@Html.Raw(Localizer["Link"].Encode())';
        Resources.Basket ='@Html.Raw(Localizer["Basket"].Encode())';
        Resources.CannotExportUnsignedDocument ='@Html.Raw(Localizer["CannotExportUnsignedDocument"].Encode())';
        Resources.Accepted ='@Html.Raw(Localizer["Accepted"].Encode())';
        Resources.Rejected ='@Html.Raw(Localizer["Rejected"].Encode())';
        Resources.SignAndExport ='@Html.Raw(Localizer["SignAndExport"].Encode())';
        Resources.SignOperations ='@Html.Raw(Localizer["SignOperations"].Encode())';
        Resources.ExportOptions ='@Html.Raw(Localizer["ExportOptions"].Encode())';
        Resources.SignAndProceed ='@Html.Raw(Localizer["SignAndProceed"].Encode())';
        Resources.MenuSearch ='@Html.Raw(Localizer["MenuSearch"].Encode())';
        Resources.NewPostponedTransferDueDate ='@Html.Raw(Localizer["NewPostponedTransferDueDate"].Encode())';
        Resources.TypeAndHitEnter ='@Html.Raw(Localizer["TypeAndHitEnter"].Encode())';
        Resources.RecallReason ='@Html.Raw(Localizer["RecallReason"].Encode())';
        Resources.TransferTitle ='@Html.Raw(Localizer["TransferTitle"].Encode())';
        Resources.Created ='@Html.Raw(Localizer["Created"].Encode())';
        Resources.CompletionInstructions ='@Html.Raw(Localizer["CompletionInstructions"].Encode())';
        Resources.OriginalExported ='@Html.Raw(Localizer["OriginalExported"].Encode())';
        Resources.CcedExported ='@Html.Raw(Localizer["CcedExported"].Encode())';
        
        Resources.reportExportedDocuments ='@Html.Raw(Localizer["reportExportedDocuments"].Encode())';
        Resources.reportRejectedDocuments ='@Html.Raw(Localizer["reportRejectedDocuments"].Encode())';
        Resources.ExportedDocuments ='@Html.Raw(Localizer["ExportedDocuments"].Encode())';
        Resources.RejectedDocuments ='@Html.Raw(Localizer["RejectedDocuments"].Encode())';
        Resources.IsExported ='@Html.Raw(Localizer["IsExported"].Encode())';
        Resources.Dismiss ='@Html.Raw(Localizer["Dismiss"].Encode())';
        Resources.Resend ='@Html.Raw(Localizer["Resend"].Encode())';
        Resources.ResendConfirmation ='@Html.Raw(Localizer["ResendConfirmation"].Encode())';
        Resources.TransferButton ='@Html.Raw(Localizer["TransferButton"].Encode())';
        Resources.ReturnReason ='@Html.Raw(Localizer["ReturnReason"].Encode())';
        Resources.EditAfterSign ='@Html.Raw(Localizer["EditAfterSign"].Encode())';
        Resources.AddedBy ='@Html.Raw(Localizer["AddedBy"].Encode())';
        Resources.Loading ='@Html.Raw(Localizer["Loading"].Encode())';
        Resources.Profile ='@Html.Raw(Localizer["Profile"].Encode())';
        Resources.FullName ='@Html.Raw(Localizer["FullName"].Encode())';
        Resources.UserName ='@Html.Raw(Localizer["UserName"].Encode())';
        Resources.DefaultStructure ='@Html.Raw(Localizer["DefaultStructure"].Encode())';
        Resources.DefaultStructureRole ='@Html.Raw(Localizer["DefaultStructureRole"].Encode())';
        Resources.DefaultSignature ='@Html.Raw(Localizer["DefaultSignature"].Encode())';
        Resources.LargerTextSize ='@Html.Raw(Localizer["LargerTextSize"].Encode())';
        Resources.ResetTextSize ='@Html.Raw(Localizer["ResetTextSize"].Encode())';
        Resources.SmallerTextSize ='@Html.Raw(Localizer["SmallerTextSize"].Encode())';
        Resources.Thisfieldvalueisrequired ='@Html.Raw(Localizer["Thisfieldvalueisrequired"].Encode())';
        Resources.Teams ='@Html.Raw(Localizer["Teams"].Encode())';
        Resources.New ='@Html.Raw(Localizer["New"].Encode())';
        Resources.Cancel ='@Html.Raw(Localizer["Cancel"].Encode())';
        Resources.Submit ='@Html.Raw(Localizer["Submit"].Encode())';
        Resources.Instruction = '@Html.Raw(Localizer["Instruction"].Encode())';
        Resources.Team = '@Html.Raw(Localizer["Team"].Encode())';
        Resources.PostponeToDate = '@Html.Raw(Localizer["PostponeToDate"].Encode())';
        Resources.PostponeToPeriod = '@Html.Raw(Localizer["PostponeToPeriod"].Encode())';
        Resources.Week = '@Html.Raw(Localizer["Week"].Encode())';
        Resources.Name = '@Html.Raw(Localizer["Name"].Encode())';
        Resources.Location= '@Html.Raw(Localizer["Location"].Encode())';
        Resources.Dismissed ='@Html.Raw(Localizer["Dismissed"].Encode())';
        Resources.Recalled ='@Html.Raw(Localizer["Recalled"].Encode())';
        Resources.Receive ='@Html.Raw(Localizer["Receive"].Encode())';
        Resources.ApplicationName ='@Html.Raw(Localizer["ApplicationName"].Encode())';
        Resources.UnknownUser ='@Html.Raw(Localizer["UnknownUser"].Encode())';
        Resources.NoUsersAvailable ='@Html.Raw(Localizer["NoUsersAvailable"].Encode())';
        Resources.DeleteInstructionMsg ='@Html.Raw(Localizer["DeleteInstructionMsg"].Encode())';
        Resources.ResendOptions ='@Html.Raw(Localizer["ResendOptions"].Encode())';
        Resources.DismissConfirmation ='@Html.Raw(Localizer["DismissConfirmation"].Encode())';
        
        Resources.Recipient='@Html.Raw(Localizer["Recipient"].Encode())';
        Resources.AtLeastOneRecipient='@Html.Raw(Localizer["AtLeastOneRecipient"].Encode())';
        Resources.FollowUpPanelReport = '@Html.Raw(Localizer["FollowUpPanelReport"].Encode())';
        Resources.ExportedDate = '@Html.Raw(Localizer["ExportedDate"].Encode())';
        Resources.DefaultApplicationLanguage = '@Html.Raw(Localizer["DefaultApplicationLanguage"].Encode())';
        Resources.ReceiveAndTransfer ='@Html.Raw(Localizer["ReceiveAndTransfer"].Encode())';
        Resources.BookmarksDosenotExist ='@Html.Raw(Localizer["BookmarksDosenotExist"].Encode())';
        Resources.GenerateSecureId ='@Html.Raw(Localizer["GenerateSecureId"].Encode())';
        Resources.TransferNumberOfDays ='@Html.Raw(Localizer["TransferNumberOfDays"].Encode())';
        Resources.ExportedNumberOfDays ='@Html.Raw(Localizer["ExportedNumberOfDays"].Encode())';

        Resources.SecureIdUnique ='@Html.Raw(Localizer["SecureIdUnique"].Encode())';

        Resources.AllowRowSelection ='@Html.Raw(Localizer["AllowRowSelection"].Encode())';
        Resources.ExportedDate ='@Html.Raw(Localizer["ExportedDate"].Encode())';

        Resources.UserHasLowerPrivacy='@Html.Raw(Localizer["UserHasLowerPrivacy"].Encode())';

        Resources.CompletionNotes = '@Html.Raw(Localizer["CompletionNotes"].Encode())';
        Resources.ExportedUser = '@Html.Raw(Localizer["ExportedUser"].Encode())';

        Resources.Mandatory = '@Html.Raw(Localizer["Mandatory"].Encode())';
        Resources.CopiedAttachments = '@Html.Raw(Localizer["CopiedAttachments"].Encode())';
        Resources.AllowTransferToMe = '@Html.Raw(Localizer["AllowTransferToMe"].Encode())';

        Resources.Subject = '@Html.Raw(Localizer["Subject"].Encode())';
        Resources.CannotRecalledWarning = '@Html.Raw(Localizer["CannotRecalledWarning"].Encode())';
        Resources.CannotResentWarning = '@Html.Raw(Localizer["CannotResentWarning"].Encode())';
        Resources.CannotDismissedWarning = '@Html.Raw(Localizer["CannotDismissedWarning"].Encode())';
        Resources.SigningDocument = '@Html.Raw(Localizer["SigningDocument"].Encode())';
        Resources.SigningSuccess = '@Html.Raw(Localizer["SigningSuccess"].Encode())';
        Resources.SigningFailed = '@Html.Raw(Localizer["SigningFailed"].Encode())';
        Resources.ListSecureUsers = '@Html.Raw(Localizer["ListSecureUsers"].Encode())';
        Resources.SecureUserId = '@Html.Raw(Localizer["SecureUserId"].Encode())';
        Resources.Reminder = '@Html.Raw(Localizer["Reminder"].Encode())';
        Resources.SignedBy = '@Html.Raw(Localizer["SignedBy"].Encode())';
        Resources.StructureType = '@Html.Raw(Localizer["StructureType"].Encode())';
        Resources.PDFPreview='@Html.Raw(Localizer["PDFPreview"].Encode())';
        Resources.AddNonArchivedAttachment='@Html.Raw(Localizer["AddNonArchivedAttachment"].Encode())';
        Resources.DeleteNonArchivedAttachment='@Html.Raw(Localizer["DeleteNonArchivedAttachment"].Encode())';
        Resources.EditNonArchivedAttachment='@Html.Raw(Localizer["EditNonArchivedAttachment"].Encode())';
        Resources.Revert='@Html.Raw(Localizer["Revert"].Encode())';
        Resources.NodeColumns='@Html.Raw(Localizer["NodeColumns"].Encode())';
        Resources.Linkeddocument='@Html.Raw(Localizer["Linkeddocument"].Encode())';
        Resources.versionnumber='@Html.Raw(Localizer["versionnumber"].Encode())';
        Resources.DeletedBy='@Html.Raw(Localizer["DeletedBy"].Encode())';
        Resources.Testcheckoutbeforecheckaccesscondition='@Html.Raw(Localizer["Testcheckoutbeforecheckaccesscondition"].Encode())';
        Resources.notnull='@Html.Raw(Localizer["notnull"].Encode())';
        Resources.null='@Html.Raw(Localizer["null="].Encode())';
        Resources.notransfercloseddate='@Html.Raw(Localizer["notransfercloseddate"].Encode())';
        Resources.transferhascloseddate='@Html.Raw(Localizer["transferhascloseddate"].Encode())';
        Resources.transfertouser='@Html.Raw(Localizer["transfertouser"].Encode())';
        Resources.nottransfertouser='@Html.Raw(Localizer["nottransfertouser"].Encode())';
        Resources.structureIdscontainstoStructure='@Html.Raw(Localizer["structureIdscontainstoStructure"].Encode())';
        Resources.structureidsdoesntcontaintoStructure='@Html.Raw(Localizer["structureidsdoesntcontaintoStructure"].Encode())';
        Resources.Transferdtouser='@Html.Raw(Localizer["Transferdtouser"].Encode())';
        Resources.TransferdtoStructure='@Html.Raw(Localizer["TransferdtoStructure"].Encode())';
        Resources.ReturnedDate='@Html.Raw(Localizer["ReturnedDate"].Encode())';
        Resources.AcceptRequestConfirmation ='@Html.Raw(Localizer["AcceptRequestConfirmation"].Encode())';
        Resources.Override='@Html.Raw(Localizer["Override"].Encode())';
        Resources.Append ='@Html.Raw(Localizer["Append"].Encode())';
        Resources.AcceptRequestConfirmation ='@Html.Raw(Localizer["AcceptRequestConfirmation"].Encode())';
        Resources.RegisterBy ='@Html.Raw(Localizer["RegisterBy"].Encode())';
        Resources.PostponeTransfer ='@Html.Raw(Localizer["PostponeTransfer"].Encode())';
        Resources.Exported ='@Html.Raw(Localizer["Exported"].Encode())';
        Resources.DeleteFollowUpDocument ='@Html.Raw(Localizer["DeleteFollowUpDocument"].Encode())';
        Resources.CantRejectNotPending ='@Html.Raw(Localizer["CantRejectNotPending"].Encode())';
        Resources.CantAcceptNotPending ='@Html.Raw(Localizer["CantAcceptNotPending"].Encode())';
        Resources.RequestNotFound =  '@Html.Raw(Localizer["RequestNotFound"].Encode())';
       Resources.Unread = '@Html.Raw(Localizer["Unread"].Encode())';
       Resources.Unarchive = '@Html.Raw(Localizer["Unarchive"].Encode())';
      Resources.OtherActions = '@Html.Raw(Localizer["OtherActions"].Encode())';
      Resources.dosenothaveaccessonthiscategory = '@Html.Raw(Localizer["dosenothaveaccessonthiscategory"].Encode())';
      Resources.And = '@Html.Raw(Localizer["And"].Encode())';
      Resources.SecurityNotAppliedYouAreNotTheCreatorOn = '@Html.Raw(Localizer["SecurityNotAppliedYouAreNotTheCreatorOn"].Encode())';
      Resources.SearchRoles = '@Html.Raw(Localizer["SearchRoles"].Encode())';
      Resources.EditAfterExport = '@Html.Raw(Localizer["EditAfterExport"].Encode())';
      Resources.ByStructure = '@Html.Raw(Localizer["ByStructure"].Encode())';
      Resources.ThereIsAnotherTransferPending = '@Html.Raw(Localizer["ThereIsAnotherTransferPending"].Encode())';

      Resources.ExportLinkedDocuments='@Html.Raw(Localizer["ExportLinkedDocuments"].Encode())';
      Resources.Attachments='@Html.Raw(Localizer["Attachments"].Encode())';
      Resources.ThereAreExternalStructureDoYouWantToContinueExporting='@Html.Raw(Localizer["ThereAreExternalStructureDoYouWantToContinueExporting"].Encode())';
      Resources.UsePrivacyColorInBookmark='@Html.Raw(Localizer["UsePrivacyColorInBookmark"].Encode())';
      Resources.TeamAlreadyExist='@Html.Raw(Localizer["TeamAlreadyExist"].Encode())';
      Resources.ExportingDocument='@Html.Raw(Localizer["ExportingDocument"].Encode())';
      Resources.DeleteTeamConfirmation='@Html.Raw(Localizer["DeleteTeamConfirmation"].Encode())';
      Resources.IsSigned  = '@Html.Raw(Localizer["IsSigned"].Encode())';

      Resources.NoCertificate='@Html.Raw(Localizer["NoCertificate"].Encode())';
      Resources.CertificateNotActivated='@Html.Raw(Localizer["CertificateNotActivated"].Encode())';
      Resources.CertificateExpired='@Html.Raw(Localizer["CertificateExpired"].Encode())';
      Resources.FailedToGetCertificate='@Html.Raw(Localizer["FailedToGetCertificate"].Encode())';


        Resources.G2GRefNo = '@Html.Raw(Localizer["G2GRefNo"].Encode())';
        Resources.Queued = '@Html.Raw(Localizer["Queued"].Encode())';
        Resources.IncomingRecalled = '@Html.Raw(Localizer["IncomingRecalled"].Encode())';
        Resources.IncomingRejected = '@Html.Raw(Localizer["IncomingRejected"].Encode())';
        Resources.SentFromStructure = '@Html.Raw(Localizer["SentFromStructure"].Encode())';
        Resources.ReceiveOrReject = '@Html.Raw(Localizer["ReceiveOrReject"].Encode())';
        Resources.PendingToReceive = '@Html.Raw(Localizer["PendingToReceive"].Encode())';
        Resources.StopSending = '@Html.Raw(Localizer["StopSending"].Encode())';
        Resources.Reject = '@Html.Raw(Localizer["Reject"].Encode())';
        Resources.Fetch = '@Html.Raw(Localizer["Fetch"].Encode())';    
        Resources.DocumentNotRegistered = '@Html.Raw(Localizer["DocumentNotRegistered"].Encode())';
        Resources.GovernmentCorrespondence = '@Html.Raw(Localizer["GovernmentCorrespondence"].Encode())';    
        Resources.ExportToNonGov = '@Html.Raw(Localizer["ExportToNonGov"].Encode())';    
        
        Resources.resources_UnderProcess = '@Html.Raw(Localizer["resources_UnderProcess"].Encode())';
        Resources.resources_AnErrorOccured = '@Html.Raw(Localizer["resources_AnErrorOccured"].Encode())';
        Resources.resources_DocumentSent = '@Html.Raw(Localizer["resources_DocumentSent"].Encode())';
        Resources.resources_NoMappedEntitiesForSender = '@Html.Raw(Localizer["resources_NoMappedEntitiesForSender"].Encode())';
        Resources.resources_DocumentNotRegistered = '@Html.Raw(Localizer["resources_DocumentNotRegistered"].Encode())';
        Resources.resources_NoRecipients = '@Html.Raw(Localizer["resources_NoRecipients"].Encode())';
        Resources.resources_NoOriginalMail = '@Html.Raw(Localizer["resources_NoOriginalMail"].Encode())';
        Resources.Yes = '@Html.Raw(Localizer["Yes"].Encode())';
        Resources.No = '@Html.Raw(Localizer["No"].Encode())';

        Resources.CannotBeRecalled='@Html.Raw(Localizer["CannotBeRecalled"].Encode())';
        Resources.CannotRecallCorrespondenceWithSubject='@Html.Raw(Localizer["CannotRecallCorrespondenceWithSubject"].Encode())';
        Resources.FavoriteStructureAddedSuccessfully = '@Html.Raw(Localizer["FavoriteStructureAddedSuccessfully"].Encode())';
        Resources.UnexpectedErrorOccurred = '@Html.Raw(Localizer["UnexpectedErrorOccurred"].Encode())';
        Resources.CannotBeRecalled='@Html.Raw(Localizer["CannotBeRecalled"].Encode())';

        Resources.UserNodes='@Html.Raw(Localizer["UserNodes"].Encode())';
        Resources.DeleteEventConfirmation = '@Html.Raw(Localizer["DeleteEventConfirmation"].Encode())';


        Resources.PersonalInbox = '@Html.Raw(Localizer["PersonalInbox"].Encode())';
        Resources.InternalPersonalInbox = '@Html.Raw(Localizer["InternalPersonalInbox"].Encode())';
        Resources.IncomingPersonalInbox = '@Html.Raw(Localizer["IncomingPersonalInbox"].Encode())';
        Resources.OutgoingPersonalInbox = '@Html.Raw(Localizer["OutgoingPersonalInbox"].Encode())';

        Resources.ReadyForTransferReport = '@Html.Raw(Localizer["ReadyForTransferReport"].Encode())';
        Resources.ReadyToExportReport = '@Html.Raw(Localizer["ReadyToExportReport"].Encode())';
        Resources.StructureReadyForExport = '@Html.Raw(Localizer["StructureReadyForExport"].Encode())';

        Resources.DepartmentInbox = '@Html.Raw(Localizer["DepartmentInbox"].Encode())';
        Resources.IncomingDepartmentInbox = '@Html.Raw(Localizer["IncomingDepartmentInbox"].Encode())';
        Resources.OutgoingDepartmentInbox = '@Html.Raw(Localizer["OutgoingDepartmentInbox"].Encode())';
        Resources.InternalDepartmentInbox = '@Html.Raw(Localizer["InternalDepartmentInbox"].Encode())';

        Resources.SentOutgoing = '@Html.Raw(Localizer["SentOutgoing"].Encode())';
        Resources.SentInternal = '@Html.Raw(Localizer["SentInternal"].Encode())';
        Resources.SentIncoming = '@Html.Raw(Localizer["SentIncoming"].Encode())';

        Resources.AcceptReject = '@Html.Raw(Localizer["AcceptReject"].Encode())';
        Resources.ArabicCharactersNotAllowed = '@Html.Raw(Localizer["ArabicCharactersNotAllowed"].Encode())';
        Resources.DeleteEventConfirmation = '@Html.Raw(Localizer["DeleteEventConfirmation"].Encode())';
        Resources.GenerateBarcode = '@Html.Raw(Localizer["GenerateBarcode"].Encode())';

        Resources.CorrespondenceIsSigned = '@Html.Raw(Localizer["CorrespondenceIsSigned"].Encode())';
        Resources.CorrespondenceIsLocked = '@Html.Raw(Localizer["CorrespondenceIsLocked"].Encode())';

        Resources.TransferOpenedByUser = '@Html.Raw(Localizer["TransferOpenedByUser"].Encode())';